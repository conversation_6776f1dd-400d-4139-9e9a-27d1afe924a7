/*
 * <AUTHOR> mark
 * @Date         : 2020-06-19
 * @copyleft Apache 2.0
 * 
 * SqlConnPool MySQL数据库连接池实现
 * 
 * What (是什么)：
 * 高性能MySQL数据库连接池，支持预创建连接、信号量控制、线程安全、连接复用
 * 
 * Why (为什么)：
 * 1. 避免频繁创建/销毁数据库连接的开销
 * 2. 控制并发数据库连接数量，保护数据库服务器
 * 3. 提供线程安全的连接获取和释放机制
 * 4. 优化数据库访问性能，减少延迟
 * 5. 实现连接资源的统一管理和监控
 * 
 * How (怎么做)：
 * 1. 单例模式确保全局唯一连接池
 * 2. 信号量控制可用连接数量
 * 3. 队列管理空闲连接的分配和回收
 * 4. 互斥锁保护连接队列的线程安全
 * 5. 预创建指定数量的数据库连接
 */ 

#include "sqlconnpool.h"
using namespace std;

// ==================== 单例模式实现 ====================

/**
 * @brief 获取数据库连接池单例实例
 * @return SqlConnPool* 连接池的唯一实例
 * 
 * What: 线程安全的单例模式实现
 * Why:  确保整个应用程序只有一个数据库连接池，避免资源竞争
 * How:  使用函数内静态变量，C++11保证线程安全的初始化
 */
/**
 * @brief 获取数据库连接池的唯一实例（单例模式）
 * @return SqlConnPool* 返回连接池对象的指针
 * 
 * 主人，让我详细解释这个单例模式的实现：
 * 
 * 什么是单例模式？
 * 就像一个城市只能有一个市长一样，整个程序只能有一个数据库连接池。
 * 
 * 为什么要用单例？
 * 1. 避免创建多个连接池造成资源浪费
 * 2. 确保所有地方使用的都是同一个连接池
 * 3. 统一管理数据库连接，避免混乱
 * 
 * 这里的技术细节：
 * 1. static SqlConnPool connPool - 这是关键！
 *    - static关键字让这个变量只创建一次
 *    - 第一次调用时创建，之后永远返回同一个对象
 *    - 程序结束时自动销毁
 * 
 * 2. 为什么是线程安全的？
 *    - C++11标准保证：函数内static变量的初始化是线程安全的
 *    - 多个线程同时调用时，只有一个线程会执行初始化
 *    - 其他线程会等待初始化完成
 * 
 * 3. 懒汉式 vs 饿汉式：
 *    - 懒汉式：需要时才创建（这里用的就是懒汉式）
 *    - 饿汉式：程序启动时就创建
 *    - 懒汉式更节省资源，只有真正需要时才创建
 */
SqlConnPool* SqlConnPool::Instance() {
    static SqlConnPool connPool;    // 函数内静态变量，线程安全的懒汉式单例
    return &connPool;               // 返回这个唯一实例的地址
}

// ==================== 连接池初始化 ====================

/**
 * @brief 初始化数据库连接池
 * @param host 数据库服务器地址
 * @param port 数据库服务器端口
 * @param user 数据库用户名
 * @param pwd 数据库密码
 * @param dbName 数据库名称
 * @param connSize 连接池大小
 * 
 * What: 配置数据库参数并预创建指定数量的连接
 * Why:  预创建连接避免运行时的连接建立延迟，提高响应速度
 * How:  
 * 1. 保存数据库连接参数
 * 2. 初始化信号量和队列
 * 3. 循环创建指定数量的MySQL连接
 * 4. 将连接存储到空闲连接队列中
 */
void SqlConnPool::Init(const char* host, int port,
              const char* user,const char* pwd, const char* dbName,
              int connSize = 10) {
    assert(connSize > 0);           // 确保连接池大小有效
    
    // ==================== 保存数据库配置 ====================
    
    host_ = host;                   // 数据库服务器地址
    port_ = port;                   // 数据库端口号
    user_ = user;                   // 数据库用户名
    pwd_ = pwd;                     // 数据库密码
    dbName_ = dbName;               // 数据库名称
    
    // ==================== 初始化同步原语 ====================
    
    MAX_CONN_ = connSize;           // 设置最大连接数
    
    // 初始化信号量，用于控制可用连接数量
    // 初始化信号量，用于控制可用连接数量
    // sem_init(信号量指针, 是否进程间共享, 初始值)
    // 参数详解：
    // - &semId_: 信号量对象的地址
    // - 0: 表示只在当前进程的线程间共享（不跨进程）
    // - MAX_CONN_: 初始值设为最大连接数，表示有MAX_CONN_个可用资源
    //
    // 工作原理（就像停车场管理）：
    // 1. 把信号量想象成停车场的车位计数器
    // 2. MAX_CONN_就是总车位数，比如有10个车位
    // 3. 每当有车进入（GetConn获取连接），计数器减1
    // 4. 每当有车离开（FreeConn归还连接），计数器加1
    // 5. 如果车位满了（连接用完），新来的车就要等待
    sem_init(&semId_, 0, MAX_CONN_);
    
    // ==================== 预创建数据库连接 ====================
    
    // 循环创建指定数量的数据库连接（就像提前准备好多个电话线）
    for (int i = 0; i < MAX_CONN_; i++) {
        MYSQL *sql = nullptr;       // 声明一个MySQL连接指针，初始为空
        
        // ==================== 第一步：初始化MySQL连接对象 ====================
        // mysql_init()的作用：
        // 1. 分配内存空间给MYSQL结构体
        // 2. 初始化连接的各种参数和状态
        // 3. 返回一个可以使用的MYSQL连接句柄
        // 就像买了一部新手机，需要先开机初始化才能使用
        sql = mysql_init(sql);
        if (!sql) {
            LOG_ERROR("MySql init error!");
            assert(sql);            // 如果初始化失败，程序直接终止
        }
        
        // ==================== 第二步：建立到数据库服务器的实际连接 ====================
        // mysql_real_connect()的作用：
        // 1. 使用提供的参数（主机地址、用户名、密码等）连接到MySQL服务器
        // 2. 验证用户身份和权限
        // 3. 选择指定的数据库
        // 就像用手机拨打电话，需要输入正确的号码才能接通
        //
        // 参数详解：
        // - sql: 刚才初始化好的连接句柄
        // - host: 数据库服务器的IP地址（比如"localhost"或"*************"）
        // - user: 数据库用户名（比如"root"）
        // - pwd: 数据库密码
        // - dbName: 要连接的数据库名称（比如"webserver_db"）
        // - port: 数据库服务器端口号（MySQL默认是3306）
        // - nullptr: Unix套接字文件名（我们用TCP连接，所以传空）
        // - 0: 客户端标志（使用默认设置）
        sql = mysql_real_connect(sql, host, user, pwd, dbName, port, nullptr, 0);
        if (!sql) {
            LOG_ERROR("MySql Connect error!");
            // 注意：这里连接失败但没有assert，程序会继续运行
            // 这可能会导致后续问题，实际项目中应该处理这种错误情况
        }
        
        // ==================== 第三步：将连接存储到连接池队列中 ====================
        // 把成功创建的连接放入队列，等待业务代码来获取使用
        // 就像把准备好的电话线放到一个盒子里，需要的时候就拿一根出来用
        connQue_.push(sql);
    }
}

// ==================== 连接获取接口 ====================

/**
 * @brief 从连接池获取一个数据库连接
 * @return MYSQL* 数据库连接句柄，失败返回nullptr
 * 
 * What: 线程安全地从连接池中获取一个可用的数据库连接
 * Why:  为业务代码提供统一的数据库连接获取接口
 * How:  
 * 1. 使用信号量等待可用连接（阻塞直到有连接可用）
 * 2. 加锁保护连接队列
 * 3. 从队列头部取出一个连接
 * 4. 返回连接给调用者
 */
MYSQL* SqlConnPool::GetConn() {
    MYSQL *sql = nullptr;
    
    if(connQue_.empty()){
        LOG_WARN("SqlConnPool busy!");  // 连接池繁忙警告
        return nullptr;
    }
    
    // ==================== 信号量等待 ====================
    
    // P操作：等待信号量，如果没有可用连接则阻塞
    sem_wait(&semId_);
    
    // ==================== 线程安全地获取连接 ====================
    
    {
        lock_guard<mutex> locker(mtx_);     // RAII方式加锁
        sql = connQue_.front();             // 获取队列头部连接
        connQue_.pop();                     // 从队列中移除该连接
    }
    
    return sql;     // 返回数据库连接
}

// ==================== 连接释放接口 ====================

/**
 * @brief 将数据库连接归还给连接池
 * @param sql 要归还的数据库连接
 * 
 * What: 线程安全地将使用完的数据库连接放回连接池
 * Why:  实现连接的复用，避免重复创建和销毁连接
 * How:  
 * 1. 验证连接有效性
 * 2. 加锁保护连接队列
 * 3. 将连接加入队列尾部
 * 4. 发送信号量通知等待的线程
 */
void SqlConnPool::FreeConn(MYSQL* sql) {
    assert(sql);                    // 确保连接指针有效
    
    // ==================== 线程安全地归还连接 ====================
    
    {
        lock_guard<mutex> locker(mtx_);     // RAII方式加锁
        connQue_.push(sql);                 // 将连接放回队列尾部
    }
    
    // ==================== 信号量通知 ====================
    
    // V操作：增加信号量，通知等待中的线程有连接可用
    sem_post(&semId_);
}

// ==================== 连接池状态查询 ====================

/**
 * @brief 获取连接池中空闲连接的数量
 * @return int 当前空闲连接数
 * 
 * What: 查询连接池当前的空闲连接数量
 * Why:  用于监控连接池状态，便于性能调优和问题诊断
 * How:  线程安全地返回队列大小
 */
int SqlConnPool::GetFreeConnCount() {
    lock_guard<mutex> locker(mtx_);
    return connQue_.size();         // 返回队列中的连接数量
}

// ==================== 连接池销毁 ====================

/**
 * @brief 关闭连接池并释放所有资源
 * 
 * What: 清理连接池的所有资源，关闭所有数据库连接
 * Why:  程序结束时需要正确释放数据库连接资源
 * How:  
 * 1. 加锁保护队列访问
 * 2. 逐个关闭队列中的所有连接
 * 3. 清空连接队列
 * 4. 销毁信号量
 */
void SqlConnPool::ClosePool() {
    lock_guard<mutex> locker(mtx_);     // 保护队列访问
    
    // ==================== 关闭所有数据库连接 ====================
    
    while(!connQue_.empty()) {
        auto item = connQue_.front();   // 获取队列头部连接
        connQue_.pop();                 // 从队列中移除
        mysql_close(item);              // 关闭MySQL连接
    }
    
    // ==================== 清理同步原语 ====================
    
    mysql_library_end();               // 清理MySQL库资源
    sem_destroy(&semId_);               // 销毁信号量
}

// ==================== 构造与析构函数 ====================

/**
 * @brief 默认构造函数
 * What: 初始化连接池对象为默认状态
 * Why:  为单例模式提供支持，延迟初始化连接池
 * How:  设置默认的无效状态值
 */
SqlConnPool::SqlConnPool() {
    useCount_ = 0;                  // 初始化使用计数
    freeCount_ = 0;                 // 初始化空闲计数
}

/**
 * @brief 析构函数
 * What: 清理连接池资源
 * Why:  RAII原则，确保程序结束时正确释放资源
 * How:  调用ClosePool()关闭所有连接
 */
SqlConnPool::~SqlConnPool() {
    ClosePool();
}
