/*
 * <AUTHOR> mark
 * @Date         : 2020-06-17
 * @copyleft Apache 2.0
 * 
 * WebServer 高性能Web服务器实现
 * 核心功能：
 * 1. 基于epoll的事件驱动架构
 * 2. 多线程处理业务逻辑
 * 3. 连接池和定时器管理
 * 4. HTTP协议处理
 * 5. 异步日志记录
 */

#include "webserver.h"

using namespace std;

/**
 * @brief WebServer构造函数
 * @param port 监听端口
 * @param trigMode 触发模式：0-LT+LT, 1-LT+ET, 2-ET+LT, 3-ET+ET
 * @param timeoutMS 连接超时时间（毫秒）
 * @param OptLinger 是否启用SO_LINGER选项
 * @param sqlPort 数据库端口
 * @param sqlUser 数据库用户名
 * @param sqlPwd 数据库密码
 * @param dbName 数据库名
 * @param connPoolNum 数据库连接池大小
 * @param threadNum 线程池大小
 * @param openLog 是否开启日志
 * @param logLevel 日志级别
 * @param logQueSize 日志队列大小
 * 
 * 初始化列表说明：
 * - 使用初始化列表提高效率，避免默认构造+赋值
 * - 智能指针管理资源，自动释放内存
 * - 原子操作保证线程安全
 */
WebServer::WebServer(
            int port, int trigMode, int timeoutMS, bool OptLinger,
            int sqlPort, const char* sqlUser, const  char* sqlPwd,
            const char* dbName, int connPoolNum, int threadNum,
            bool openLog, int logLevel, int logQueSize):
            port_(port), openLinger_(OptLinger), timeoutMS_(timeoutMS), isClose_(false),
            timer_(new HeapTimer()), threadpool_(new ThreadPool(threadNum)), epoller_(new Epoller())
    {
    // ==================== 设置静态资源目录 ====================
    
    // 获取当前工作目录
    /*代码功能解析
这段代码的作用是获取当前程序运行的工作目录，用来设置Web服务器的静态资源路径。
逐行分析
getcwd() 是C标准库函数，作用是获取当前工作目录的绝对路径
nullptr 表示让系统自动分配内存来存储路径
256 是建议的缓冲区大小（字节）
返回值是一个指向路径字符串的指针，赋值给 srcDir_ 成员变量
简单比喻：就像问系统"我现在在哪个文件夹里？"，系统回答一个完整的路径，比如 /home/<USER>/myproject
这是一个编译器特定的宏，用来确保 getcwd() 调用成功。如果获取目录失败，会进行错误处理。
为什么需要获取工作目录？
看后面的代码：
目的：Web服务器需要知道静态文件（如HTML、CSS、图片）存放在哪里，这样才能正确响应客户端的文件请求。
举例：如果程序在 /home/<USER>/home/<USER>/resources/，这样服务器就知道去这个目录找网页文件了。
*/
    srcDir_ = getcwd(nullptr, 256);
    __DEFINE_CPP_OVERLOAD_STANDARD_NFUNC_0_2_EX(srcDir_);  // 确保获取成功

    // 拼接资源目录路径：当前目录 + "/resources/"
    strncat(srcDir_, "/resources/", 16);
    
    // 初始化HTTP连接的静态成员
    HttpConn::userCount = 0;        // 连接计数器清零
    HttpConn::srcDir = srcDir_;     // 设置静态资源目录

    // ==================== 初始化数据库连接池 ====================
    
    // 使用单例模式初始化SQL连接池
    // 这里硬编码了数据库IP，实际项目中应该从配置文件读取
    SqlConnPool::Instance()->Init("*************", sqlPort, sqlUser, sqlPwd, dbName, connPoolNum);

    // ==================== 初始化事件模式和Socket ====================
    
    InitEventMode_(trigMode);       // 设置epoll触发模式
    if(!InitSocket_()) {            // 初始化监听socket
        isClose_ = true;            // 初始化失败，标记为关闭状态
    }

    // ==================== 初始化日志系统 ====================
    /*
核心功能
这段代码是在初始化服务器的日志系统，并记录服务器启动时的关键配置信息。
逐步解析
1. 条件判断
只有当 openLog 参数为 true 时才启用日志系统
这是个开关，让用户可以选择是否需要日志功能
2. 初始化日志系统
Log::Instance() - 获取日志系统的单例对象（全程序只有一个日志管理器）
init() 参数含义：
logLevel - 日志级别（如：调试、信息、警告、错误）
"./log" - 日志文件存放目录
".log" - 日志文件后缀名
logQueSize - 异步日志队列大小
3. 检查服务器状态并记录
如果服务器初始化失败（isClose_ 为 true），记录错误日志
4. 记录服务器配置信息
生活比喻
就像开店前在记录本上写下营业信息：
店铺地址（端口号）
营业模式（ET/LT触发模式）
员工数量（线程池大小）
仓库容量（数据库连接池大小）
这样出问题时可以查看记录，知道当时是怎么配置的*/
    if(openLog) {
        // 初始化异步日志系统
        Log::Instance()->init(logLevel, "./log", ".log", logQueSize);
        
        if(isClose_) { 
            LOG_ERROR("========== Server init error!=========="); 
        }
        else {
            // 记录服务器启动信息
            LOG_INFO("========== Server init ==========");
            LOG_INFO("Port:%d, OpenLinger: %s", port_, OptLinger? "true":"false");
            LOG_INFO("Listen Mode: %s, OpenConn Mode: %s",
                            (listenEvent_ & EPOLLET ? "ET": "LT"),
                            (connEvent_ & EPOLLET ? "ET": "LT"));
            LOG_INFO("LogSys level: %d", logLevel);
            LOG_INFO("srcDir: %s", HttpConn::srcDir);
            LOG_INFO("SqlConnPool num: %d, ThreadPool num: %d", connPoolNum, threadNum);
        }
    }
}

/**
 * @brief WebServer析构函数
 * 
 * RAII资源管理：
 * 1. 关闭监听socket
 * 2. 设置关闭标志
 * 3. 释放资源目录内存
 * 4. 关闭数据库连接池
 * 5. 智能指针自动释放其他资源
 */
WebServer::~WebServer() {
    close(listenFd_);                           // 关闭监听socket
    isClose_ = true;                            // 设置关闭标志
    free(srcDir_);                              // 释放目录字符串内存
    SqlConnPool::Instance()->ClosePool();       // 关闭数据库连接池
}

/**
 * @brief 初始化事件模式
 * @param trigMode 触发模式配置
 * 
 * 触发模式说明：
 * - EPOLLRDHUP: 检测对端关闭连接
 * - EPOLLONESHOT: 确保一个socket在任意时刻只被一个线程处理
 * - EPOLLET: 边缘触发模式，高性能但需要一次性处理完所有数据
 * 
 * 模式组合：
 * 0: LT + LT (水平触发，兼容性好)
 * 1: LT + ET (监听LT，连接ET)
 * 2: ET + LT (监听ET，连接LT)
 * 3: ET + ET (全ET，最高性能)
 *两步初始化的原因
第一步：设置基础必需的事件标志
第二步：根据配置追加可选的事件标志
为什么这样设计
基础功能保证：无论选择什么模式，都必须有EPOLLRDHUP和EPOLLONESHOT
可选功能叠加：ET模式是额外的性能优化，可选择性添加
代码清晰：先设置必需的，再根据用户选择添加可选的
简单比喻
就像穿衣服：
先穿基础内衣（必需的事件标志）
再根据天气选择是否加外套（ET模式标志）
这样既保证了基本功能，又提供了灵活的性能配置选择！
 */
void WebServer::InitEventMode_(int trigMode) {
    // 监听socket事件：检测对端关闭
    listenEvent_ = EPOLLRDHUP;
    
    // 连接socket事件：一次性处理 + 检测对端关闭
    connEvent_ = EPOLLONESHOT | EPOLLRDHUP;
    
    switch (trigMode)
    {
    case 0:
        // 默认LT模式，无需额外设置
        break;
    case 1:
        // 连接使用ET模式
        connEvent_ |= EPOLLET;
        break;
    case 2:
        // 监听使用ET模式
        listenEvent_ |= EPOLLET;
        break;
    case 3:
        // 全部使用ET模式（推荐）
        listenEvent_ |= EPOLLET;
        connEvent_ |= EPOLLET;
        break;
    default:
        // 默认使用全ET模式
        listenEvent_ |= EPOLLET;
        connEvent_ |= EPOLLET;
        break;
    }
    
    // 设置HttpConn的全局ET标志
    HttpConn::isET = (connEvent_ & EPOLLET);
}

/**
 * @brief 启动服务器主循环（核心函数）
 * 
 * 事件循环架构：
 * 1. 计算epoll超时时间（基于定时器）
 * 2. 等待事件发生
 * 3. 遍历所有就绪事件
 * 4. 根据事件类型分发处理
 * 5. 重复循环直到服务器关闭
 */
void WebServer::Start() {
    int timeMS = -1;  // epoll_wait超时时间，-1表示无限等待
    
    if(!isClose_) { 
        LOG_INFO("========== Server start =========="); 
    }
    
    // ==================== 主事件循环 ====================
    while(!isClose_) {
        // 1. 计算超时时间
        if(timeoutMS_ > 0) {
            timeMS = timer_->GetNextTick();  // 获取最近定时器的超时时间
        }
        
        // 2. 等待事件（核心阻塞点）
        // 等待事件发生，阻塞等待时间由timeMS决定
        // 如果timeMS为-1，则阻塞等待直到有事件发生
        // 如果timeMS为0，则立即返回，不阻塞
        // 如果timeMS大于0，则阻塞等待timeMS毫秒
        int eventCnt = epoller_->Wait(timeMS);
        
        // 3. 处理所有就绪事件
        for(int i = 0; i < eventCnt; i++) {
            // 获取事件信息
            int fd = epoller_->GetEventFd(i);       // 文件描述符
            uint32_t events = epoller_->GetEvents(i); // 事件类型
            
            // ==================== 事件分发处理 ====================
            
            if(fd == listenFd_) {
                // 新连接事件
                DealListen_();
            }
            else if(events & (EPOLLRDHUP | EPOLLHUP | EPOLLERR)) {
                // 连接异常事件（对端关闭、挂起、错误）
                assert(users_.count(fd) > 0);  // 确保连接存在
                CloseConn_(&users_[fd]);       // 关闭连接
            }
            else if(events & EPOLLIN) {
                // 可读事件（客户端发送数据）
                assert(users_.count(fd) > 0);
                DealRead_(&users_[fd]);        // 处理读事件
            }
            else if(events & EPOLLOUT) {
                // 可写事件（可以向客户端发送数据）
                assert(users_.count(fd) > 0);
                DealWrite_(&users_[fd]);       // 处理写事件
            } 
            else {
                // 未知事件类型
                LOG_ERROR("Unexpected event");
            }
        }
    }
}

/**
 * @brief 发送错误信息给客户端
 * @param fd 客户端文件描述符
 * @param info 错误信息字符串
 * 
 * 用于在服务器繁忙或出错时向客户端发送错误响应
 */
void WebServer::SendError_(int fd, const char*info) {
    assert(fd > 0);
    
    // 发送错误信息
    int ret = send(fd, info, strlen(info), 0);
    if(ret < 0) {
        LOG_WARN("send error to client[%d] error!", fd);
    }
    
    // 发送完毕后关闭连接
    close(fd);
}

/**
 * @brief 关闭客户端连接
 * @param client HTTP连接对象指针
 * 
 * 完整的连接关闭流程：
 * 1. 记录日志
 * 2. 从epoll中移除文件描述符
 * 3. 关闭socket连接
 * 4. 清理相关资源
 */
void WebServer::CloseConn_(HttpConn* client) {
    assert(client);
    
    LOG_INFO("Client[%d] quit!", client->GetFd());  // 记录连接关闭日志
    epoller_->DelFd(client->GetFd());               // 从epoll中移除
    client->Close();                                // 关闭HTTP连接
}

/**
 * @brief 添加新的客户端连接
 * @param fd 客户端socket文件描述符
 * @param addr 客户端地址信息
 * 
 * 新连接处理流程：
 * 1. 初始化HTTP连接对象
 * 2. 添加定时器（如果启用超时）
 * 3. 注册到epoll事件监听
 * 4. 设置非阻塞模式
 * 5. 记录连接日志
 */
void WebServer::AddClient_(int fd, sockaddr_in addr) {
    assert(fd > 0);
    
    // 1. 初始化HTTP连接
    users_[fd].init(fd, addr);
    
    // 2. 添加超时定时器
    if(timeoutMS_ > 0) {
        // 使用bind绑定成员函数作为超时回调
        timer_->add(fd, timeoutMS_, std::bind(&WebServer::CloseConn_, this, &users_[fd]));
    }
    
    // 3. 注册epoll事件（可读事件 + 连接事件模式）
    epoller_->AddFd(fd, EPOLLIN | connEvent_);
    
    // 4. 设置非阻塞模式
    SetFdNonblock(fd);
    
    // 5. 记录连接日志
    LOG_INFO("Client[%d] in!", users_[fd].GetFd());
}

/**
 * @brief 处理新连接事件
 * 
 * 监听socket的事件处理：
 * 1. 接受新连接
 * 2. 检查连接数限制
 * 3. 添加到连接管理
 * 4. ET模式下循环处理所有连接
 */
void WebServer::DealListen_() {
    struct sockaddr_in addr;
    socklen_t len = sizeof(addr);
    
    do {
        // 接受新连接
        int fd = accept(listenFd_, (struct sockaddr *)&addr, &len);
        
        if(fd <= 0) { 
            return;  // 没有更多连接或出错
        }
        else if(HttpConn::userCount >= MAX_FD) {
            // 连接数达到上限
            SendError_(fd, "Server busy!");
            LOG_WARN("Clients is full!");
            return;
        }
        
        // 添加新客户端
        AddClient_(fd, addr);
        
    } while(listenEvent_ & EPOLLET);  // ET模式下循环处理所有连接
}

/**
 * @brief 处理读事件
 * @param client HTTP连接对象指针
 * 
 * 读事件处理流程：
 * 1. 延长连接超时时间
 * 2. 将读任务提交给线程池异步处理
 */
void WebServer::DealRead_(HttpConn* client) {
    assert(client);
    
    ExtentTime_(client);  // 延长超时时间
    
    // 提交读任务到线程池
    threadpool_->AddTask(std::bind(&WebServer::OnRead_, this, client));
}

/**
 * @brief 处理写事件
 * @param client HTTP连接对象指针
 * 
 * 写事件处理流程：
 * 1. 延长连接超时时间
 * 2. 将写任务提交给线程池异步处理
 */
void WebServer::DealWrite_(HttpConn* client) {
    assert(client);
    
    ExtentTime_(client);  // 延长超时时间
    
    // 提交写任务到线程池
    threadpool_->AddTask(std::bind(&WebServer::OnWrite_, this, client));
}

/**
 * @brief 延长连接超时时间
 * @param client HTTP连接对象指针
 * 
 * 在有活动时重置定时器，避免活跃连接被误关闭
 */
void WebServer::ExtentTime_(HttpConn* client) {
    assert(client);
    
    if(timeoutMS_ > 0) { 
        timer_->adjust(client->GetFd(), timeoutMS_); 
    }
}

/**
 * @brief 读事件的实际处理函数（在线程池中执行）
 * @param client HTTP连接对象指针
 * 
 * 异步读处理：
 * 1. 从socket读取数据到缓冲区
 * 2. 错误处理
 * 3. 触发HTTP请求处理
 */
void WebServer::OnRead_(HttpConn* client) {
    assert(client);
    
    int ret = -1;
    int readErrno = 0;
    
    // 读取数据
    ret = client->read(&readErrno);
    
    if(ret <= 0 && readErrno != EAGAIN) {
        // 读取失败且不是EAGAIN错误（EAGAIN表示暂时无数据）
        CloseConn_(client);
        return;
    }
    
    // 处理HTTP请求
    OnProcess(client);
}

/**
 * @brief HTTP请求处理
 * @param client HTTP连接对象指针
 * 
 * 核心业务逻辑：
 * 1. 解析HTTP请求
 * 2. 生成HTTP响应
 * 3. 根据处理结果修改epoll事件
 */
void WebServer::OnProcess(HttpConn* client) {
    if(client->process()) {
        // 处理成功，有数据要发送，监听写事件
        epoller_->ModFd(client->GetFd(), connEvent_ | EPOLLOUT);
    } else {
        // 处理失败或需要更多数据，继续监听读事件
        epoller_->ModFd(client->GetFd(), connEvent_ | EPOLLIN);
    }
}

/**
 * @brief 写事件的实际处理函数（在线程池中执行）
 * @param client HTTP连接对象指针
 * 
 * 异步写处理：
 * 1. 将响应数据写入socket
 * 2. 检查是否写完
 * 3. 处理Keep-Alive连接
 */
void WebServer::OnWrite_(HttpConn* client) {
    assert(client);
    
    int ret = -1;
    int writeErrno = 0;
    
    // 写入数据
    ret = client->write(&writeErrno);
    
    if(client->ToWriteBytes() == 0) {
        // 数据传输完成
        if(client->IsKeepAlive()) {
            // Keep-Alive连接，继续处理下一个请求
            OnProcess(client);
            return;
        }
    }
    else if(ret < 0) {
        if(writeErrno == EAGAIN) {
            // 继续传输
            epoller_->ModFd(client->GetFd(), connEvent_ | EPOLLOUT);
            return;
        }
    }
    
    CloseConn_(client);
}

/**
 * @brief 初始化监听Socket
 * @return bool 初始化是否成功
 * 
 * Socket初始化流程：
 * 1. 验证端口号有效性
 * 2. 创建TCP socket
 * 3. 设置socket选项（优雅关闭、端口复用）
 * 4. 绑定地址和端口
 * 5. 开始监听
 * 6. 添加到epoll监听
 * 7. 设置非阻塞模式
 */
bool WebServer::InitSocket_() {
    int ret;
    struct sockaddr_in addr;
    
    // ==================== 1. 端口号验证 ====================
    // 检查端口号是否在有效范围内 (1024 - 65535)
    if(port_ > 65535 || port_ < 1024) {
        // 如果端口号无效，记录错误日志
        LOG_ERROR("Port:%d error!",  port_);
        // 返回false表示初始化失败
        return false;
    }
    
    // ==================== 2. 配置服务器地址 ====================
    addr.sin_family = AF_INET;                    // IPv4协议族
    addr.sin_addr.s_addr = htonl(INADDR_ANY);     // 绑定所有可用接口
    addr.sin_port = htons(port_);                 // 网络字节序端口号
    
    // ==================== 3. 配置优雅关闭选项 ====================
    // 配置SO_LINGER选项，用于控制close()系统调用的行为。
    // struct linger结构体用于设置SO_LINGER选项的参数。
    struct linger optLinger = { 0 }; // 初始化linger结构体，l_onoff和l_linger都设为0。
                                     // 默认情况下 (l_onoff=0)，close()会立即返回，系统会尝试在后台发送剩余数据。

    // 根据服务器的配置参数openLinger_决定是否启用优雅关闭。
    if(openLinger_) {
        // 如果openLinger_为true，表示启用优雅关闭。
        // 优雅关闭 (Graceful Shutdown)：
        // 当调用close()时，如果发送缓冲区中还有未发送的数据，系统不会立即丢弃这些数据，
        // 而是会尝试在指定的时间内 (l_linger) 将这些数据发送出去。
        // 如果在超时时间内数据发送完毕，连接正常关闭 (FIN)。
        // 如果超时时间到达数据仍未发送完毕，连接会被强制关闭 (RST)。
        optLinger.l_onoff = 1;   // 将l_onoff设置为1，启用SO_LINGER选项。
        optLinger.l_linger = 1;  // 将l_linger设置为1秒，指定等待发送数据的超时时间。
                                 // 这里的1秒是一个相对较小的超时值，旨在快速释放资源，
                                 // 同时给少量关键数据（如最后的HTTP响应）一个发送的机会。
    }

    // ==================== 4. 创建TCP socket ====================
    // 创建监听socket
    // AF_INET: IPv4协议族
    // SOCK_STREAM: TCP流式socket
    // 0: 自动选择协议 (TCP)
    listenFd_ = socket(AF_INET, SOCK_STREAM, 0);
    // 检查socket是否创建成功
    if(listenFd_ < 0) {
        // 如果创建失败，记录错误日志
        LOG_ERROR("Create socket error!", port_);
        // 返回false表示初始化失败
        return false;
    }

    // ==================== 5. 设置SO_LINGER选项 ====================
    // 设置socket选项，启用优雅关闭
    // setsockopt()函数用于设置socket的选项。
    // 第一个参数是socket文件描述符
    // 第二个参数是选项层级，SOL_SOCKET表示通用socket选项
    // 第三个参数是选项名称，SO_LINGER表示优雅关闭选项
    // 第四个参数是选项值，&optLinger表示指向linger结构体的指针
    ret = setsockopt(listenFd_, SOL_SOCKET, SO_LINGER, &optLinger, sizeof(optLinger));
    if(ret < 0) {
        // 如果设置失败，关闭socket并记录错误日志
        close(listenFd_);
        LOG_ERROR("Init linger error!", port_);
        // 返回false表示初始化失败
        return false;
    }

    // ==================== 6. 设置端口复用 ====================
    // 设置端口复用选项
    // 允许重用本地地址，避免TIME_WAIT状态下的端口占用
    // 定义一个整数变量optval，并初始化为1。
    // 这个变量将作为setsockopt函数的选项值，1表示启用该选项。
    int optval = 1;
    // 调用setsockopt函数设置socket选项。
    // listenFd_: 要设置选项的socket文件描述符。
    // SOL_SOCKET: 选项所在的协议层，SOL_SOCKET表示在socket层。
    // SO_REUSEADDR: 选项的名称，表示允许重用本地地址和端口。
    //               这对于服务器非常有用，可以在服务器崩溃或重启后立即绑定到之前使用的端口，
    //               而无需等待TIME_WAIT状态的连接超时。
    // (const void*)&optval: 指向选项值的指针，这里是optval的地址。
    // sizeof(int): 选项值的大小。
    ret = setsockopt(listenFd_, SOL_SOCKET, SO_REUSEADDR, (const void*)&optval, sizeof(int));
    // 检查setsockopt函数的返回值。
    // 如果返回-1，表示设置选项失败。
    if(ret == -1) {
        // 记录错误日志，说明设置SO_REUSEADDR选项失败。
        LOG_ERROR("set socket setsockopt error !");
        // 关闭监听socket，释放资源。
        close(listenFd_);
        // 返回false，表示初始化过程失败。
        return false;
    }

    // ==================== 7. 绑定地址和端口 ====================
    // 将socket绑定到指定的地址和端口
    // bind()函数用于将socket绑定到指定的地址和端口。
    // 第一个参数是socket文件描述符
    // 第二个参数是结构体指针，包含要绑定的地址和端口
    // 第三个参数是地址结构体的大小
    ret = bind(listenFd_, (struct sockaddr *)&addr, sizeof(addr));
    if(ret < 0) {
        LOG_ERROR("Bind Port:%d error!", port_);
        close(listenFd_);
        return false;
    }

    // ==================== 8. 开始监听连接 ====================
    // 开始监听连接
    // listen()函数用于将socket设置为监听状态。
    // 第一个参数是socket文件描述符
    // 第二个参数是监听队列的最大长度
    ret = listen(listenFd_, 6);  // 监听队列长度为6
    if(ret < 0) {
        LOG_ERROR("Listen port:%d error!", port_);
        close(listenFd_);
        return false;
    }
    
    // ==================== 9. 添加到epoll监听 ====================
    // 将监听socket添加到epoll事件监听器中。
    // epoller_ 是 Epoller 对象的智能指针，负责管理epoll实例。
    // AddFd 方法用于向epoll实例注册一个文件描述符及其关注的事件。
    // listenFd_: 要添加的文件描述符，这里是监听socket。
    // listenEvent_ | EPOLLIN: 关注的事件。
    //   listenEvent_ 通常包含边缘触发(EPOLLET)等模式设置。
    //   EPOLLIN: 表示关注文件描述符可读（有新连接到来）事件。
    ret = epoller_->AddFd(listenFd_,  listenEvent_ | EPOLLIN);
    // 检查 AddFd 的返回值。根据 Epoller::AddFd 的实现，返回0可能表示失败。
    if(ret == 0) {
        // 如果添加失败，记录错误日志。
        LOG_ERROR("Add listen error!");
        // 关闭监听socket，释放资源。
        close(listenFd_);
        // 返回false，表示初始化过程失败。
        return false;
    }
    
    // ==================== 10. 设置非阻塞模式 ====================
    SetFdNonblock(listenFd_);
    
    LOG_INFO("Server port:%d", port_);
    return true;
}

/**
 * @brief 设置文件描述符为非阻塞模式
 * @param fd 文件描述符
 * @return int 设置结果，成功返回新的文件状态标志
 * 
 * 非阻塞IO的重要性：
 * 1. 避免IO操作阻塞整个线程
 * 2. 配合epoll实现高并发处理
 * 3. 提高服务器响应性能
 * 
 * 实现原理：
 * 1. 获取当前文件状态标志
 * 2. 添加O_NONBLOCK标志
 * 3. 设置新的文件状态标志
 */
int WebServer::SetFdNonblock(int fd) {
    assert(fd > 0);
    
    // 获取当前文件状态标志，然后添加O_NONBLOCK标志
    return fcntl(fd, F_SETFL, fcntl(fd, F_GETFD, 0) | O_NONBLOCK);
}


