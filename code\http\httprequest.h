/*
 * <AUTHOR> mark
 * @Date         : 2020-06-25
 * @copyleft Apache 2.0
 * 
 * HttpRequest HTTP请求解析器
 * 
 * What (是什么)：
 * 基于状态机的HTTP/1.1请求解析器，支持GET/POST方法，用户验证，表单数据处理
 * 
 * Why (为什么)：
 * 1. 将原始HTTP报文转换为结构化数据
 * 2. 支持动态路由和用户认证
 * 3. 处理URL编码的表单数据
 * 4. 实现Keep-Alive连接管理
 * 5. 提供安全的用户登录注册功能
 * 
 * How (怎么做)：
 * 1. 使用有限状态机解析HTTP报文格式
 * 2. 正则表达式匹配请求行和头部字段
 * 3. MySQL数据库验证用户身份
 * 4. URL解码处理特殊字符
 * 5. 路径映射实现页面跳转
 */ 

#ifndef HTTP_REQUEST_H
#define HTTP_REQUEST_H

// 标准库头文件
#include <unordered_map>   // 哈希表：存储HTTP头部和POST参数
#include <unordered_set>   // 哈希集合：存储默认HTML页面列表
#include <string>          // 字符串处理
#include <regex>           // 正则表达式：解析HTTP请求行和头部
#include <errno.h>         // 错误码定义
#include <mysql/mysql.h>   // MySQL C API：用户验证

// 项目内部模块
#include "../buffer/buffer.h"         // 缓冲区管理
#include "../log/log.h"               // 日志系统
#include "../pool/sqlconnpool.h"      // 数据库连接池
#include "../pool/sqlconnRAII.h"      // RAII数据库连接管理

/**
 * @class HttpRequest
 * @brief HTTP请求解析和处理类
 * 
 * 解析状态机架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                   HTTP请求解析流程                       │
 * │                                                         │
 * │  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐   │
 * │  │REQUEST_LINE │──►│   HEADERS   │──►│    BODY     │   │
 * │  │             │   │             │   │             │   │
 * │  │解析请求行    │   │解析头部字段  │   │解析请求体    │   │
 * │  │GET/POST     │   │Content-Type │   │POST数据     │   │
 * │  │URL Path     │   │Connection   │   │表单参数     │   │
 * │  │HTTP版本     │   │Keep-Alive   │   │用户验证     │   │
 * │  └─────────────┘   └─────────────┘   └─────────────┘   │
 * │         │                  │                  │        │
 * │         ▼                  ▼                  ▼        │
 * │  ┌─────────────────────────────────────────────────────┐ │
 * │  │                  FINISH                             │ │
 * │  │                                                     │ │
 * │  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐│ │
 * │  │  │路径映射  │  │用户验证  │  │数据解码  │  │响应准备 ││ │
 * │  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘│ │
 * │  └─────────────────────────────────────────────────────┘ │
 * └─────────────────────────────────────────────────────────┘
 */
class HttpRequest {
public:
    // ==================== 解析状态枚举 ====================
    
    /**
     * @brief HTTP请求解析状态
     * 
     * 主人，让我为您详细解释这个状态机的设计！
     * 
     * 【什么是状态机？】
     * 状态机就像一个有序的工作流程，比如您去银行办业务：
     * 1. 先取号(REQUEST_LINE) 
     * 2. 再排队等叫号(HEADERS)
     * 3. 然后到柜台办理(BODY)
     * 4. 最后完成业务(FINISH)
     * 
     * 每个步骤都有明确的顺序，不能跳跃！
     * 
     * 【为什么要用状态机解析HTTP？】
     * HTTP协议有固定格式，必须按顺序解析：
     * ┌─────────────────────────────────────┐
     * │ HTTP请求的标准格式：                  │
     * │                                   │
     * │ GET /login.html HTTP/1.1    ←─请求行 │
     * │ Host: localhost:8080        ←─头部1  │
     * │ Connection: keep-alive      ←─头部2  │
     * │ Content-Length: 25          ←─头部3  │
     * │                            ←─空行   │
     * │ username=admin&password=123 ←─请求体  │
     * └─────────────────────────────────────┘
     * 
     * 【状态转换流程】
     * REQUEST_LINE → HEADERS → BODY → FINISH
     *     ↓            ↓        ↓       ↓
     *   解析方法     解析请求头  解析数据  处理完成
     */
    enum PARSE_STATE {
        REQUEST_LINE,    // 状态1：解析请求行（第一行）
                        // 例如："GET /index.html HTTP/1.1"
                        // 提取：请求方法(GET)、路径(/index.html)、版本(HTTP/1.1)
                        
        HEADERS,         // 状态2：解析请求头部（多行键值对）
                        // 例如："Host: localhost"、"Connection: keep-alive"
                        // 存储到哈希表中，方便后续查询
                        
        BODY,            // 状态3：解析请求体（POST数据）
                        // 例如：表单数据 "username=admin&password=123"
                        // 只有POST请求才有请求体，GET请求跳过此状态
                        
        FINISH,          // 状态4：解析完成
                        // 所有数据解析完毕，可以开始处理业务逻辑
    };

    /**
     * @brief HTTP处理结果码
     * What: 标识请求处理的结果状态
     * Why:  便于上层逻辑根据解析结果做相应处理
     * How:  枚举值对应不同的处理情况
     */
    enum HTTP_CODE {
        NO_REQUEST = 0,      // 请求不完整，需要更多数据
        GET_REQUEST,         // 完整的GET请求
        BAD_REQUEST,         // 请求语法错误
        NO_RESOURSE,         // 请求的资源不存在
        FORBIDDENT_REQUEST,  // 请求被禁止访问
        FILE_REQUEST,        // 文件请求
        INTERNAL_ERROR,      // 服务器内部错误
        CLOSED_CONNECTION,   // 连接关闭
    };
    
    // ==================== 构造与析构 ====================
    
    /**
     * @brief 默认构造函数
     * What: 初始化HTTP请求解析器
     * Why:  创建可复用的解析器对象
     * How:  调用Init()方法重置状态
     */
    HttpRequest() { Init(); }
    
    /**
     * @brief 默认析构函数
     * What: 清理HTTP请求相关资源
     * Why:  RAII原则，自动资源管理
     * How:  编译器生成的默认析构函数
     */
    ~HttpRequest() = default;

    // ==================== 核心接口 ====================

    /**
     * @brief 初始化/重置解析器状态
     * What: 清空所有解析状态，准备解析新请求
     * Why:  支持连接复用，一个连接处理多个请求
     * How:  重置成员变量到初始状态
     */
    void Init();
    
    /**
     * @brief 解析HTTP请求
     * @param buff 包含HTTP请求数据的缓冲区
     * @return bool true表示解析成功，false表示失败或需要更多数据
     * 
     * What: 状态机驱动的HTTP请求解析核心函数
     * Why:  将原始字节流转换为结构化的HTTP请求数据
     * How:  
     * 1. 按行读取缓冲区数据
     * 2. 根据当前状态调用相应的解析函数
     * 3. 状态转换直到FINISH或出错
     */
    bool parse(Buffer& buff);

    // ==================== 数据访问接口 ====================

    /**
     * @brief 获取请求路径（只读）
     * What: 返回HTTP请求的路径部分
     * Why:  上层需要根据路径确定响应内容
     * How:  返回path_成员变量的拷贝
     */
    std::string path() const;
    
    /**
     * @brief 获取请求路径（可修改）
     * What: 返回HTTP请求的路径引用
     * Why:  允许上层修改路径（如重定向）
     * How:  返回path_成员变量的引用
     */
    std::string& path();
    
    /**
     * @brief 获取请求方法
     * What: 返回HTTP请求方法（GET/POST等）
     * Why:  不同方法需要不同的处理逻辑
     * How:  返回method_成员变量
     */
    std::string method() const;
    
    /**
     * @brief 获取HTTP版本
     * What: 返回HTTP协议版本（1.0/1.1等）
     * Why:  版本影响响应格式和连接管理
     * How:  返回version_成员变量
     */
    std::string version() const;
    
    /**
     * @brief 获取POST参数值
     * @param key 参数名
     * @return std::string 参数值，不存在返回空字符串
     * 
     * What: 获取POST表单中的特定参数
     * Why:  处理用户提交的表单数据
     * How:  在post_哈希表中查找对应键值
     */
    std::string GetPost(const std::string& key) const;
    std::string GetPost(const char* key) const;

    /**
     * @brief 检查是否为Keep-Alive连接
     * @return bool true表示保持连接，false表示关闭连接
     * 
     * What: 判断客户端是否请求保持连接
     * Why:  Keep-Alive可以复用连接，提高性能
     * How:  检查Connection头部字段和HTTP版本
     */
    bool IsKeepAlive() const;

    /* 
    TODO: 扩展功能
    void HttpConn::ParseFormData() {}  // 解析multipart/form-data
    void HttpConn::ParseJson() {}      // 解析JSON格式数据
    */

private:
    // ==================== 私有解析函数 ====================

    /**
     * @brief 解析HTTP请求行
     * @param line 请求行字符串
     * @return bool 解析是否成功
     * 
     * What: 解析"GET /path HTTP/1.1"格式的请求行
     * Why:  请求行包含最基本的请求信息
     * How:  使用正则表达式提取方法、路径、版本
     */
    bool ParseRequestLine_(const std::string& line);
    
    /**
     * @brief 解析HTTP头部字段
     * @param line 头部行字符串
     * 
     * What: 解析"Key: Value"格式的头部字段
     * Why:  头部包含请求的元数据信息
     * How:  正则表达式分离键值对，存储到header_映射
     */
    void ParseHeader_(const std::string& line);
    
    /**
     * @brief 解析HTTP请求体
     * @param line 请求体字符串
     * 
     * What: 解析POST请求的主体数据
     * Why:  POST数据包含用户提交的表单信息
     * How:  保存到body_，然后调用ParsePost_处理
     */
    void ParseBody_(const std::string& line);

    /**
     * @brief 处理请求路径
     * 
     * What: 将抽象路径映射到具体的HTML文件
     * Why:  简化URL结构，支持默认页面
     * How:  查表映射，添加.html后缀
     */
    void ParsePath_();
    
    /**
     * @brief 处理POST请求数据
     * 
     * What: 解析POST表单数据并进行用户验证
     * Why:  支持用户登录注册功能
     * How:  根据Content-Type选择解析方法，调用用户验证
     */
    void ParsePost_();
    
    /**
     * @brief 解析URL编码的表单数据
     * 
     * What: 解析application/x-www-form-urlencoded格式数据
     * Why:  标准的HTML表单提交格式
     * How:  解析键值对，处理URL编码的特殊字符
     */
    void ParseFromUrlencoded_();

    /**
     * @brief 用户身份验证
     * @param name 用户名
     * @param pwd 密码
     * @param isLogin true为登录，false为注册
     * @return bool 验证是否成功
     * 
     * What: 基于MySQL数据库的用户认证系统
     * Why:  提供安全的用户登录注册功能
     * How:  查询user表验证密码，支持新用户注册
     */
    static bool UserVerify(const std::string& name, const std::string& pwd, bool isLogin);

    // ==================== 成员变量 ====================

    /**
     * @brief 当前解析状态
     * What: 状态机的当前状态
     * Why:  控制解析流程的状态转换
     * How:  在parse()函数中根据状态调用不同解析函数
     */
    PARSE_STATE state_;
    
    /**
     * @brief HTTP请求的基本组成部分
     * What: 存储解析出的请求信息
     * Why:  结构化存储便于后续处理
     * How:  分别存储方法、路径、版本、请求体
     */
    std::string method_, path_, version_, body_;
    
    /**
     * @brief HTTP头部字段映射
     * What: 存储所有头部键值对
     * Why:  头部信息影响请求处理逻辑
     * How:  unordered_map提供O(1)查找性能
     */
    std::unordered_map<std::string, std::string> header_;
    
    /**
     * @brief POST参数映射
     * What: 存储表单提交的参数
     * Why:  支持动态内容和用户交互
     * How:  从URL编码数据中解析出的键值对
     */
    std::unordered_map<std::string, std::string> post_;

    // ==================== 静态配置数据 ====================

    /**
     * @brief 默认HTML页面集合
     * What: 服务器支持的静态页面列表
     * Why:  简化URL到文件的映射关系
     * How:  unordered_set提供快速查找
     */
    static const std::unordered_set<std::string> DEFAULT_HTML;
    
    /**
     * @brief HTML页面标签映射
     * What: 特殊页面的处理标识
     * Why:  区分登录页面和注册页面的处理逻辑
     * How:  映射页面路径到处理标签
     */
    static const std::unordered_map<std::string, int> DEFAULT_HTML_TAG;
    
    /**
     * @brief 十六进制字符转换
     * @param ch 十六进制字符
     * @return int 对应的数值
     * 
     * What: URL解码辅助函数
     * Why:  处理%XX格式的URL编码字符
     * How:  字符到数值的转换表
     */
    static int ConverHex(char ch);
};

#endif //HTTP_REQUEST_H