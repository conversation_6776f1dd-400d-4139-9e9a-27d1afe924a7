/*
 * <AUTHOR> mark
 * @Date         : 2020-06-16
 * @copyleft Apache 2.0
 * 
 * Log 异步日志系统
 * 设计模式：单例模式 + 生产者消费者模式
 * 核心特性：
 * 1. 异步写入，不阻塞主业务逻辑
 * 2. 线程安全的日志记录
 * 3. 自动日志文件切换和管理
 * 4. 多级别日志支持
 * 5. 高性能的缓冲区管理
 */ 

#ifndef LOG_H
#define LOG_H

// 系统头文件
#include <mutex>        // 互斥锁，保证线程安全
#include <string>       // 字符串处理
#include <thread>       // C++11线程库
#include <sys/time.h>   // 时间相关函数
#include <string.h>     // 字符串操作函数
#include <stdarg.h>     // 可变参数处理：va_start, va_end
#include <assert.h>     // 断言宏
#include <sys/stat.h>   // 文件状态：mkdir等

// 项目内部模块
#include "blockqueue.h"      // 阻塞队列，实现生产者消费者模式
#include "../buffer/buffer.h" // 高效缓冲区

/**
 * @class Log
 * @brief 高性能异步日志系统
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                    Log System                           │
 * │                                                         │
 * │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │
 * │  │  业务线程    │    │   日志队列   │    │  写入线程    │ │
 * │  │             │    │             │    │             │ │
 * │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │ │
 * │  │ │LOG_INFO │ │───►│ │ Block   │ │◄───│ │ Async   │ │ │
 * │  │ │LOG_WARN │ │    │ │ Queue   │ │    │ │ Write   │ │ │
 * │  │ │LOG_ERROR│ │    │ │         │ │    │ │ Thread  │ │ │
 * │  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │ │
 * │  └─────────────┘    └─────────────┘    └─────────────┘ │
 * │         │                   │                   │      │
 * │         ▼                   ▼                   ▼      │
 * │  ┌─────────────────────────────────────────────────────┐ │
 * │  │                  File System                        │ │
 * │  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐│ │
 * │  │  │ 2024-01 │  │ 2024-02 │  │ 2024-03 │  │   ...   ││ │
 * │  │  │ -01.log │  │ -01.log │  │ -01.log │  │         ││ │
 * │  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘│ │
 * │  └─────────────────────────────────────────────────────┘ │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 工作流程：
 * 1. 业务线程调用LOG_XXX宏记录日志
 * 2. 日志内容被格式化并放入阻塞队列
 * 3. 异步写入线程从队列取出日志并写入文件
 * 4. 自动管理日志文件的切换和创建
 */
class Log {
public:
    /**
     * @brief 初始化日志系统
     * @param level 日志级别：0-DEBUG, 1-INFO, 2-WARN, 3-ERROR
     * @param path 日志文件路径，默认"./log"
     * @param suffix 日志文件后缀，默认".log"
     * @param maxQueueCapacity 异步队列容量，默认1024
     * 
     * 功能：
     * 1. 设置日志级别和文件路径
     * 2. 创建日志目录
     * 3. 初始化异步写入线程
     * 4. 配置阻塞队列
     */
    void init(int level, const char* path = "./log", 
                const char* suffix =".log",
                int maxQueueCapacity = 1024);

    /**
     * @brief 获取日志系统单例
     * @return Log* 日志系统实例指针
     * 
     * 单例模式实现：
     * 1. 线程安全的懒汉式单例
     * 2. 使用局部静态变量保证唯一性
     * 3. C++11保证静态变量初始化的线程安全
     */
    static Log* Instance();
    
    /**
     * @brief 异步写入线程的入口函数
     * 
     * 静态函数，用于创建异步写入线程
     * 内部调用AsyncWrite_()执行实际的写入逻辑
     */
    static void FlushLogThread();

    /**
     * @brief 写入日志（核心接口）
     * @param level 日志级别
     * @param format 格式化字符串（类似printf）
     * @param ... 可变参数
     * 
     * 功能：
     * 1. 检查日志级别是否满足输出条件
     * 2. 格式化日志内容（时间戳、级别、内容）
     * 3. 异步模式：放入队列；同步模式：直接写入
     */
    void write(int level, const char *format,...);
    
    /**
     * @brief 刷新日志缓冲区
     * 
     * 强制将缓冲区中的日志内容写入文件
     * 确保重要日志及时持久化
     */
    void flush();

    // ==================== 配置管理接口 ====================

    /**
     * @brief 获取当前日志级别
     * @return int 日志级别
     */
    int GetLevel();
    
    /**
     * @brief 设置日志级别
     * @param level 新的日志级别
     */
    void SetLevel(int level);
    
    /**
     * @brief 检查日志系统是否已开启
     * @return bool true表示已开启，false表示未开启
     */
    bool IsOpen() { return isOpen_; }
    
private:
    /**
     * @brief 私有构造函数（单例模式）
     * 初始化日志系统的基本状态
     */
    Log();
    
    /**
     * @brief 添加日志级别标题
     * @param level 日志级别
     * 
     * 在日志内容前添加级别标识：[DEBUG], [INFO], [WARN], [ERROR]
     */
    void AppendLogLevelTitle_(int level);
    
    /**
     * @brief 虚析构函数
     * 清理资源，关闭文件，停止异步线程
     */
    virtual ~Log();
    
    /**
     * @brief 异步写入线程的主循环
     * 
     * 功能：
     * 1. 从阻塞队列中取出日志内容
     * 2. 写入到日志文件
     * 3. 处理日志文件的切换
     */
    void AsyncWrite_();

private:
    // ==================== 静态常量配置 ====================
    
    static const int LOG_PATH_LEN = 256;  // 日志路径最大长度
    static const int LOG_NAME_LEN = 256;  // 日志文件名最大长度
    static const int MAX_LINES = 50000;   // 单个日志文件最大行数

    // ==================== 文件管理相关 ====================
    
    const char* path_;      // 日志文件路径
    const char* suffix_;    // 日志文件后缀
    int MAX_LINES_;         // 最大行数限制
    int lineCount_;         // 当前文件行数计数
    int toDay_;             // 当前日期（用于日志文件切换）

    // ==================== 系统状态管理 ====================
    
    bool isOpen_;           // 日志系统开启状态
    Buffer buff_;           // 日志内容缓冲区
    int level_;             // 当前日志级别
    bool isAsync_;          // 是否启用异步模式

    // ==================== 文件IO相关 ====================
    
    FILE* fp_;              // 日志文件指针

    // ==================== 异步处理相关 ====================
    
    std::unique_ptr<BlockDeque<std::string>> deque_;    // 阻塞队列，存储待写入的日志
    std::unique_ptr<std::thread> writeThread_;          // 异步写入线程
    std::mutex mtx_;                                    // 互斥锁，保护共享资源
};

// ==================== 日志宏定义 ====================

/**
 * @brief 日志记录基础宏
 * @param level 日志级别
 * @param format 格式化字符串
 * @param ... 可变参数
 * 
 * 这个宏的作用就像一个"万能日志记录器"，我来详细解释：
 * 
 * 什么是宏？
 * 宏就像是一个"代码模板"，当你写 LOG_BASE(1, "hello %s", "world") 时，
 * 编译器会把这段代码完全替换成下面 do-while 里的内容。
 * 
 * 为什么用 do-while(0)？
 * 这是个编程技巧，就像给代码加个"保护套"：
 * - 确保宏在任何地方使用都安全
 * - 比如在 if 语句后不用大括号时不会出错
 * - 例子：if (error) LOG_DEBUG("出错了"); else continue;
 * 
 * 代码执行流程（一步步解释）：
 * 1. Log* log = Log::Instance();  // 获取日志系统单例（全局唯一的日志对象）
 * 2. if (log->IsOpen() && log->GetLevel() <= level)  // 检查两个条件：
 *    - IsOpen()：日志系统是否开启？（比如初始化了吗？）
 *    - GetLevel() <= level：当前日志级别是否允许输出？
 *      比如系统设置只显示ERROR(3)级别，那DEBUG(0)就不会输出
 * 3. log->write(level, format, ##__VA_ARGS__);  // 如果条件满足，写入日志
 * 4. log->flush();  // 立即刷新，确保日志马上写入文件
 * 
 * ##__VA_ARGS__ 是什么？
 * 这是处理可变参数的特殊语法：
 * - __VA_ARGS__ 代表 "..." 中的所有参数
 * - ## 的作用是：如果没有参数，就删除前面的逗号
 * - 例子：LOG_BASE(1, "hello") 会变成 write(1, "hello")
 *        LOG_BASE(1, "hello %s", "world") 会变成 write(1, "hello %s", "world")
 */
#define LOG_BASE(level, format, ...) \
    do {\
        Log* log = Log::Instance();\
        if (log->IsOpen() && log->GetLevel() <= level) {\
            log->write(level, format, ##__VA_ARGS__); \
            log->flush();\
        }\
    } while(0);

/**
 * @brief DEBUG级别日志宏 - 级别0（最详细）
 * 用于调试信息，通常在开发阶段使用
 * 
 * 简单理解：这就是调用 LOG_BASE(0, format, ...)
 * 使用例子：LOG_DEBUG("变量x的值是: %d", x);
 * 生活比喻：就像医生的详细检查记录，平时不看，出问题时很有用
 */
#define LOG_DEBUG(format, ...) do {LOG_BASE(0, format, ##__VA_ARGS__)} while(0);

/**
 * @brief INFO级别日志宏 - 级别1（一般信息）
 * 用于一般信息记录，如系统启动、配置加载等
 * 
 * 使用例子：LOG_INFO("服务器启动成功，端口：%d", port);
 * 生活比喻：就像新闻播报，告诉你发生了什么重要的事
 */
#define LOG_INFO(format, ...) do {LOG_BASE(1, format, ##__VA_ARGS__)} while(0);

/**
 * @brief WARN级别日志宏 - 级别2（警告）
 * 用于警告信息，如资源不足、配置异常等
 * 
 * 使用例子：LOG_WARN("内存使用率已达 %d%%", usage);
 * 生活比喻：就像汽车的油量警告灯，提醒你注意但还能继续
 */
#define LOG_WARN(format, ...) do {LOG_BASE(2, format, ##__VA_ARGS__)} while(0);

/**
 * @brief ERROR级别日志宏 - 级别3（错误，最严重）
 * 用于错误信息，如系统异常、操作失败等
 * 
 * 使用例子：LOG_ERROR("数据库连接失败：%s", error_msg);
 * 生活比喻：就像火灾报警器，有严重问题必须立即处理
 */
#define LOG_ERROR(format, ...) do {LOG_BASE(3, format, ##__VA_ARGS__)} while(0);

/*
 * 日志级别的过滤机制：
 * 如果系统设置日志级别为2(WARN)，那么：
 * - LOG_DEBUG(0) 不会输出 ❌ (0 < 2)
 * - LOG_INFO(1)  不会输出 ❌ (1 < 2) 
 * - LOG_WARN(2)  会输出    ✅ (2 <= 2)
 * - LOG_ERROR(3) 会输出    ✅ (3 > 2)
 * 
 * 这样设计的好处：
 * 1. 开发时用DEBUG看所有信息
 * 2. 生产环境用INFO减少日志量
 * 3. 紧急情况只看ERROR快速定位问题
 */

#endif //LOG_H