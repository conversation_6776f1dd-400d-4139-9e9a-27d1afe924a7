/*
 * <AUTHOR> mark
 * @Date         : 2020-06-15
 * @copyleft Apache 2.0
 * 
 * HttpConn HTTP连接管理类实现
 * 
 * What (是什么)：
 * HTTP连接的完整生命周期管理，包含连接初始化、数据读写、请求处理、连接关闭
 * 
 * Why (为什么)：
 * 1. 封装复杂的HTTP连接处理逻辑
 * 2. 提供线程安全的连接管理
 * 3. 支持Keep-Alive长连接机制
 * 4. 实现零拷贝数据传输优化
 * 5. 统一管理连接状态和资源
 * 
 * How (怎么做)：
 * 1. 使用原子操作管理全局连接计数
 * 2. 通过Buffer缓冲区优化IO操作
 * 3. 采用向量IO(writev)提升写性能
 * 4. 结合HttpRequest和HttpResponse处理HTTP协议
 */ 

#include "httpconn.h"
using namespace std;

// ==================== 静态成员变量定义 ====================

/**
 * @brief 资源目录路径
 * What: 静态资源文件的根目录路径
 * Why:  所有HTTP连接共享同一个资源目录，避免重复存储
 * How:  在WebServer构造时设置，所有连接实例共享
 */
const char* HttpConn::srcDir;

/**
 * @brief 全局用户连接计数器
 * What: 记录当前活跃的HTTP连接总数
 * Why:  
 * 1. 监控服务器负载状态
 * 2. 实现连接数限制控制
 * 3. 统计服务器性能指标
 * How:  使用原子操作保证多线程安全性
 */
std::atomic<int> HttpConn::userCount;

/**
 * @brief 边缘触发模式标志
 * What: 标识当前是否使用epoll的ET(Edge Triggered)模式
 * Why:  ET模式需要一次性读取所有可用数据，影响read/write行为
 * How:  在WebServer初始化时设置，影响循环读写逻辑
 */
bool HttpConn::isET;

// ==================== 构造与析构函数 ====================

/**
 * @brief 默认构造函数
 * What: 初始化HTTP连接对象为无效状态
 * Why:  对象池模式下需要预创建对象，后续通过init()激活
 * How:  设置无效的fd和关闭状态
 */
HttpConn::HttpConn() { 
    fd_ = -1;           // 无效文件描述符
    addr_ = { 0 };      // 清零地址结构
    isClose_ = true;    // 标记为关闭状态
};

/**
 * @brief 析构函数
 * What: 清理HTTP连接相关资源
 * Why:  RAII原则，确保资源正确释放
 * How:  调用Close()方法关闭连接
 */
HttpConn::~HttpConn() { 
    Close(); 
};

// ==================== 连接生命周期管理 ====================

/**
 * @brief 初始化HTTP连接
 * @param fd 客户端socket文件描述符
 * @param addr 客户端地址信息
 * 
 * What: 激活HTTP连接，设置连接参数
 * Why:  将预分配的连接对象绑定到具体的客户端socket
 * How:  
 * 1. 保存fd和地址信息
 * 2. 清空读写缓冲区
 * 3. 增加全局连接计数
 * 4. 记录连接建立日志
 */
void HttpConn::init(int fd, const sockaddr_in& addr) {
    assert(fd > 0);
    
    userCount++;                    // 原子操作：连接数+1
    addr_ = addr;                   // 保存客户端地址
    fd_ = fd;                       // 保存socket文件描述符
    writeBuff_.RetrieveAll();       // 清空写缓冲区
    readBuff_.RetrieveAll();        // 清空读缓冲区
    isClose_ = false;               // 标记连接为活跃状态
    
    // 记录连接建立日志：[fd](IP:PORT) in, userCount:总数
    LOG_INFO("Client[%d](%s:%d) in, userCount:%d", fd_, GetIP(), GetPort(), (int)userCount);
}

/**
 * @brief 关闭HTTP连接
 * 
 * What: 完整的连接关闭流程
 * Why:  释放系统资源，防止文件描述符泄漏
 * How:  
 * 1. 解除文件内存映射
 * 2. 关闭socket描述符
 * 3. 减少全局连接计数
 * 4. 记录连接关闭日志
 */
void HttpConn::Close() {
    response_.UnmapFile();          // 解除mmap文件映射
    if(isClose_ == false){
        isClose_ = true;            // 设置关闭标志
        userCount--;                // 原子操作：连接数-1
        close(fd_);                 // 关闭socket
        
        // 记录连接关闭日志
        LOG_INFO("Client[%d](%s:%d) quit, UserCount:%d", fd_, GetIP(), GetPort(), (int)userCount);
    }
}

// ==================== 连接信息获取接口 ====================

/**
 * @brief 获取socket文件描述符
 * What: 返回当前连接的文件描述符
 * Why:  epoll事件处理需要fd作为标识
 * How:  直接返回成员变量fd_
 */
int HttpConn::GetFd() const {
    return fd_;
};

/**
 * @brief 获取客户端地址结构
 * What: 返回客户端的sockaddr_in地址信息
 * Why:  用于日志记录和连接管理
 * How:  直接返回成员变量addr_
 */
struct sockaddr_in HttpConn::GetAddr() const {
    return addr_;
}

/**
 * @brief 获取客户端IP地址字符串
 * What: 将二进制IP地址转换为点分十进制字符串
 * Why:  便于日志记录和调试
 * How:  使用inet_ntoa()系统函数转换
 */
const char* HttpConn::GetIP() const {
    return inet_ntoa(addr_.sin_addr);
}

/**
 * @brief 获取客户端端口号
 * What: 返回客户端的端口号
 * Why:  用于连接标识和日志记录
 * How:  直接返回addr_.sin_port（网络字节序）
 */
int HttpConn::GetPort() const {
    return addr_.sin_port;
}

// ==================== 数据读取操作 ====================

/**
 * @brief 从socket读取数据到缓冲区
 * @param saveErrno 输出参数，保存errno错误码
 * @return ssize_t 读取的字节数，-1表示错误
 * 
 * What: 高效的socket数据读取操作
 * Why:  
 * 1. ET模式需要一次性读取所有可用数据
 * 2. 使用Buffer缓冲区管理内存
 * 3. 避免数据丢失和读取不完整
 * 
 * How:  
 * 1. 循环读取直到无数据或出错
 * 2. ET模式下循环读取，LT模式读取一次
 * 3. 委托给Buffer的ReadFd()方法处理复杂逻辑
 */
ssize_t HttpConn::read(int* saveErrno) {
    ssize_t len = -1;
    do {
        // 使用Buffer的高效读取方法（支持readv分散读取）
        len = readBuff_.ReadFd(fd_, saveErrno);
        if (len <= 0) {
            break;  // 读取完毕或出错
        }
    } while (isET);  // ET模式下循环读取所有数据
    return len;
}

// ==================== 数据写入操作 ====================

/**
 * @brief 向socket写入数据
 * @param saveErrno 输出参数，保存errno错误码
 * @return ssize_t 写入的字节数，-1表示错误
 * 
 * What: 高性能的HTTP响应数据发送
 * Why:  
 * 1. 支持零拷贝的向量IO操作
 * 2. 同时发送响应头和文件内容
 * 3. 处理部分写入的情况
 * 4. ET模式下完整发送数据
 * 
 * How:  
 * 1. 使用writev()向量IO同时发送多个缓冲区
 * 2. iov_[0]包含HTTP响应头数据
 * 3. iov_[1]包含mmap映射的文件内容
 * 4. 处理部分写入，更新iovec结构
 */
ssize_t HttpConn::write(int* saveErrno) {
    ssize_t len = -1;  // 初始化返回值为-1（表示错误状态）
    
    do {
        // 🚀 核心技术：使用writev()向量IO进行零拷贝数据传输
        // writev()可以同时发送多个缓冲区的数据，避免数据拷贝
        // fd_: socket文件描述符
        // iov_: iovec结构数组，包含要发送的数据块
        // iovCnt_: iovec数组中有效元素的个数
        len = writev(fd_, iov_, iovCnt_);
        
        // 📝 错误处理：检查写入操作是否成功
        if(len <= 0) {
            *saveErrno = errno;  // 保存系统错误码供上层调用者使用
            break;               // 写入失败，退出循环
        }
        
        // ✅ 完成检查：判断是否所有数据都已发送完毕
        // iov_[0].iov_len: 响应头缓冲区剩余字节数
        // iov_[1].iov_len: 文件内容缓冲区剩余字节数
        if(iov_[0].iov_len + iov_[1].iov_len == 0) { 
            break; /* 🎉 所有数据传输完成，退出循环 */ 
        }
        
        // 🔄 部分写入处理：writev可能只发送了部分数据，需要调整缓冲区指针
        
        // 情况1：响应头已完全发送，文件内容部分发送
        // 判断条件：实际发送字节数 > 响应头总长度
        else if(static_cast<size_t>(len) > iov_[0].iov_len) {
            
            // 📊 计算文件内容已发送的字节数
            size_t fileSentBytes = len - iov_[0].iov_len;
            
            // 🎯 调整文件内容缓冲区指针和长度
            // 将iov_[1].iov_base指针向前移动已发送的字节数
            iov_[1].iov_base = (uint8_t*) iov_[1].iov_base + fileSentBytes;
            // 减少剩余待发送的文件内容长度
            iov_[1].iov_len -= fileSentBytes;
            
            // 🧹 清理响应头缓冲区（如果还有剩余长度的话）
            if(iov_[0].iov_len) {
                writeBuff_.RetrieveAll();  // 清空写缓冲区中的所有数据
                iov_[0].iov_len = 0;       // 响应头长度设为0
            }
        }
        // 情况2：响应头部分发送（还未发送完）
        else {
            // 🎯 调整响应头缓冲区指针和长度
            // 将iov_[0].iov_base指针向前移动已发送的字节数
            iov_[0].iov_base = (uint8_t*)iov_[0].iov_base + len; 
            // 减少剩余待发送的响应头长度
            iov_[0].iov_len -= len; 
            // 🧹 更新写缓冲区，移除已发送的数据
            writeBuff_.Retrieve(len);
        }
        
    // 🔁 循环条件：
    // 1. isET为true：边缘触发模式下需要一次性发送完所有数据
    // 2. ToWriteBytes() > 10240：剩余数据超过10KB时继续发送（避免大文件阻塞）
    } while(isET || ToWriteBytes() > 10240);
    
    return len;  // 返回最后一次写入的字节数
}

// ==================== HTTP协议处理 ====================

/**
 * @brief 处理HTTP请求
 * @return bool true表示有响应数据要发送，false表示需要更多请求数据
 * 
 * What: HTTP协议解析和响应生成的核心流程
 * Why:  
 * 1. 将原始HTTP请求转换为结构化数据
 * 2. 根据请求生成相应的HTTP响应
 * 3. 设置向量IO结构准备数据发送
 * 
 * How:  
 * 1. 初始化请求解析器
 * 2. 检查读缓冲区是否有数据
 * 3. 解析HTTP请求（请求行、头部、体）
 * 4. 生成HTTP响应（状态行、头部、内容）
 * 5. 设置iovec结构准备writev发送
 */
bool HttpConn::process() {
    request_.Init();  // 重置请求解析器状态
    
    // 检查是否有待处理的请求数据
    if(readBuff_.ReadableBytes() <= 0) {
        return false;  // 无数据，需要继续读取
    }
    // 解析HTTP请求
    else if(request_.parse(readBuff_)) {
        // 解析成功，记录请求路径
        LOG_DEBUG("%s", request_.path().c_str());
        
        // 生成200 OK响应
        response_.Init(srcDir, request_.path(), request_.IsKeepAlive(), 200);
    } else {
        // 解析失败，生成400 Bad Request响应
        response_.Init(srcDir, request_.path(), false, 400);
    }

    // 生成完整的HTTP响应到写缓冲区
    response_.MakeResponse(writeBuff_);
    
    // 设置响应头的iovec结构
    iov_[0].iov_base = const_cast<char*>(writeBuff_.Peek());  // 响应头数据
    iov_[0].iov_len = writeBuff_.ReadableBytes();             // 响应头长度
    iovCnt_ = 1;

    // 如果有文件内容，设置文件的iovec结构
    if(response_.FileLen() > 0  && response_.File()) {
        iov_[1].iov_base = response_.File();      // mmap映射的文件内容
        iov_[1].iov_len = response_.FileLen();    // 文件大小
        iovCnt_ = 2;  // 两个iovec：响应头 + 文件内容
    }
    
    // 记录调试信息：文件大小，iovec数量，总发送字节数
    LOG_DEBUG("filesize:%d, %d  to %d", response_.FileLen() , iovCnt_, ToWriteBytes());
    return true;  // 有数据要发送
}
