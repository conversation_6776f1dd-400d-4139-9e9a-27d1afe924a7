# 第五课：HTTP协议处理模块详解(核心业务逻辑)

> 主人，第五课我们来深度学习HTTP协议处理模块！这是Web服务器的核心业务逻辑，包括HTTP请求解析、响应生成和连接管理。我会为您逐行分析这个复杂而精妙的协议处理系统！

---

## 🎯 学习目标

通过本课学习，您将掌握：
1. **HTTP/1.1协议的完整解析流程**
2. **有限状态机的协议解析设计**
3. **正则表达式在协议解析中的应用**
4. **HTTP响应生成和文件传输机制**

---

## 📋 1. HTTP模块整体架构分析

### 1.1 HTTP处理三大核心类

```
HTTP模块架构图：

┌─────────────────────────────────────────────────────────┐
│                HTTP协议处理模块                          │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │
│  │ HttpRequest │    │HttpResponse │    │  HttpConn   │ │
│  │(请求解析)    │    │(响应生成)    │    │(连接管理)    │ │
│  │             │    │             │    │             │ │
│  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │ │
│  │ │状态机解析│ │    │ │响应构建  │ │    │ │读写缓冲区│ │ │
│  │ │正则匹配 │ │◄──►│ │文件映射  │ │◄──►│ │事件处理 │ │ │
│  │ │POST处理│ │    │ │状态码   │ │    │ │Keep-Alive│ │ │
│  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │ │
│  └─────────────┘    └─────────────┘    └─────────────┘ │
│         │                   │                   │      │
│         ▼                   ▼                   ▼      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │
│  │  用户验证    │    │  资源定位    │    │  定时器管理  │ │
│  │ (数据库查询) │    │ (文件系统)   │    │ (超时控制)   │ │
│  └─────────────┘    └─────────────┘    └─────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 1.2 HTTP处理流程概览

```
HTTP请求处理完整流程：

客户端请求 ──► HttpConn::read() ──► HttpRequest::parse() ──┐
                                                      │
        ┌─────────────────────────────────────────────┘
        ▼
状态机解析：REQUEST_LINE → HEADERS → BODY → FINISH
        │
        ▼
HttpResponse::Init() ──► 路径解析 ──► 资源定位
        │
        ▼
文件处理：存在性检查 ──► 权限验证 ──► 内存映射
        │
        ▼
响应构建：状态行 ──► 响应头 ──► 响应体
        │
        ▼
HttpConn::write() ──► 数据发送 ──► 连接管理
```

---

## 📨 2. HttpRequest请求解析深度分析

### 2.1 请求解析状态机设计

```cpp
enum PARSE_STATE {
    REQUEST_LINE,    // 解析请求行：GET /index.html HTTP/1.1
    HEADERS,         // 解析请求头：Host: localhost
    BODY,            // 解析请求体：POST数据
    FINISH,          // 解析完成
};
```

**状态机转换图**：
```
开始 ──► REQUEST_LINE ──► HEADERS ──► BODY ──► FINISH
        │              │          │       │
        │              │          │       ▼
        │              │          │    解析完成
        │              │          │
        │              │          ▼
        │              │     POST数据解析
        │              │
        │              ▼
        │          请求头解析循环
        │
        ▼
    请求行解析(方法、路径、版本)
```

### 2.2 HTTP解析主函数详解

```cpp
bool HttpRequest::parse(Buffer& buff) {
    const char CRLF[] = "\r\n";                      // HTTP行分隔符
    if(buff.ReadableBytes() <= 0) {
        return false;                                 // 无数据可解析
    }
    
    while(buff.ReadableBytes() && state_ != FINISH) {
        // 查找行结束符 \r\n
        const char* lineEnd = search(buff.Peek(), buff.BeginWriteConst(), CRLF, CRLF + 2);
        std::string line(buff.Peek(), lineEnd);       // 提取一行数据
        
        switch(state_) {                              // 状态机分发
        case REQUEST_LINE:
            if(!ParseRequestLine_(line)) {
                return false;                         // 请求行解析失败
            }
            ParsePath_();                             // 解析路径
            break;    
        case HEADERS:
            ParseHeader_(line);                       // 解析请求头
            if(buff.ReadableBytes() <= 2) {           // 检查是否到达空行
                state_ = FINISH;
            }
            break;
        case BODY:
            ParseBody_(line);                         // 解析请求体
            break;
        default:
            break;
        }
        
        if(lineEnd == buff.BeginWrite()) { break; }   // 没有找到完整行
        buff.RetrieveUntil(lineEnd + 2);              // 移动读指针
    }
    return true;
}
```

**主人，这个解析函数的精妙设计**：
- **逐行解析**：HTTP协议基于行的特性
- **状态驱动**：根据解析状态分发处理逻辑
- **缓冲区管理**：高效地处理不完整数据包

### 2.3 请求行解析详解

```cpp
bool HttpRequest::ParseRequestLine_(const string& line) {
    // 正则表达式：匹配 "方法 路径 HTTP/版本"
    regex patten("^([^ ]*) ([^ ]*) HTTP/([^ ]*)$");
    smatch subMatch;
    
    if(regex_match(line, subMatch, patten)) {
        method_ = subMatch[1];                        // 提取HTTP方法
        path_ = subMatch[2];                          // 提取请求路径
        version_ = subMatch[3];                       // 提取协议版本
        state_ = HEADERS;                             // 切换到头部解析状态
        return true;
    }
    LOG_ERROR("RequestLine Error");
    return false;
}
```

**正则表达式详解**：
```
^([^ ]*) ([^ ]*) HTTP/([^ ]*)$

解释：
^ ：行开始
([^ ]*) ：第一个捕获组，匹配非空格字符（HTTP方法）
 ：空格分隔符
([^ ]*) ：第二个捕获组，匹配非空格字符（路径）
 HTTP/ ：字面量 "HTTP/"
([^ ]*) ：第三个捕获组，匹配非空格字符（版本号）
$ ：行结束

示例匹配：
GET /index.html HTTP/1.1
POST /login HTTP/1.1
```

### 2.4 请求头解析机制

```cpp
void HttpRequest::ParseHeader_(const string& line) {
    regex patten("^([^:]*): ?(.*)$");                // 匹配 "键: 值"
    smatch subMatch;
    
    if(regex_match(line, subMatch, patten)) {
        header_[subMatch[1]] = subMatch[2];           // 存储键值对
    }
    else {
        state_ = BODY;                                // 遇到空行，切换到BODY状态
    }
}
```

**请求头处理原理**：
```
请求头格式：键: 值

Host: localhost:1316
User-Agent: Mozilla/5.0
Content-Type: application/x-www-form-urlencoded
Content-Length: 25
Connection: keep-alive

空行标志头部结束
```

### 2.5 POST数据解析详解

#### 📌 **URL编码解析**

```cpp
void HttpRequest::ParseFromUrlencoded_() {
    if(body_.size() == 0) { return; }
    
    string key, value;
    int num = 0;
    int n = body_.size();
    int i = 0, j = 0;

    for(; i < n; i++) {
        char ch = body_[i];
        switch (ch) {
        case '=':
            key = body_.substr(j, i - j);              // 提取键
            j = i + 1;
            break;
        case '+':
            body_[i] = ' ';                           // 加号转空格
            break;
        case '%':
            // URL百分号编码解码：%XX -> 字符
            num = ConverHex(body_[i + 1]) * 16 + ConverHex(body_[i + 2]);
            body_[i + 2] = num % 10 + '0';
            body_[i + 1] = num / 10 + '0';
            i += 2;
            break;
        case '&':
            value = body_.substr(j, i - j);            // 提取值
            j = i + 1;
            post_[key] = value;                        // 存储键值对
            break;
        default:
            break;
        }
    }
    
    // 处理最后一个键值对
    if(post_.count(key) == 0 && j < i) {
        value = body_.substr(j, i - j);
        post_[key] = value;
    }
}
```

**URL编码解析示例**：
```
原始POST数据：
username=admin&password=123456&email=test%40example.com

解析过程：
1. username=admin  -> post_["username"] = "admin"
2. password=123456 -> post_["password"] = "123456"  
3. email=test%40example.com -> post_["email"] = "<EMAIL>"
   (%40解码为@字符)
```

#### 📌 **十六进制转换函数**

```cpp
int HttpRequest::ConverHex(char ch) {
    if(ch >= 'A' && ch <= 'F') return ch -'A' + 10;   // A-F -> 10-15
    if(ch >= 'a' && ch <= 'f') return ch -'a' + 10;   // a-f -> 10-15  
    return ch;                                         // 0-9 -> 0-9
}
```

### 2.6 用户验证系统

```cpp
bool HttpRequest::UserVerify(const string &name, const string &pwd, bool isLogin) {
    if(name == "" || pwd == "") { return false; }
    
    MYSQL* sql;
    SqlConnRAII(&sql, SqlConnPool::Instance());        // RAII管理数据库连接
    assert(sql);
    
    bool flag = false;
    char order[256] = { 0 };
    MYSQL_RES *res = nullptr;
    
    if(!isLogin) { flag = true; }                      // 注册时默认成功
    
    // 查询用户及密码
    snprintf(order, 256, "SELECT username, password FROM user WHERE username='%s' LIMIT 1", 
             name.c_str());
    
    if(mysql_query(sql, order)) { 
        mysql_free_result(res);
        return false; 
    }
    
    res = mysql_store_result(sql);                     // 获取查询结果
    
    while(MYSQL_ROW row = mysql_fetch_row(res)) {
        string password(row[1]);
        if(isLogin) {
            if(pwd == password) { flag = true; }       // 登录验证密码
        }
        else {
            flag = false;                              // 注册时用户已存在
        }
    }
    mysql_free_result(res);
    
    // 注册新用户
    if(!isLogin && flag == true) {
        snprintf(order, 256,"INSERT INTO user(username, password) VALUES('%s','%s')", 
                 name.c_str(), pwd.c_str());
        if(mysql_query(sql, order)) { 
            flag = false; 
        }
    }
    return flag;
}
```

**用户验证流程图**：
```
用户提交登录/注册 ──► 解析POST数据 ──► 提取用户名密码
                                    │
                                    ▼
                            查询数据库用户表
                                    │
                          ┌─────────┼─────────┐
                          ▼                   ▼
                      登录模式              注册模式
                          │                   │
                    ┌─────┼─────┐       ┌─────┼─────┐
                    ▼           ▼       ▼           ▼
                用户存在     用户不存在   用户存在    用户不存在
                    │           │       │           │
                    ▼           ▼       ▼           ▼
                验证密码     返回失败   返回失败    插入新用户
                    │                               │
                ┌───┼───┐                          ▼
                ▼       ▼                      返回成功
            密码正确  密码错误
                │       │
                ▼       ▼
            返回成功  返回失败
```

---

## 📤 3. HttpResponse响应生成深度分析

### 3.1 响应生成类设计

```cpp
class HttpResponse {
public:
    HttpResponse();
    ~HttpResponse();

    void Init(const std::string& srcDir, std::string& path, 
              bool isKeepAlive = false, int code = -1);
    void MakeResponse(Buffer& buff);
    void UnmapFile();
    char* File();
    size_t FileLen() const;
    void ErrorContent(Buffer& buff, std::string message);
    int Code() const { return code_; }

private:
    void AddStateLine_(Buffer &buff);              // 添加状态行
    void AddHeader_(Buffer &buff);                 // 添加响应头
    void AddContent_(Buffer &buff);                // 添加响应体

    void ErrorHtml_();                             // 错误页面
    std::string GetFileType_();                    // 获取文件类型

    int code_;                                     // 状态码
    bool isKeepAlive_;                             // 是否保持连接

    std::string path_;                             // 文件路径
    std::string srcDir_;                           // 资源目录
    
    char* mmFile_;                                 // 内存映射文件
    struct stat mmFileStat_;                       // 文件状态信息

    static const std::unordered_map<std::string, std::string> SUFFIX_TYPE;
    static const std::unordered_map<int, std::string> CODE_STATUS;
    static const std::unordered_map<int, std::string> CODE_PATH;
};
```

### 3.2 响应生成主流程

```cpp
void HttpResponse::MakeResponse(Buffer& buff) {
    // 检查文件状态
    if(stat((srcDir_ + path_).data(), &mmFileStat_) < 0 || S_ISDIR(mmFileStat_.st_mode)) {
        code_ = 404;                               // 文件不存在或是目录
    }
    else if(!(mmFileStat_.st_mode & S_IROTH)) {
        code_ = 403;                               // 无读权限
    }
    else if(code_ == -1) { 
        code_ = 200;                               // 默认成功状态
    }
    
    ErrorHtml_();                                  // 处理错误页面
    AddStateLine_(buff);                           // 添加状态行
    AddHeader_(buff);                              // 添加响应头
    AddContent_(buff);                             // 添加响应体
}
```

### 3.3 状态行生成

```cpp
void HttpResponse::AddStateLine_(Buffer& buff) {
    string status;
    if(CODE_STATUS.count(code_) == 1) {
        status = CODE_STATUS.find(code_)->second;   // 查找状态描述
    }
    else {
        code_ = 400;
        status = CODE_STATUS.find(400)->second;
    }
    buff.Append("HTTP/1.1 " + to_string(code_) + " " + status + "\r\n");
}
```

**状态码映射表**：
```cpp
const unordered_map<int, string> HttpResponse::CODE_STATUS = {
    { 200, "OK" },
    { 400, "Bad Request" },
    { 403, "Forbidden" },
    { 404, "Not Found" },
};
```

### 3.4 响应头生成

```cpp
void HttpResponse::AddHeader_(Buffer& buff) {
    buff.Append("Connection: ");
    if(isKeepAlive_) {
        buff.Append("keep-alive\r\n");             // 保持连接
        buff.Append("keep-alive: max=6, timeout=120\r\n");
    } else{
        buff.Append("close\r\n");                  // 关闭连接
    }
    buff.Append("Content-type: " + GetFileType_() + "\r\n");
}
```

### 3.5 文件类型识别

```cpp
string HttpResponse::GetFileType_() {
    string::size_type idx = path_.find_last_of('.');
    if(idx == string::npos) {
        return "text/plain";                       // 默认纯文本
    }
    string suffix = path_.substr(idx);
    if(SUFFIX_TYPE.count(suffix) == 1) {
        return SUFFIX_TYPE.find(suffix)->second;
    }
    return "text/plain";
}
```

**文件类型映射表**：
```cpp
const unordered_map<string, string> HttpResponse::SUFFIX_TYPE = {
    { ".html",  "text/html" },
    { ".xml",   "text/xml" },
    { ".xhtml", "application/xhtml+xml" },
    { ".txt",   "text/plain" },
    { ".rtf",   "application/rtf" },
    { ".pdf",   "application/pdf" },
    { ".word",  "application/nsword" },
    { ".png",   "image/png" },
    { ".gif",   "image/gif" },
    { ".jpg",   "image/jpeg" },
    { ".jpeg",  "image/jpeg" },
    { ".au",    "audio/basic" },
    { ".mpeg",  "video/mpeg" },
    { ".mpg",   "video/mpeg" },
    { ".avi",   "video/x-msvideo" },
    { ".gz",    "application/x-gzip" },
    { ".tar",   "application/x-tar" },
    { ".css",   "text/css "},
    { ".js",    "text/javascript "},
};
```

### 3.6 内存映射文件传输

```cpp
void HttpResponse::AddContent_(Buffer& buff) {
    int srcFd = open((srcDir_ + path_).data(), O_RDONLY);
    if(srcFd < 0) { 
        ErrorContent(buff, "File NotFound!");
        return; 
    }

    // 内存映射文件
    LOG_DEBUG("file path %s", (srcDir_ + path_).data());
    int* mmRet = (int*)mmap(0, mmFileStat_.st_size, 
                           PROT_READ, MAP_PRIVATE, srcFd, 0);
    if(*mmRet == -1) {
        ErrorContent(buff, "File NotFound!");
        return; 
    }
    mmFile_ = (char*)mmRet;
    close(srcFd);
    buff.Append("Content-length: " + to_string(mmFileStat_.st_size) + "\r\n\r\n");
}
```

**内存映射优势**：
- **零拷贝**：文件数据直接映射到进程空间
- **高效传输**：避免用户态内核态数据拷贝
- **系统优化**：利用操作系统的文件缓存

---

## 🔗 4. HttpConn连接管理深度分析

### 4.1 连接类设计

```cpp
class HttpConn {
public:
    HttpConn();
    ~HttpConn();

    void init(int sockFd, const sockaddr_in& addr);
    
    ssize_t read(int* saveErrno);                  // 读取数据
    ssize_t write(int* saveErrno);                 // 发送数据
    
    void Close();                                  // 关闭连接
    
    int GetFd() const;                            // 获取文件描述符
    int GetPort() const;                          // 获取端口
    const char* GetIP() const;                    // 获取IP地址
    sockaddr_in GetAddr() const;                  // 获取地址结构
    
    bool process();                               // 处理HTTP请求

    int ToWriteBytes() { 
        return iov_[0].iov_len + iov_[1].iov_len; 
    }

    bool IsKeepAlive() const {
        return request_.IsKeepAlive();
    }

    static bool isET;                             // 是否边缘触发
    static const char* srcDir;                    // 资源目录
    static std::atomic<int> userCount;            // 用户连接数

private:
    int fd_;                                      // 连接文件描述符
    struct sockaddr_in addr_;                     // 客户端地址

    bool isClose_;                                // 连接是否关闭
    
    int iovCnt_;                                  // IO向量数量
    struct iovec iov_[2];                         // IO向量数组
    
    Buffer readBuff_;                             // 读缓冲区
    Buffer writeBuff_;                            // 写缓冲区

    HttpRequest request_;                         // HTTP请求对象
    HttpResponse response_;                       // HTTP响应对象
};
```

### 4.2 HTTP请求处理核心函数

```cpp
bool HttpConn::process() {
    request_.Init();                              // 初始化请求对象
    if(readBuff_.ReadableBytes() <= 0) {
        return false;                             // 无数据可处理
    }
    else if(request_.parse(readBuff_)) {          // 解析HTTP请求
        LOG_DEBUG("%s", request_.path().c_str());
        response_.Init(srcDir, request_.path(), request_.IsKeepAlive(), 200);
    } else {
        response_.Init(srcDir, request_.path(), false, 400);
    }

    response_.MakeResponse(writeBuff_);           // 生成HTTP响应
    // 响应头
    iov_[0].iov_base = const_cast<char*>(writeBuff_.Peek());
    iov_[0].iov_len = writeBuff_.ReadableBytes();
    iovCnt_ = 1;

    // 文件
    if(response_.FileLen() > 0 && response_.File()) {
        iov_[1].iov_base = response_.File();
        iov_[1].iov_len = response_.FileLen();
        iovCnt_ = 2;
    }
    LOG_DEBUG("filesize:%d, %d  to %d", response_.FileLen(), iovCnt_, ToWriteBytes());
    return true;
}
```

### 4.3 高效读取机制

```cpp
ssize_t HttpConn::read(int* saveErrno) {
    ssize_t len = -1;
    do {
        len = readBuff_.ReadFd(fd_, saveErrno);    // 使用Buffer的readv读取
        if (len <= 0) {
            break;
        }
    } while (isET);                               // ET模式需要读取完所有数据
    return len;
}
```

### 4.4 分散写入机制

```cpp
ssize_t HttpConn::write(int* saveErrno) {
    ssize_t len = -1;
    do {
        len = writev(fd_, iov_, iovCnt_);          // 分散写入
        if(len <= 0) {
            *saveErrno = errno;
            break;
        }
        if(iov_[0].iov_len + iov_[1].iov_len  == 0) { break; } // 传输完成
        else if(static_cast<size_t>(len) > iov_[0].iov_len) {
            iov_[1].iov_base = (uint8_t*) iov_[1].iov_base + (len - iov_[0].iov_len);
            iov_[1].iov_len -= (len - iov_[0].iov_len);
            if(iov_[0].iov_len) {
                writeBuff_.RetrieveAll();
                iov_[0].iov_len = 0;
            }
        }
        else {
            iov_[0].iov_base = (uint8_t*)iov_[0].iov_base + len; 
            iov_[0].iov_len -= len; 
            writeBuff_.Retrieve(len);
        }
    } while(isET || ToWriteBytes() > 10240);
    return len;
}
```

**分散写入的优势**：
```
iov_[0]: HTTP响应头 (存储在writeBuff_中)
iov_[1]: 文件内容    (内存映射的文件数据)

writev一次系统调用发送两部分数据：
┌─────────────┐  ┌─────────────┐
│ HTTP响应头   │  │  文件内容    │
│ (缓冲区)    │  │ (内存映射)   │  ──► 网络
└─────────────┘  └─────────────┘
     iov_[0]          iov_[1]

优势：
1. 减少系统调用次数
2. 避免数据拷贝
3. 提高传输效率
```

---

## 🎯 5. 学习要点总结

主人，通过第五课的学习，您需要重点掌握：

### 5.1 **HTTP协议解析技术**
- ✅ 有限状态机的协议解析设计
- ✅ 正则表达式在文本解析中的应用
- ✅ URL编码解码的完整实现
- ✅ 数据库集成的用户验证系统

### 5.2 **HTTP响应生成机制**
- ✅ 响应头的动态生成策略
- ✅ 文件类型的自动识别机制
- ✅ 内存映射实现零拷贝文件传输
- ✅ 错误处理和状态码管理

### 5.3 **连接管理与优化**
- ✅ Keep-Alive长连接机制
- ✅ 分散读写提高IO效率
- ✅ 缓冲区管理优化内存使用
- ✅ ET模式下的完整数据处理

---

## 📝 课后思考题

1. **为什么HTTP解析要使用状态机而不是一次性解析？**
2. **内存映射相比read()读取文件有什么优势？**
3. **writev()分散写入如何提高网络传输效率？**
4. **如何实现HTTP/2.0协议的解析？**

---

## 🔜 下一课预告

下一课我们将学习 **日志系统模块**，深入分析异步日志的实现原理和性能优化技巧。

主人，第五课就到这里！您对HTTP协议处理还有什么疑问吗？ 📨 