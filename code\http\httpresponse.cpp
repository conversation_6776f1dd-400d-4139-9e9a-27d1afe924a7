/*
 * <AUTHOR> mark
 * @Date         : 2020-06-27
 * @copyleft Apache 2.0
 * 
 * HttpResponse HTTP响应生成器实现
 * 
 * What (是什么)：
 * 完整的HTTP响应生成系统，支持静态文件服务、MIME类型识别、mmap优化、错误处理
 * 
 * Why (为什么)：
 * 1. 提供标准化的HTTP响应生成能力
 * 2. 实现高性能的静态文件服务
 * 3. 支持现代浏览器的文件类型识别
 * 4. 优化内存使用和传输效率
 * 5. 提供用户友好的错误处理
 * 
 * How (怎么做)：
 * 1. 使用内存映射技术实现零拷贝文件传输
 * 2. 自动文件类型检测和MIME设置
 * 3. 完整的HTTP状态码和错误页面支持
 * 4. Keep-Alive连接管理优化
 * 5. 安全的文件访问权限检查
 */ 

#include "httpresponse.h"

using namespace std;

// ==================== 静态配置数据初始化 ====================

/**
 * @brief 文件扩展名到MIME类型映射表
 * What: 根据文件后缀自动识别Content-Type的静态映射表
 * Why:  浏览器需要正确的MIME类型来决定如何处理不同格式的文件
 * How:  预定义常见文件类型的映射关系，支持Web开发中的主要文件格式
 */
const unordered_map<string, string> HttpResponse::SUFFIX_TYPE = {
    { ".html",  "text/html" },              // HTML网页文件
    { ".xml",   "text/xml" },               // XML文档
    { ".xhtml", "application/xhtml+xml" },  // XHTML文档
    { ".txt",   "text/plain" },             // 纯文本文件
    { ".rtf",   "application/rtf" },        // 富文本格式
    { ".pdf",   "application/pdf" },        // PDF文档
    { ".word",  "application/nsword" },     // Word文档
    { ".png",   "image/png" },              // PNG图片
    { ".gif",   "image/gif" },              // GIF图片
    { ".jpg",   "image/jpeg" },             // JPEG图片
    { ".jpeg",  "image/jpeg" },             // JPEG图片（完整扩展名）
    { ".au",    "audio/basic" },            // 音频文件
    { ".mpeg",  "video/mpeg" },             // MPEG视频
    { ".mpg",   "video/mpeg" },             // MPEG视频（简写）
    { ".avi",   "video/x-msvideo" },        // AVI视频
    { ".gz",    "application/x-gzip" },     // Gzip压缩文件
    { ".tar",   "application/x-tar" },      // Tar归档文件
    { ".css",   "text/css "},               // CSS样式表
    { ".js",    "text/javascript "},        // JavaScript脚本
};

/**
 * @brief HTTP状态码到状态描述映射表
 * What: 标准HTTP状态码对应的描述文字
 * Why:  HTTP协议要求状态行包含可读的状态描述
 * How:  实现常用HTTP状态码的标准描述文字
 */
const unordered_map<int, string> HttpResponse::CODE_STATUS = {
    { 200, "OK" },                  // 请求成功
    { 400, "Bad Request" },         // 客户端请求错误
    { 403, "Forbidden" },           // 服务器拒绝访问
    { 404, "Not Found" },           // 请求的资源不存在
};

/**
 * @brief HTTP错误码到错误页面映射表
 * What: 错误状态码对应的专用错误页面路径
 * Why:  为不同错误提供定制化的用户友好页面
 * How:  将HTTP错误码映射到相应的HTML错误页面
 */
const unordered_map<int, string> HttpResponse::CODE_PATH = {
    { 400, "/400.html" },           // 请求错误页面
    { 403, "/403.html" },           // 禁止访问页面
    { 404, "/404.html" },           // 页面未找到
};

// ==================== 构造与析构函数 ====================

/**
 * @brief 默认构造函数
 * What: 初始化HTTP响应对象为无效状态
 * Why:  为对象池模式提供支持，预创建对象后续激活使用
 * How:  设置所有成员变量为默认无效值
 */
HttpResponse::HttpResponse() {
    code_ = -1;                     // 无效状态码
    path_ = srcDir_ = "";           // 空路径
    isKeepAlive_ = false;           // 默认关闭连接
    mmFile_ = nullptr;              // 未映射文件
    mmFileStat_ = { 0 };            // 清零文件状态
};

/**
 * @brief 析构函数
 * What: 清理HTTP响应对象的资源
 * Why:  RAII原则，确保内存映射等系统资源被正确释放
 * How:  调用UnmapFile()释放可能的内存映射
 */
HttpResponse::~HttpResponse() {
    UnmapFile();
}

// ==================== 生命周期管理函数 ====================

/**
 * @brief 初始化HTTP响应对象
 * @param srcDir 静态资源根目录路径
 * @param path 客户端请求的文件路径
 * @param isKeepAlive 是否保持HTTP连接
 * @param code HTTP状态码
 * 
 * What: 设置HTTP响应生成所需的基本参数
 * Why:  准备响应生成过程中需要的所有信息
 * How:  保存参数并重置对象状态以便复用
 */
void HttpResponse::Init(const string& srcDir, string& path, bool isKeepAlive, int code){
    assert(srcDir != "");           // 确保资源目录有效
    
    if(mmFile_) { UnmapFile(); }    // 清理之前的内存映射
    
    code_ = code;                   // 保存HTTP状态码
    isKeepAlive_ = isKeepAlive;     // 保存连接保持标志
    path_ = path;                   // 保存请求路径
    srcDir_ = srcDir;               // 保存资源根目录
    mmFile_ = nullptr;              // 重置文件映射指针
    mmFileStat_ = { 0 };            // 重置文件状态信息
}

// ==================== 响应生成核心函数 ====================

/**
 * @brief 生成完整的HTTP响应
 * @param buff 输出缓冲区，用于存储生成的响应数据
 * 
 * What: HTTP响应生成的主控制流程
 * Why:  将请求信息转换为标准的HTTP响应报文
 * How:  
 * 1. 检查文件存在性和访问权限
 * 2. 根据检查结果设置适当的状态码
 * 3. 依次生成状态行、头部、内容
 */
void HttpResponse::MakeResponse(Buffer& buff) {
    // ==================== 文件状态检查 ====================
    
    // 构造完整文件路径并获取文件状态
    if(stat((srcDir_ + path_).data(), &mmFileStat_) < 0 || S_ISDIR(mmFileStat_.st_mode)) {
        // 文件不存在或者是目录
        code_ = 404;
    }
    else if(!(mmFileStat_.st_mode & S_IROTH)) {
        // 文件存在但没有读权限
        code_ = 403;
    }
    else if(code_ == -1) { 
        // 文件存在且有权限，设置成功状态码
        code_ = 200; 
    }
    
    // ==================== 响应生成流程 ====================
    
    ErrorHtml_();           // 处理错误页面映射
    AddStateLine_(buff);    // 添加HTTP状态行
    AddHeader_(buff);       // 添加HTTP头部
    AddContent_(buff);      // 添加HTTP内容
}

// ==================== 文件访问接口 ====================

/**
 * @brief 获取内存映射的文件内容指针
 * @return char* 文件内容在内存中的地址
 * 
 * What: 返回mmap映射的文件数据指针
 * Why:  支持零拷贝文件传输，直接从内存发送文件内容
 * How:  返回mmFile_成员变量
 */
char* HttpResponse::File() {
    return mmFile_;
}

/**
 * @brief 获取文件大小
 * @return size_t 文件的字节数
 * 
 * What: 返回当前处理文件的大小
 * Why:  HTTP响应需要Content-Length头部指定内容长度
 * How:  返回stat结构中的st_size字段
 */
size_t HttpResponse::FileLen() const {
    return mmFileStat_.st_size;
}

// ==================== 错误处理函数 ====================

/**
 * @brief 错误页面路径映射
 * 
 * What: 将HTTP错误状态码映射到对应的错误页面
 * Why:  为用户提供友好的错误信息页面而不是空白响应
 * How:  查表映射，更新路径并重新获取文件状态
 */
void HttpResponse::ErrorHtml_() {
    if(CODE_PATH.count(code_) == 1) {
        path_ = CODE_PATH.find(code_)->second;          // 更新为错误页面路径
        stat((srcDir_ + path_).data(), &mmFileStat_);   // 获取错误页面文件状态
    }
}

// ==================== HTTP响应组件生成函数 ====================

/**
 * @brief 添加HTTP状态行
 * @param buff 输出缓冲区
 * 
 * What: 生成HTTP响应的第一行：状态行
 * Why:  状态行是HTTP响应的必需部分，标识请求处理结果
 * How:  格式："HTTP/1.1 状态码 状态描述\r\n"
 * 
 * ==================== 详细解释 ====================
 * 
 * 主人，让我来详细为您解释这个函数的每一个细节：
 * 
 * 【函数作用】
 * 这个函数负责生成HTTP响应的状态行，它是HTTP响应的第一行，格式固定为：
 * "HTTP/1.1 状态码 状态描述\r\n"
 * 
 * 比如：
 * - "HTTP/1.1 200 OK\r\n"     （成功响应）
 * - "HTTP/1.1 404 Not Found\r\n" （文件未找到）
 * - "HTTP/1.1 500 Internal Server Error\r\n" （服务器错误）
 * 
 * 【核心逻辑分析】
 * 1. 首先声明一个string类型的status变量来存储状态描述
 * 2. 通过CODE_STATUS这个映射表查找当前状态码对应的描述文字
 * 3. 如果找到了，就使用对应的描述；如果没找到，默认设为400错误
 * 4. 最后将完整的状态行写入缓冲区
 * 
 * 【C++知识点详解】
 * 
 * 1. 成员变量code_：
 *    - 这是HttpResponse类的私有成员，存储当前响应的状态码
 *    - 比如200表示成功，404表示未找到，500表示服务器错误
 * 
 * 2. CODE_STATUS容器操作：
 *    - CODE_STATUS应该是一个map<int, string>类型的静态映射表
 *    - count(code_)方法：检查映射表中是否存在这个键值
 *    - find(code_)->second：找到对应的键值对，取其值部分（状态描述）
 * 
 * 3. string字符串拼接：
 *    - "HTTP/1.1 " + to_string(code_) + " " + status + "\r\n"
 *    - to_string()：C++11标准库函数，将数字转换为字符串
 *    - +操作符：string类重载的连接操作符
 *    - \r\n：HTTP协议规定的行结束符（回车+换行）
 * 
 * 4. Buffer类的Append方法：
 *    - 这是项目中自定义的缓冲区类
 *    - Append方法将字符串追加到缓冲区末尾
 * 
 * 【设计思路】
 * 这个函数设计得很巧妙：
 * - 使用映射表而不是switch-case，代码更简洁
 * - 有容错机制：未知状态码默认改为400
 * - 严格遵循HTTP协议格式
 * 
 * 【可能的改进】
 * 可以考虑使用unordered_map而不是map来提高查找效率，但对于状态码这种
 * 数量有限的场景，性能差异微乎其微。
 */
void HttpResponse::AddStateLine_(Buffer& buff) {
    string status;
    
    // 查找状态码对应的描述文字
    // CODE_STATUS是一个映射表，存储状态码->状态描述的对应关系
    if(CODE_STATUS.count(code_) == 1) {
        // 找到了对应的状态码，获取其描述文字
        // find()返回迭代器，->second取得映射的值部分
        status = CODE_STATUS.find(code_)->second;
    }
    else {
        // 未知状态码的容错处理：默认使用400错误
        // 这样确保即使传入非标准状态码，也能正常生成响应
        code_ = 400;
        status = CODE_STATUS.find(400)->second;
    }
    
    // 生成完整的HTTP状态行
    // 格式：协议版本 + 空格 + 状态码 + 空格 + 状态描述 + CRLF
    // 例如："HTTP/1.1 200 OK\r\n"
    buff.Append("HTTP/1.1 " + to_string(code_) + " " + status + "\r\n");
}

/**
 * @brief 添加HTTP响应头部
 * @param buff 输出缓冲区
 * 
 * 【这段代码在干什么？】
 * 这个函数就像在给一个包裹贴标签一样！
 * 当我们要把网页内容发给浏览器时，不能光发内容，还要告诉浏览器：
 * "这个包裹里装的是什么？""你收到后要不要保持联系？"
 * 
 * 【为什么需要这些头部信息？】
 * 就像寄快递一样，包裹上要写清楚：
 * - 收件人地址（这里是Connection：告诉浏览器连接方式）
 * - 包裹内容说明（这里是Content-Type：告诉浏览器文件类型）
 * 
 * 【这些头部具体有什么用？】
 * Connection头部：就像问"要不要继续聊天？"
 * Content-Type头部：就像告诉对方"我发给你的是图片还是网页"
 */
void HttpResponse::AddHeader_(Buffer& buff) {
    
    // =============== 第一步：添加Connection头部 ===============
    
    // 先写"Connection: "这个标签
    buff.Append("Connection: ");
    
    if(isKeepAlive_) {
        // 如果要保持长连接（就像保持电话不挂断）
        
        // 告诉浏览器："咱们保持连接，别挂断！"
        buff.Append("keep-alive\r\n");
        
        // 详细说明连接规则："最多保持6个连接，超时时间120秒"
        // 就像说："我们可以同时聊6个话题，但如果120秒没说话就挂断"
        buff.Append("keep-alive: max=6, timeout=120\r\n");
    } else{
        // 如果用短连接（用完就挂断）
        
        // 告诉浏览器："发完这个就挂断电话吧"
        buff.Append("close\r\n");
    }
    
    // =============== 第二步：添加Content-Type头部 ===============
    
    // 告诉浏览器这个文件是什么类型的
    // GetFileType_()会根据文件扩展名判断类型
    // 比如：.html -> "text/html"，.jpg -> "image/jpeg"
    // 就像告诉对方："我发给你的是一张图片"或"我发给你的是一个网页"
    buff.Append("Content-type: " + GetFileType_() + "\r\n");
    
    /*
     * 【小知识】\r\n的含义：
     * \r = 回车（Carriage Return）
     * \n = 换行（Line Feed）  
     * HTTP协议规定每一行都要用\r\n结尾，就像写信时每句话后面要换行一样
     * 
     * 【为什么要分这两个头部？】
     * 1. Connection：管理连接状态，影响性能
     *    - keep-alive：一个连接可以发送多个请求，减少建立连接的开销
     *    - close：每次请求都要重新建立连接，更稳定但效率低
     * 
     * 2. Content-Type：告诉浏览器如何解释内容
     *    - text/html：当作网页显示
     *    - image/jpeg：当作图片显示
     *    - application/json：当作JSON数据处理
     * 
     * 【这就像寄快递时】：
     * Connection = 选择快递公司的服务类型（普通/特快）
     * Content-Type = 标注包裹内容（易碎品/食品/文件）
     */
}

/**
 * @brief 添加HTTP响应内容
 * @param buff 输出缓冲区
 * 
 * What: 处理HTTP响应的主体内容（通常是文件）
 * Why:  响应体包含客户端请求的实际资源数据
 * How:  
 * 1. 打开请求的文件
 * 2. 使用mmap将文件映射到内存
 * 3. 添加Content-Length头部
 * 4. 文件内容通过iovec在writev中发送
 */
void HttpResponse::AddContent_(Buffer& buff) {
    // 以只读模式打开文件
    int srcFd = open((srcDir_ + path_).data(), O_RDONLY);
    if(srcFd < 0) { 
        ErrorContent(buff, "File NotFound!");
        return; 
    }

    // ==================== 内存映射优化 ====================
    
    /* 
     * 使用mmap将文件映射到内存空间：
     * - 提高文件访问速度，避免频繁的read()系统调用
     * - MAP_PRIVATE：创建写时拷贝的私有映射，保护原文件
     * - PROT_READ：映射区域只读，符合文件服务的需求
     */
    LOG_DEBUG("file path %s", (srcDir_ + path_).data());
    int* mmRet = (int*)mmap(0, mmFileStat_.st_size, PROT_READ, MAP_PRIVATE, srcFd, 0);
    if(*mmRet == -1) {
        ErrorContent(buff, "File NotFound!");
        return; 
    }
    
    mmFile_ = (char*)mmRet;     // 保存映射地址
    close(srcFd);               // 关闭文件描述符（映射后不再需要）
    
    // 添加Content-Length头部，指定响应体长度
    buff.Append("Content-length: " + to_string(mmFileStat_.st_size) + "\r\n\r\n");
}

/**
 * @brief 解除文件内存映射
 * 
 * What: 释放mmap创建的内存映射
 * Why:  避免内存泄漏，虚拟内存地址空间有限
 * How:  调用munmap()系统调用释放映射区域
 */
void HttpResponse::UnmapFile() {
    if(mmFile_) {
        munmap(mmFile_, mmFileStat_.st_size);   // 解除内存映射
        mmFile_ = nullptr;                      // 重置指针
    }
}

// ==================== MIME类型识别函数 ====================

/**
 * @brief 根据文件扩展名获取MIME类型
 * @return string 对应的MIME类型字符串
 * 
 * What: 自动识别文件类型并返回相应的MIME类型
 * Why:  浏览器需要正确的Content-Type来决定如何处理文件
 * How:  
 * 1. 提取文件路径中的扩展名
 * 2. 在MIME映射表中查找对应类型
 * 3. 未找到时返回默认的text/plain类型
 */
string HttpResponse::GetFileType_() {
    // 查找文件名中最后一个点的位置
    string::size_type idx = path_.find_last_of('.');
    if(idx == string::npos) {
        return "text/plain";        // 无扩展名，返回纯文本类型
    }
    
    // 提取扩展名（包含点号）
    string suffix = path_.substr(idx);
    
    // 在MIME映射表中查找
    if(SUFFIX_TYPE.count(suffix) == 1) {
        return SUFFIX_TYPE.find(suffix)->second;
    }
    
    return "text/plain";            // 未知类型，返回纯文本类型
}

// ==================== 错误响应生成函数 ====================

/**
 * @brief 生成错误响应内容
 * @param buff 输出缓冲区
 * @param message 错误信息文字
 * 
 * What: 生成HTML格式的错误响应页面
 * Why:  当文件不存在或其他错误时，向用户显示友好的错误信息
 * How:  构造简单的HTML页面，包含错误码和错误信息
 */
void HttpResponse::ErrorContent(Buffer& buff, string message) 
{
    string body;
    string status;
    
    // 构造HTML错误页面内容
    body += "<html><title>Error</title>";
    body += "<body bgcolor=\"ffffff\">";
    
    // 获取状态码描述
    if(CODE_STATUS.count(code_) == 1) {
        status = CODE_STATUS.find(code_)->second;
    } else {
        status = "Bad Request";
    }
    
    // 添加错误信息
    body += to_string(code_) + " : " + status  + "\n";
    body += "<p>" + message + "</p>";
    body += "<hr><em>TinyWebServer</em></body></html>";

    // 添加Content-Length和响应体
    buff.Append("Content-length: " + to_string(body.size()) + "\r\n\r\n");
    buff.Append(body);
}
