# 第二周详细学习计划：网络编程入门

> **主人，第二周我们要从零开始掌握网络编程！我会从最基础的概念开始，手把手教您写出完整的网络服务器！**

## 📅 第二周时间安排

### Day 8: 网络编程理论基础

#### 上午任务 (3小时) - 理论学习
**1. 网络基础概念理解**

```cpp
// 创建文件：day8_network_basics.cpp
#include <iostream>
#include <string>

/**
 * 主人，我们先理解网络编程的基本概念
 * 就像寄信一样简单！
 */

// 任务1：理解网络通信的基本概念
void explain_network_concepts() {
    std::cout << "=== 网络通信就像寄信 ===" << std::endl;
    std::cout << R"(
    寄信过程                    网络通信
    ────────────────────────────────────────
    写信内容          →         准备数据
    写收件人地址      →         指定IP地址
    写门牌号          →         指定端口号
    投递到邮箱        →         发送到网络
    邮递员送信        →         网络传输
    收件人收信        →         目标程序接收
    )" << std::endl;
}

// 任务2：理解IP地址和端口的概念
void explain_ip_and_port() {
    std::cout << "\n=== IP地址和端口 ===" << std::endl;
    std::cout << R"(
    IP地址就像小区地址：
    ************* → 告诉邮递员到哪个小区
    
    端口就像门牌号：
    8080 → 告诉邮递员具体哪一户
    
    完整地址：*************:8080
    就像：某某小区100号楼8080室
    )" << std::endl;
    
    // 常见端口示例
    std::cout << "常见端口用途：" << std::endl;
    std::cout << "80    - HTTP网站服务" << std::endl;
    std::cout << "443   - HTTPS安全网站" << std::endl;
    std::cout << "22    - SSH远程登录" << std::endl;
    std::cout << "3306  - MySQL数据库" << std::endl;
    std::cout << "8080  - 我们的测试服务器" << std::endl;
}

// 任务3：理解TCP和UDP的区别
void explain_tcp_udp() {
    std::cout << "\n=== TCP vs UDP ===" << std::endl;
    std::cout << R"(
    TCP（可靠传输）：
    ├── 就像挂号信：保证送达，有回执
    ├── 数据不会丢失
    ├── 数据顺序正确
    └── 速度稍慢，但可靠
    
    UDP（快速传输）：
    ├── 就像普通信件：快速，但可能丢失
    ├── 数据可能丢失
    ├── 数据可能乱序
    └── 速度很快，适合游戏、视频
    
    我们的WebServer使用TCP，因为HTTP需要可靠传输！
    )" << std::endl;
}

int main() {
    explain_network_concepts();
    explain_ip_and_port();
    explain_tcp_udp();
    return 0;
}
```

**2. Socket编程基础理论**

```cpp
// 创建文件：day8_socket_theory.cpp
#include <iostream>

// 任务4：理解Socket的本质
void explain_socket_concept() {
    std::cout << "=== Socket是什么？ ===" << std::endl;
    std::cout << R"(
    Socket就像电话：
    ├── socket()     → 买一部电话机
    ├── bind()       → 申请一个电话号码
    ├── listen()     → 开机等待来电
    ├── accept()     → 接听电话
    ├── read/write() → 通话内容
    └── close()      → 挂断电话
    )" << std::endl;
}

// 任务5：理解服务器和客户端的角色
void explain_client_server() {
    std::cout << "\n=== 客户端 vs 服务器 ===" << std::endl;
    std::cout << R"(
    服务器（Server）：
    ├── 就像商店：固定地址，等待顾客
    ├── 使用 bind() 绑定固定地址
    ├── 使用 listen() 等待连接
    ├── 使用 accept() 接待客户
    └── 可以同时服务多个客户
    
    客户端（Client）：
    ├── 就像顾客：主动找商店
    ├── 使用 connect() 连接服务器
    ├── 发送请求，接收响应
    └── 通常一次只连一个服务器
    )" << std::endl;
}

// 任务6：理解网络字节序
void explain_byte_order() {
    std::cout << "\n=== 网络字节序 ===" << std::endl;
    std::cout << R"(
    为什么需要字节序转换？
    
    不同电脑存储数字的方式不同：
    数字 0x12345678 的存储：
    
    大端序（Big Endian）：    12 34 56 78
    小端序（Little Endian）： 78 56 34 12
    
    网络统一使用大端序，所以需要转换：
    ├── htons() → 主机序转网络序（16位）
    ├── htonl() → 主机序转网络序（32位）
    ├── ntohs() → 网络序转主机序（16位）
    └── ntohl() → 网络序转主机序（32位）
    )" << std::endl;
}

int main() {
    explain_socket_concept();
    explain_client_server();
    explain_byte_order();
    return 0;
}
```

#### 下午任务 (3小时) - 基础练习

**3. 第一个Socket程序 - 获取本机信息**

```cpp
// 创建文件：day8_first_socket.cpp
#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>

// 练习1：创建和关闭socket
void practice_socket_creation() {
    std::cout << "=== 练习：创建Socket ===" << std::endl;
    
    // 创建TCP socket
    int sock_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (sock_fd < 0) {
        std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
        return;
    }
    
    std::cout << "✓ 成功创建TCP socket，文件描述符：" << sock_fd << std::endl;
    
    // 创建UDP socket
    int udp_fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (udp_fd < 0) {
        std::cerr << "创建UDP socket失败: " << strerror(errno) << std::endl;
    } else {
        std::cout << "✓ 成功创建UDP socket，文件描述符：" << udp_fd << std::endl;
        close(udp_fd);
    }
    
    // 关闭socket
    close(sock_fd);
    std::cout << "✓ Socket已关闭" << std::endl;
}

// 练习2：设置socket选项
void practice_socket_options() {
    std::cout << "\n=== 练习：Socket选项 ===" << std::endl;
    
    int sock_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (sock_fd < 0) {
        std::cerr << "创建socket失败" << std::endl;
        return;
    }
    
    // 设置地址复用（很重要！）
    int opt = 1;
    if (setsockopt(sock_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        std::cerr << "设置SO_REUSEADDR失败: " << strerror(errno) << std::endl;
    } else {
        std::cout << "✓ 设置地址复用成功" << std::endl;
    }
    
    // 获取socket类型
    int sock_type;
    socklen_t opt_len = sizeof(sock_type);
    if (getsockopt(sock_fd, SOL_SOCKET, SO_TYPE, &sock_type, &opt_len) == 0) {
        std::cout << "✓ Socket类型：" << (sock_type == SOCK_STREAM ? "TCP" : "UDP") << std::endl;
    }
    
    close(sock_fd);
}

// 练习3：字节序转换实验
void practice_byte_order() {
    std::cout << "\n=== 练习：字节序转换 ===" << std::endl;
    
    uint16_t port = 8080;
    uint32_t ip = 0x7F000001;  // 127.0.0.1
    
    std::cout << "原始端口号：" << port << std::endl;
    std::cout << "网络字节序：" << htons(port) << std::endl;
    std::cout << "转换回来：" << ntohs(htons(port)) << std::endl;
    
    std::cout << "\n原始IP（16进制）：0x" << std::hex << ip << std::dec << std::endl;
    std::cout << "网络字节序：0x" << std::hex << htonl(ip) << std::dec << std::endl;
    
    // IP地址字符串转换
    struct in_addr addr;
    addr.s_addr = htonl(ip);
    std::cout << "IP地址字符串：" << inet_ntoa(addr) << std::endl;
}

int main() {
    practice_socket_creation();
    practice_socket_options();
    practice_byte_order();
    return 0;
}
```

**当日作业：**
1. 运行所有练习代码，理解每个概念
2. 画一张网络通信的流程图
3. 用自己的话解释TCP和UDP的区别

---

### Day 9: 最简单的Echo服务器（版本1）

#### 上午任务 (3小时) - 服务器端开发

**1. 一步步实现TCP服务器**

```cpp
// 创建文件：day9_simple_server.cpp
#include <iostream>
#include <string>
#include <cstring>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <errno.h>

class SimpleEchoServer {
public:
    // 步骤1：创建socket
    bool create_socket() {
        std::cout << "步骤1：创建socket" << std::endl;
        
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            std::cerr << "❌ socket()失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ socket创建成功，fd = " << listen_fd_ << std::endl;
        return true;
    }
    
    // 步骤2：设置socket选项
    bool set_socket_options() {
        std::cout << "\n步骤2：设置socket选项" << std::endl;
        
        int opt = 1;
        if (setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
            std::cerr << "❌ setsockopt()失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ 地址复用设置成功" << std::endl;
        return true;
    }
    
    // 步骤3：绑定地址
    bool bind_address(int port) {
        std::cout << "\n步骤3：绑定地址和端口" << std::endl;
        
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        
        server_addr.sin_family = AF_INET;           // IPv4
        server_addr.sin_addr.s_addr = INADDR_ANY;   // 所有网卡
        server_addr.sin_port = htons(port);         // 端口转网络字节序
        
        if (bind(listen_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            std::cerr << "❌ bind()失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ 绑定地址成功: 0.0.0.0:" << port << std::endl;
        return true;
    }
    
    // 步骤4：开始监听
    bool start_listen() {
        std::cout << "\n步骤4：开始监听" << std::endl;
        
        const int BACKLOG = 128;  // 等待队列长度
        if (listen(listen_fd_, BACKLOG) < 0) {
            std::cerr << "❌ listen()失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ 开始监听，等待连接..." << std::endl;
        return true;
    }
    
    // 步骤5：接受连接
    int accept_connection() {
        std::cout << "\n等待客户端连接..." << std::endl;
        
        struct sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);
        
        int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
        if (client_fd < 0) {
            std::cerr << "❌ accept()失败: " << strerror(errno) << std::endl;
            return -1;
        }
        
        // 显示客户端信息
        char client_ip[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
        uint16_t client_port = ntohs(client_addr.sin_port);
        
        std::cout << "✓ 新客户端连接: " << client_ip << ":" << client_port 
                  << " (fd=" << client_fd << ")" << std::endl;
        
        return client_fd;
    }
    
    // 步骤6：处理客户端数据
    void handle_client(int client_fd) {
        std::cout << "\n开始处理客户端数据..." << std::endl;
        
        char buffer[1024];
        
        while (true) {
            // 读取客户端数据
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read <= 0) {
                if (bytes_read == 0) {
                    std::cout << "客户端主动断开连接" << std::endl;
                } else {
                    std::cerr << "read()失败: " << strerror(errno) << std::endl;
                }
                break;
            }
            
            // 添加字符串结束符
            buffer[bytes_read] = '\0';
            std::cout << "收到数据 (" << bytes_read << " 字节): " << buffer;
            
            // 回显给客户端
            ssize_t bytes_written = write(client_fd, buffer, bytes_read);
            if (bytes_written < 0) {
                std::cerr << "write()失败: " << strerror(errno) << std::endl;
                break;
            }
            
            std::cout << "回显完成 (" << bytes_written << " 字节)" << std::endl;
        }
    }
    
    // 启动服务器的完整流程
    void start(int port) {
        std::cout << "=== 启动Echo服务器 ===" << std::endl;
        
        if (!create_socket() || 
            !set_socket_options() || 
            !bind_address(port) || 
            !start_listen()) {
            cleanup();
            return;
        }
        
        std::cout << "\n🚀 服务器启动成功！监听端口 " << port << std::endl;
        std::cout << "提示：可以用 'telnet localhost " << port << "' 测试" << std::endl;
        
        // 主循环：接受连接并处理
        while (true) {
            int client_fd = accept_connection();
            if (client_fd < 0) {
                continue;
            }
            
            handle_client(client_fd);
            close(client_fd);
            std::cout << "客户端连接已关闭\n" << std::endl;
        }
    }
    
    void cleanup() {
        if (listen_fd_ >= 0) {
            close(listen_fd_);
            std::cout << "服务器socket已关闭" << std::endl;
        }
    }
    
    ~SimpleEchoServer() {
        cleanup();
    }
    
private:
    int listen_fd_ = -1;
};

int main() {
    SimpleEchoServer server;
    server.start(8080);
    return 0;
}
```

#### 下午任务 (3小时) - 客户端开发和测试

**2. 编写测试客户端**

```cpp
// 创建文件：day9_simple_client.cpp
#include <iostream>
#include <string>
#include <cstring>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>

class SimpleEchoClient {
public:
    bool connect_to_server(const std::string& server_ip, int port) {
        std::cout << "连接到服务器 " << server_ip << ":" << port << std::endl;
        
        // 创建socket
        sock_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (sock_fd_ < 0) {
            std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        // 设置服务器地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        
        if (inet_pton(AF_INET, server_ip.c_str(), &server_addr.sin_addr) <= 0) {
            std::cerr << "IP地址无效: " << server_ip << std::endl;
            return false;
        }
        
        // 连接服务器
        if (connect(sock_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            std::cerr << "连接失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ 连接成功！" << std::endl;
        return true;
    }
    
    void interactive_mode() {
        std::cout << "\n=== 交互模式 ===" << std::endl;
        std::cout << "输入消息发送给服务器（输入'quit'退出）：" << std::endl;
        
        std::string input;
        char buffer[1024];
        
        while (true) {
            std::cout << ">> ";
            std::getline(std::cin, input);
            
            if (input == "quit") {
                break;
            }
            
            // 发送数据
            ssize_t bytes_sent = send(sock_fd_, input.c_str(), input.length(), 0);
            if (bytes_sent < 0) {
                std::cerr << "发送失败: " << strerror(errno) << std::endl;
                break;
            }
            
            // 接收回显
            ssize_t bytes_received = recv(sock_fd_, buffer, sizeof(buffer) - 1, 0);
            if (bytes_received <= 0) {
                std::cerr << "接收失败或连接断开" << std::endl;
                break;
            }
            
            buffer[bytes_received] = '\0';
            std::cout << "<< " << buffer << std::endl;
        }
    }
    
    void cleanup() {
        if (sock_fd_ >= 0) {
            close(sock_fd_);
        }
    }
    
    ~SimpleEchoClient() {
        cleanup();
    }
    
private:
    int sock_fd_ = -1;
};

int main() {
    SimpleEchoClient client;
    
    if (client.connect_to_server("127.0.0.1", 8080)) {
        client.interactive_mode();
    }
    
    return 0;
}
```

**3. 测试脚本和调试技巧**

```bash
# 创建文件：day9_test.sh
#!/bin/bash

echo "=== Day 9 Echo服务器测试 ==="

# 编译程序
echo "编译服务器和客户端..."
g++ -o day9_server day9_simple_server.cpp
g++ -o day9_client day9_simple_client.cpp

if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
else
    echo "❌ 编译失败"
    exit 1
fi

echo ""
echo "测试方法："
echo "1. 运行服务器: ./day9_server"
echo "2. 在另一个终端运行客户端: ./day9_client"
echo "3. 或者使用telnet测试: telnet localhost 8080"
echo "4. 或者使用netcat测试: echo 'hello' | nc localhost 8080"

echo ""
echo "调试技巧："
echo "1. 使用lsof查看端口占用: lsof -i :8080"
echo "2. 使用netstat查看连接: netstat -an | grep 8080"
echo "3. 使用tcpdump抓包: sudo tcpdump -i lo -X port 8080"
```

**当日作业：**
1. 编译并运行服务器和客户端
2. 用telnet和netcat测试连接
3. 思考：为什么服务器一次只能处理一个客户端？

---

### Day 10: 多客户端支持（版本2）

#### 上午任务 (3小时) - 多进程版本

```cpp
// 创建文件：day10_multiprocess_server.cpp
#include <iostream>
#include <string>
#include <cstring>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>

class MultiProcessEchoServer {
public:
    MultiProcessEchoServer() {
        // 处理子进程退出信号，避免僵尸进程
        signal(SIGCHLD, handle_sigchld);
        signal(SIGPIPE, SIG_IGN);  // 忽略SIGPIPE信号
    }
    
    // 信号处理函数：清理僵尸进程
    static void handle_sigchld(int sig) {
        // 清理所有已退出的子进程
        while (waitpid(-1, nullptr, WNOHANG) > 0) {
            std::cout << "清理了一个子进程" << std::endl;
        }
    }
    
    bool initialize(int port) {
        // 创建监听socket
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            std::cerr << "socket创建失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        // 设置地址复用
        int opt = 1;
        setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 绑定地址
        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = INADDR_ANY;
        addr.sin_port = htons(port);
        
        if (bind(listen_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            std::cerr << "bind失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        if (listen(listen_fd_, 128) < 0) {
            std::cerr << "listen失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ 多进程服务器初始化成功，监听端口 " << port << std::endl;
        return true;
    }
    
    void start() {
        std::cout << "🚀 多进程Echo服务器启动！" << std::endl;
        std::cout << "支持多个客户端同时连接" << std::endl;
        
        while (true) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
            if (client_fd < 0) {
                if (errno == EINTR) {
                    continue;  // 被信号中断，继续
                }
                std::cerr << "accept失败: " << strerror(errno) << std::endl;
                continue;
            }
            
            // 显示客户端信息
            char client_ip[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
            uint16_t client_port = ntohs(client_addr.sin_port);
            
            std::cout << "✓ 新连接: " << client_ip << ":" << client_port 
                      << " (fd=" << client_fd << ")" << std::endl;
            
            // 创建子进程处理客户端
            pid_t pid = fork();
            if (pid == 0) {
                // 子进程：处理客户端
                close(listen_fd_);  // 子进程不需要监听socket
                handle_client(client_fd, client_ip, client_port);
                close(client_fd);
                exit(0);  // 子进程退出
            } else if (pid > 0) {
                // 父进程：继续接受新连接
                close(client_fd);  // 父进程不需要客户端socket
                std::cout << "创建子进程 " << pid << " 处理客户端" << std::endl;
            } else {
                // fork失败
                std::cerr << "fork失败: " << strerror(errno) << std::endl;
                close(client_fd);
            }
        }
    }
    
private:
    void handle_client(int client_fd, const char* client_ip, uint16_t client_port) {
        std::cout << "[进程 " << getpid() << "] 开始处理客户端 " 
                  << client_ip << ":" << client_port << std::endl;
        
        char buffer[1024];
        
        while (true) {
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read <= 0) {
                if (bytes_read == 0) {
                    std::cout << "[进程 " << getpid() << "] 客户端断开连接" << std::endl;
                } else {
                    std::cerr << "[进程 " << getpid() << "] 读取失败: " 
                              << strerror(errno) << std::endl;
                }
                break;
            }
            
            buffer[bytes_read] = '\0';
            
            // 添加进程信息到回显
            std::string response = "[Echo from process " + std::to_string(getpid()) + 
                                  "] " + std::string(buffer);
            
            ssize_t bytes_written = write(client_fd, response.c_str(), response.length());
            if (bytes_written < 0) {
                std::cerr << "[进程 " << getpid() << "] 发送失败: " 
                          << strerror(errno) << std::endl;
                break;
            }
            
            std::cout << "[进程 " << getpid() << "] 处理了 " << bytes_read 
                      << " 字节数据" << std::endl;
        }
        
        std::cout << "[进程 " << getpid() << "] 客户端处理完毕" << std::endl;
    }
    
    int listen_fd_ = -1;
};

int main() {
    MultiProcessEchoServer server;
    
    if (server.initialize(8080)) {
        server.start();
    }
    
    return 0;
}
```

#### 下午任务 (3小时) - 多客户端测试工具

```cpp
// 创建文件：day10_multi_client_test.cpp
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>

class MultiClientTester {
public:
    // 单个客户端的测试函数
    static void client_worker(int client_id, const std::string& server_ip, int port) {
        std::cout << "客户端 " << client_id << " 开始连接" << std::endl;
        
        // 创建socket
        int sock_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (sock_fd < 0) {
            std::cerr << "客户端 " << client_id << " 创建socket失败" << std::endl;
            return;
        }
        
        // 连接服务器
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        inet_pton(AF_INET, server_ip.c_str(), &server_addr.sin_addr);
        
        if (connect(sock_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            std::cerr << "客户端 " << client_id << " 连接失败: " 
                      << strerror(errno) << std::endl;
            close(sock_fd);
            return;
        }
        
        std::cout << "✓ 客户端 " << client_id << " 连接成功" << std::endl;
        
        // 发送多条消息
        for (int i = 0; i < 5; ++i) {
            std::string message = "Message " + std::to_string(i) + 
                                 " from client " + std::to_string(client_id);
            
            // 发送消息
            send(sock_fd, message.c_str(), message.length(), 0);
            
            // 接收回显
            char buffer[1024];
            ssize_t bytes_received = recv(sock_fd, buffer, sizeof(buffer) - 1, 0);
            if (bytes_received > 0) {
                buffer[bytes_received] = '\0';
                std::cout << "客户端 " << client_id << " 收到: " << buffer << std::endl;
            }
            
            // 短暂延迟
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        
        close(sock_fd);
        std::cout << "客户端 " << client_id << " 断开连接" << std::endl;
    }
    
    // 启动多个客户端进行测试
    static void run_multi_client_test(int num_clients) {
        std::cout << "=== 启动 " << num_clients << " 个客户端测试 ===" << std::endl;
        
        std::vector<std::thread> clients;
        
        // 创建多个客户端线程
        for (int i = 0; i < num_clients; ++i) {
            clients.emplace_back(client_worker, i + 1, "127.0.0.1", 8080);
            
            // 间隔启动，避免连接过于密集
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 等待所有客户端完成
        for (auto& client : clients) {
            client.join();
        }
        
        std::cout << "所有客户端测试完成" << std::endl;
    }
};

int main() {
    std::cout << "多客户端测试工具" << std::endl;
    std::cout << "确保服务器已经启动在端口8080" << std::endl;
    
    int num_clients;
    std::cout << "请输入客户端数量: ";
    std::cin >> num_clients;
    
    if (num_clients > 0 && num_clients <= 100) {
        MultiClientTester::run_multi_client_test(num_clients);
    } else {
        std::cout << "客户端数量应该在1-100之间" << std::endl;
    }
    
    return 0;
}
```

**当日作业：**
1. 测试多进程服务器的并发能力
2. 使用ps命令观察子进程的创建和销毁
3. 思考多进程模型的优缺点

---

### Day 11: 非阻塞I/O与select

#### 上午任务 (3小时) - 理解阻塞vs非阻塞

**1. 阻塞I/O的问题演示**

```cpp
// 创建文件：day11_blocking_demo.cpp
#include <iostream>
#include <sys/socket.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>

// 演示阻塞I/O的问题
void demonstrate_blocking_problem() {
    std::cout << "=== 阻塞I/O问题演示 ===" << std::endl;
    std::cout << R"(
    阻塞I/O就像排队买票：
    ├── 一个窗口一次只能服务一个人
    ├── 后面的人必须等前面的人买完
    ├── 如果前面的人很慢，所有人都要等
    └── 效率很低！
    
    非阻塞I/O就像预约制：
    ├── 可以同时处理多个预约
    ├── 没空的时候立即告诉你"稍后再试"
    ├── 不会傻等，可以去处理其他事情
    └── 效率很高！
    )" << std::endl;
}

// 设置socket为非阻塞模式
void set_nonblocking(int fd) {
    int flags = fcntl(fd, F_GETFL, 0);
    if (flags == -1) {
        std::cerr << "获取文件状态失败" << std::endl;
        return;
    }
    
    flags |= O_NONBLOCK;
    if (fcntl(fd, F_SETFL, flags) == -1) {
        std::cerr << "设置非阻塞失败" << std::endl;
        return;
    }
    
    std::cout << "✓ 设置socket为非阻塞模式" << std::endl;
}

// 测试非阻塞读取
void test_nonblocking_read() {
    std::cout << "\n=== 非阻塞读取测试 ===" << std::endl;
    
    int fds[2];
    if (pipe(fds) == -1) {
        std::cerr << "创建管道失败" << std::endl;
        return;
    }
    
    // 设置读端为非阻塞
    set_nonblocking(fds[0]);
    
    char buffer[100];
    
    // 尝试从空管道读取
    ssize_t result = read(fds[0], buffer, sizeof(buffer));
    if (result == -1) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            std::cout << "✓ 非阻塞读取：暂时没有数据可读" << std::endl;
        } else {
            std::cerr << "读取出错: " << strerror(errno) << std::endl;
        }
    }
    
    // 写入一些数据再读取
    write(fds[1], "Hello", 5);
    result = read(fds[0], buffer, sizeof(buffer));
    if (result > 0) {
        buffer[result] = '\0';
        std::cout << "✓ 非阻塞读取成功: " << buffer << std::endl;
    }
    
    close(fds[0]);
    close(fds[1]);
}

int main() {
    demonstrate_blocking_problem();
    test_nonblocking_read();
    return 0;
}
```

#### 下午任务 (3小时) - select多路复用

**2. select基础使用**

```cpp
// 创建文件：day11_select_demo.cpp
#include <iostream>
#include <sys/select.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <unistd.h>
#include <vector>

class SelectDemo {
public:
    void demonstrate_select_usage() {
        std::cout << "=== Select使用演示 ===" << std::endl;
        std::cout << R"(
        select就像一个秘书：
        ├── 帮你监视多个电话
        ├── 一旦有电话响了就告诉你
        ├── 你只需要接听响铃的电话
        └── 不用每个电话都去检查
        )" << std::endl;
        
        // 创建多个管道模拟多个socket
        create_test_pipes();
        run_select_loop();
        cleanup();
    }
    
private:
    std::vector<int> read_fds_;
    std::vector<int> write_fds_;
    
    void create_test_pipes() {
        std::cout << "\n创建测试管道..." << std::endl;
        
        for (int i = 0; i < 3; ++i) {
            int fds[2];
            if (pipe(fds) == 0) {
                read_fds_.push_back(fds[0]);
                write_fds_.push_back(fds[1]);
                
                // 设置为非阻塞
                int flags = fcntl(fds[0], F_GETFL, 0);
                fcntl(fds[0], F_SETFL, flags | O_NONBLOCK);
                
                std::cout << "✓ 创建管道 " << i << ": 读端=" << fds[0] 
                          << ", 写端=" << fds[1] << std::endl;
            }
        }
    }
    
    void run_select_loop() {
        std::cout << "\n开始select监控..." << std::endl;
        
        // 模拟写入数据到某些管道
        write(write_fds_[1], "Data1", 5);  // 向管道1写入数据
        
        for (int round = 0; round < 5; ++round) {
            std::cout << "\n--- 第 " << round + 1 << " 轮监控 ---" << std::endl;
            
            fd_set read_set;
            FD_ZERO(&read_set);
            
            int max_fd = 0;
            for (int fd : read_fds_) {
                FD_SET(fd, &read_set);
                max_fd = std::max(max_fd, fd);
            }
            
            // 设置超时时间
            struct timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            int ready = select(max_fd + 1, &read_set, nullptr, nullptr, &timeout);
            
            if (ready > 0) {
                std::cout << "有 " << ready << " 个文件描述符就绪" << std::endl;
                
                for (size_t i = 0; i < read_fds_.size(); ++i) {
                    if (FD_ISSET(read_fds_[i], &read_set)) {
                        char buffer[100];
                        ssize_t bytes = read(read_fds_[i], buffer, sizeof(buffer) - 1);
                        if (bytes > 0) {
                            buffer[bytes] = '\0';
                            std::cout << "从管道 " << i << " 读取: " << buffer << std::endl;
                        }
                    }
                }
            } else if (ready == 0) {
                std::cout << "超时，没有文件描述符就绪" << std::endl;
                
                // 模拟写入更多数据
                if (round == 2) {
                    write(write_fds_[0], "Data0", 5);
                    write(write_fds_[2], "Data2", 5);
                }
            } else {
                std::cerr << "select失败: " << strerror(errno) << std::endl;
                break;
            }
        }
    }
    
    void cleanup() {
        for (int fd : read_fds_) close(fd);
        for (int fd : write_fds_) close(fd);
    }
};

int main() {
    SelectDemo demo;
    demo.demonstrate_select_usage();
    return 0;
}
```

---

### Day 12: select版本的Echo服务器（版本3）

#### 全天任务 (6小时) - 完整的select服务器

```cpp
// 创建文件：day12_select_server.cpp
#include <iostream>
#include <vector>
#include <map>
#include <algorithm>
#include <sys/select.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>

class SelectEchoServer {
public:
    bool initialize(int port) {
        // 创建监听socket
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            std::cerr << "创建socket失败" << std::endl;
            return false;
        }
        
        // 设置为非阻塞
        set_nonblocking(listen_fd_);
        
        // 设置地址复用
        int opt = 1;
        setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 绑定地址
        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = INADDR_ANY;
        addr.sin_port = htons(port);
        
        if (bind(listen_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            std::cerr << "绑定失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        if (listen(listen_fd_, 128) < 0) {
            std::cerr << "监听失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ Select服务器初始化成功，端口 " << port << std::endl;
        return true;
    }
    
    void start() {
        std::cout << "🚀 Select Echo服务器启动！" << std::endl;
        std::cout << "使用I/O多路复用，支持高并发连接" << std::endl;
        
        while (true) {
            fd_set read_fds;
            int max_fd = prepare_select(read_fds);
            
            // 设置超时
            struct timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            int ready = select(max_fd + 1, &read_fds, nullptr, nullptr, &timeout);
            
            if (ready < 0) {
                if (errno == EINTR) continue;
                std::cerr << "select失败: " << strerror(errno) << std::endl;
                break;
            }
            
            if (ready == 0) {
                // 超时，打印统计信息
                print_stats();
                continue;
            }
            
            // 处理就绪的文件描述符
            handle_ready_fds(read_fds);
        }
    }
    
private:
    int listen_fd_ = -1;
    std::vector<int> client_fds_;
    std::map<int, std::string> client_info_;
    
    void set_nonblocking(int fd) {
        int flags = fcntl(fd, F_GETFL, 0);
        fcntl(fd, F_SETFL, flags | O_NONBLOCK);
    }
    
    int prepare_select(fd_set& read_fds) {
        FD_ZERO(&read_fds);
        
        // 添加监听socket
        FD_SET(listen_fd_, &read_fds);
        int max_fd = listen_fd_;
        
        // 添加所有客户端socket
        for (int client_fd : client_fds_) {
            FD_SET(client_fd, &read_fds);
            max_fd = std::max(max_fd, client_fd);
        }
        
        return max_fd;
    }
    
    void handle_ready_fds(const fd_set& read_fds) {
        // 检查监听socket
        if (FD_ISSET(listen_fd_, &read_fds)) {
            handle_new_connections();
        }
        
        // 检查客户端socket
        auto it = client_fds_.begin();
        while (it != client_fds_.end()) {
            int client_fd = *it;
            
            if (FD_ISSET(client_fd, &read_fds)) {
                if (!handle_client_data(client_fd)) {
                    // 客户端断开，移除
                    close_client(client_fd);
                    it = client_fds_.erase(it);
                } else {
                    ++it;
                }
            } else {
                ++it;
            }
        }
    }
    
    void handle_new_connections() {
        while (true) {  // 非阻塞模式下可能有多个连接
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
            if (client_fd < 0) {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    break;  // 没有更多连接
                } else {
                    std::cerr << "accept失败: " << strerror(errno) << std::endl;
                    break;
                }
            }
            
            // 设置为非阻塞
            set_nonblocking(client_fd);
            
            // 保存客户端信息
            char client_ip[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
            uint16_t client_port = ntohs(client_addr.sin_port);
            
            client_fds_.push_back(client_fd);
            client_info_[client_fd] = std::string(client_ip) + ":" + std::to_string(client_port);
            
            std::cout << "✓ 新连接: " << client_info_[client_fd] 
                      << " (fd=" << client_fd << "), 总连接数: " << client_fds_.size() << std::endl;
        }
    }
    
    bool handle_client_data(int client_fd) {
        char buffer[1024];
        
        while (true) {
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                
                std::string response = "[Select Echo] " + std::string(buffer);
                
                ssize_t bytes_written = write(client_fd, response.c_str(), response.length());
                if (bytes_written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                    std::cerr << "发送失败: " << strerror(errno) << std::endl;
                    return false;
                }
                
                std::cout << "[" << client_info_[client_fd] << "] 处理 " 
                          << bytes_read << " 字节" << std::endl;
                
            } else if (bytes_read == 0) {
                std::cout << "[" << client_info_[client_fd] << "] 客户端断开" << std::endl;
                return false;
                
            } else {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    break;  // 没有更多数据
                } else {
                    std::cerr << "读取失败: " << strerror(errno) << std::endl;
                    return false;
                }
            }
        }
        
        return true;
    }
    
    void close_client(int client_fd) {
        close(client_fd);
        client_info_.erase(client_fd);
    }
    
    void print_stats() {
        static int count = 0;
        if (++count % 10 == 0) {  // 每10秒打印一次
            std::cout << "📊 当前连接数: " << client_fds_.size() << std::endl;
        }
    }
};

int main() {
    SelectEchoServer server;
    
    if (server.initialize(8080)) {
        server.start();
    }
    
    return 0;
}
```

---

### Day 13: epoll入门

#### 上午任务 (3小时) - epoll基础概念

**1. 理解epoll的优势**

```cpp
// 创建文件：day13_epoll_intro.cpp
#include <iostream>
#include <sys/epoll.h>
#include <unistd.h>

void explain_epoll_advantages() {
    std::cout << "=== epoll相比select的优势 ===" << std::endl;
    std::cout << R"(
    select的问题：
    ├── 最多只能监控1024个文件描述符
    ├── 每次调用都要重新设置fd_set
    ├── 需要遍历所有fd才能找到就绪的
    └── 性能随fd数量线性下降 O(n)
    
    epoll的优势：
    ├── 没有文件描述符数量限制
    ├── 只需要设置一次，自动管理
    ├── 直接返回就绪的fd列表
    └── 性能稳定，与fd数量无关 O(1)
    
    简单来说：
    select像是挨个询问："你有事吗？"
    epoll像是等电话响：只处理有事的
    )" << std::endl;
}

// epoll基础操作演示
void demonstrate_epoll_basics() {
    std::cout << "\n=== epoll基础操作 ===" << std::endl;
    
    // 创建epoll实例
    int epoll_fd = epoll_create1(EPOLL_CLOEXEC);
    if (epoll_fd < 0) {
        std::cerr << "创建epoll失败: " << strerror(errno) << std::endl;
        return;
    }
    std::cout << "✓ 创建epoll实例: " << epoll_fd << std::endl;
    
    // 创建一个管道用于测试
    int pipe_fds[2];
    if (pipe(pipe_fds) < 0) {
        std::cerr << "创建管道失败" << std::endl;
        close(epoll_fd);
        return;
    }
    
    // 添加读端到epoll
    struct epoll_event event;
    event.events = EPOLLIN;  // 监听读事件
    event.data.fd = pipe_fds[0];
    
    if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, pipe_fds[0], &event) < 0) {
        std::cerr << "添加fd到epoll失败: " << strerror(errno) << std::endl;
    } else {
        std::cout << "✓ 添加fd " << pipe_fds[0] << " 到epoll监控" << std::endl;
    }
    
    // 测试事件触发
    std::cout << "\n写入数据测试事件..." << std::endl;
    write(pipe_fds[1], "test", 4);
    
    struct epoll_event events[10];
    int ready = epoll_wait(epoll_fd, events, 10, 1000);  // 等待1秒
    
    if (ready > 0) {
        std::cout << "✓ epoll检测到 " << ready << " 个就绪事件" << std::endl;
        for (int i = 0; i < ready; ++i) {
            std::cout << "  fd " << events[i].data.fd << " 就绪" << std::endl;
        }
    }
    
    // 清理资源
    close(pipe_fds[0]);
    close(pipe_fds[1]);
    close(epoll_fd);
}

int main() {
    explain_epoll_advantages();
    demonstrate_epoll_basics();
    return 0;
}
```

#### 下午任务 (3小时) - epoll版本Echo服务器

**2. 完整的epoll服务器**

```cpp
// 创建文件：day13_epoll_server.cpp
#include <iostream>
#include <vector>
#include <unordered_map>
#include <sys/epoll.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>

class EpollEchoServer {
public:
    bool initialize(int port) {
        // 创建epoll实例
        epoll_fd_ = epoll_create1(EPOLL_CLOEXEC);
        if (epoll_fd_ < 0) {
            std::cerr << "创建epoll失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        // 创建监听socket
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        // 设置为非阻塞
        set_nonblocking(listen_fd_);
        
        // 设置地址复用
        int opt = 1;
        setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 绑定地址
        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = INADDR_ANY;
        addr.sin_port = htons(port);
        
        if (bind(listen_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            std::cerr << "绑定失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        if (listen(listen_fd_, 128) < 0) {
            std::cerr << "监听失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        // 添加监听socket到epoll
        struct epoll_event event;
        event.events = EPOLLIN;
        event.data.fd = listen_fd_;
        if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, listen_fd_, &event) < 0) {
            std::cerr << "添加监听socket到epoll失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ Epoll服务器初始化成功，端口 " << port << std::endl;
        return true;
    }
    
    void start() {
        std::cout << "🚀 Epoll Echo服务器启动！" << std::endl;
        std::cout << "使用epoll实现高性能I/O多路复用" << std::endl;
        
        struct epoll_event events[MAX_EVENTS];
        
        while (true) {
            int ready = epoll_wait(epoll_fd_, events, MAX_EVENTS, 1000);  // 1秒超时
            
            if (ready < 0) {
                if (errno == EINTR) continue;
                std::cerr << "epoll_wait失败: " << strerror(errno) << std::endl;
                break;
            }
            
            if (ready == 0) {
                // 超时，打印统计信息
                print_stats();
                continue;
            }
            
            // 处理所有就绪事件
            for (int i = 0; i < ready; ++i) {
                handle_event(events[i]);
            }
        }
    }
    
private:
    static const int MAX_EVENTS = 1024;
    int epoll_fd_ = -1;
    int listen_fd_ = -1;
    std::unordered_map<int, std::string> client_info_;
    
    void set_nonblocking(int fd) {
        int flags = fcntl(fd, F_GETFL, 0);
        fcntl(fd, F_SETFL, flags | O_NONBLOCK);
    }
    
    void handle_event(const struct epoll_event& event) {
        int fd = event.data.fd;
        
        if (fd == listen_fd_) {
            // 新连接事件
            handle_new_connections();
        } else {
            // 客户端数据事件
            if (event.events & EPOLLIN) {
                if (!handle_client_data(fd)) {
                    close_client(fd);
                }
            }
            
            if (event.events & (EPOLLHUP | EPOLLERR)) {
                std::cout << "客户端 " << fd << " 连接异常，关闭连接" << std::endl;
                close_client(fd);
            }
        }
    }
    
    void handle_new_connections() {
        while (true) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
            if (client_fd < 0) {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    break;
                } else {
                    std::cerr << "accept失败: " << strerror(errno) << std::endl;
                    break;
                }
            }
            
            // 设置为非阻塞
            set_nonblocking(client_fd);
            
            // 添加到epoll监控
            struct epoll_event event;
            event.events = EPOLLIN | EPOLLET;  // 使用边缘触发模式
            event.data.fd = client_fd;
            
            if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, client_fd, &event) < 0) {
                std::cerr << "添加客户端到epoll失败: " << strerror(errno) << std::endl;
                close(client_fd);
                continue;
            }
            
            // 保存客户端信息
            char client_ip[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
            uint16_t client_port = ntohs(client_addr.sin_port);
            
            client_info_[client_fd] = std::string(client_ip) + ":" + std::to_string(client_port);
            
            std::cout << "✓ 新连接: " << client_info_[client_fd] 
                      << " (fd=" << client_fd << "), 总连接数: " << client_info_.size() << std::endl;
        }
    }
    
    bool handle_client_data(int client_fd) {
        char buffer[1024];
        
        while (true) {  // ET模式需要一次性读完所有数据
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                
                std::string response = "[Epoll Echo] " + std::string(buffer);
                
                ssize_t bytes_written = write(client_fd, response.c_str(), response.length());
                if (bytes_written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                    std::cerr << "发送失败: " << strerror(errno) << std::endl;
                    return false;
                }
                
                std::cout << "[" << client_info_[client_fd] << "] 处理 " 
                          << bytes_read << " 字节" << std::endl;
                
            } else if (bytes_read == 0) {
                std::cout << "[" << client_info_[client_fd] << "] 客户端断开" << std::endl;
                return false;
                
            } else {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    break;  // ET模式下所有数据读完了
                } else {
                    std::cerr << "读取失败: " << strerror(errno) << std::endl;
                    return false;
                }
            }
        }
        
        return true;
    }
    
    void close_client(int client_fd) {
        epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, client_fd, nullptr);
        close(client_fd);
        client_info_.erase(client_fd);
    }
    
    void print_stats() {
        static int count = 0;
        if (++count % 10 == 0) {
            std::cout << "📊 当前连接数: " << client_info_.size() << std::endl;
        }
    }
};

int main() {
    EpollEchoServer server;
    
    if (server.initialize(8080)) {
        server.start();
    }
    
    return 0;
}
```

---

### Day 14: 版本4和5的完善 + 总结

#### 上午任务 (3小时) - 面向对象封装

**1. Socket类封装**

参考@03-实战第一步-简单Echo服务器.md中的版本4和版本5设计

#### 下午任务 (3小时) - 性能测试和总结

**2. 性能对比测试**

编写完整的测试脚本，对比所有版本的性能

**3. 第二周学习总结**

总结网络编程的核心概念和实战经验

---

## 📝 第二周作业总览

**每日作业汇总：**
1. Day 8: 理解网络基础概念，完成Socket基础练习
2. Day 9: 实现并测试最简单的Echo服务器
3. Day 10: 实现多进程版本，理解并发处理
4. Day 11: 掌握非阻塞I/O和select使用
5. Day 12: 完成select版本的完整服务器
6. Day 13: 学会epoll，实现高性能服务器
7. Day 14: 完善面向对象版本，进行性能测试

**主人，这样的第二周安排是否更符合您的基础水平？每天都有理论+实践+作业，循序渐进地掌握网络编程！** 