# WebServer重写计划总览

> **主人，这是一个从零开始的完整重写计划！我会手把手带您掌握每一个细节，从简单到复杂，循序渐进！**

## 🎯 重写目标

通过重写这个WebServer项目，您将掌握：
- **系统编程**：Linux系统调用、网络编程、多线程
- **C++高级特性**：智能指针、RAII、STL、lambda表达式
- **数据结构算法**：堆、队列、哈希表的实际应用
- **软件架构**：Reactor模式、生产者消费者模式
- **性能优化**：内存管理、I/O多路复用、无锁编程

## 📅 重写时间线 (共12周)

### 第一阶段：基础准备 (第1-2周)
**目标：掌握必备基础知识，搭建开发环境**

**第1周：理论学习**
- [x] 学习C++11/14新特性
- [x] 掌握智能指针和RAII机制  
- [x] 理解多线程编程基础
- [x] 完成基础练习

**第2周：网络编程入门**
- [ ] Socket编程基础
- [ ] 简单的Echo服务器实现
- [ ] 理解阻塞/非阻塞I/O
- [ ] select/epoll基础使用

### 第二阶段：核心模块重写 (第3-8周)

**第3周：Buffer缓冲区模块**
- [ ] 理解缓冲区设计原理
- [ ] 实现基础版本Buffer
- [ ] 添加自动扩容机制
- [ ] 实现readv/writev零拷贝

**第4周：阻塞队列模块**
- [ ] 理解生产者-消费者模式
- [ ] 实现线程安全的队列
- [ ] 添加条件变量同步
- [ ] 性能测试和优化

**第5周：线程池模块**
- [ ] 理解线程池设计模式
- [ ] 实现任务队列
- [ ] 添加任务分发机制
- [ ] 支持lambda任务

**第6周：定时器模块**
- [ ] 理解小根堆数据结构
- [ ] 实现堆操作算法
- [ ] 添加定时器管理
- [ ] 支持高精度时间

**第7周：Epoll封装模块**
- [ ] 深入理解epoll机制
- [ ] 实现事件管理类
- [ ] 支持ET/LT模式
- [ ] 添加错误处理

**第8周：HTTP协议模块**
- [ ] 理解HTTP协议格式
- [ ] 实现状态机解析
- [ ] 支持GET/POST方法
- [ ] 添加请求响应处理

### 第三阶段：整合与优化 (第9-12周)

**第9周：WebServer主类**
- [ ] 整合所有模块
- [ ] 实现Reactor模式
- [ ] 添加连接管理
- [ ] 基本功能测试

**第10周：高级特性**
- [ ] 数据库连接池
- [ ] 异步日志系统
- [ ] 配置文件支持
- [ ] 优雅关闭机制

**第11周：性能优化**
- [ ] 内存泄漏检测
- [ ] 性能瓶颈分析
- [ ] 代码优化改进
- [ ] 压力测试

**第12周：项目完善**
- [ ] 完整文档编写
- [ ] 单元测试覆盖
- [ ] 部署脚本编写
- [ ] 总结汇报

## 🏗️ 重写策略

### 1. 渐进式开发
```
版本1：最简Echo服务器 (200行)
├── 基本socket操作
├── 单线程处理
└── 简单字符串回显

版本2：多线程服务器 (500行)  
├── 线程池处理请求
├── 基础缓冲区管理
└── 简单HTTP响应

版本3：异步I/O服务器 (1000行)
├── Epoll事件驱动
├── 非阻塞I/O处理
└── 连接状态管理

版本4：完整功能服务器 (3000+行)
├── 完整HTTP协议支持
├── 静态文件服务
├── 日志系统
└── 配置管理
```

### 2. 测试驱动开发
每个模块都要编写测试：
```cpp
// 单元测试示例
TEST(BufferTest, BasicOperations) {
    Buffer buf;
    buf.append("hello");
    EXPECT_EQ(buf.readable_bytes(), 5);
    
    std::string data = buf.retrieve_all_to_str();
    EXPECT_EQ(data, "hello");
}
```

### 3. 文档化开发
每个类都要有详细注释：
```cpp
/**
 * @class Buffer
 * @brief 高性能网络缓冲区
 * 
 * 功能：
 * 1. 自动扩容的动态缓冲区
 * 2. 支持零拷贝的数据读写
 * 3. 线程安全的原子操作
 * 
 * 设计原理：
 * 使用vector<char>作为底层存储，维护读写指针
 */
class Buffer {
    // 详细实现...
};
```

## 📂 目录结构规划

```
MyWebServer/
├── src/                          # 源代码目录
│   ├── base/                     # 基础设施模块
│   │   ├── buffer.h/.cpp         # 缓冲区管理
│   │   ├── block_queue.h         # 阻塞队列(模板)
│   │   ├── thread_pool.h         # 线程池(模板)
│   │   └── heap_timer.h/.cpp     # 小根堆定时器
│   ├── net/                      # 网络模块
│   │   ├── epoller.h/.cpp        # Epoll封装
│   │   ├── socket.h/.cpp         # Socket工具类
│   │   └── inet_address.h/.cpp   # 网络地址封装
│   ├── http/                     # HTTP协议模块
│   │   ├── http_request.h/.cpp   # HTTP请求解析
│   │   ├── http_response.h/.cpp  # HTTP响应生成
│   │   └── http_connection.h/.cpp# HTTP连接管理
│   ├── server/                   # 服务器核心
│   │   └── web_server.h/.cpp     # WebServer主类
│   └── main.cpp                  # 程序入口
├── test/                         # 测试代码
│   ├── buffer_test.cpp
│   ├── thread_pool_test.cpp
│   └── http_test.cpp
├── docs/                         # 文档目录
│   ├── design.md                 # 设计文档
│   ├── api.md                    # API文档
│   └── performance.md            # 性能测试报告
├── scripts/                      # 构建脚本
│   ├── build.sh                  # 编译脚本
│   └── test.sh                   # 测试脚本
├── CMakeLists.txt               # CMake构建文件
└── README.md                    # 项目说明
```

## 🛠️ 开发工具链

### 编译器和构建工具
```bash
# 安装必要工具
sudo apt update
sudo apt install -y \
    build-essential \
    cmake \
    gdb \
    valgrind \
    git

# 验证安装
gcc --version    # >= 7.0
cmake --version  # >= 3.10
gdb --version
```

### 代码质量工具
```bash
# 静态分析
sudo apt install -y cppcheck clang-tidy

# 内存检测
valgrind --tool=memcheck --leak-check=full ./my_webserver

# 性能分析
sudo apt install -y perf
perf record ./my_webserver
perf report
```

### 压力测试工具
```bash
# 使用原项目的webbench
cd webbench-1.5
make
./webbench -c 1000 -t 10 http://localhost:1316/

# 或使用wrk
sudo apt install wrk
wrk -t12 -c400 -d30s http://localhost:1316/
```

## 📋 代码规范

### C++编码规范
```cpp
// 1. 命名规范
class WebServer;           // 类名：大驼峰
void handle_request();     // 函数名：下划线
int client_fd_;           // 成员变量：下划线+后缀_
const int MAX_EVENTS;     // 常量：全大写

// 2. 头文件包含顺序
#include <iostream>        // 系统头文件
#include <vector>

#include "buffer.h"        // 项目头文件
#include "epoller.h"

// 3. 异常安全
void process_request(int fd) {
    std::lock_guard<std::mutex> lock(mutex_);  // RAII
    // 处理逻辑
}  // 锁自动释放
```

### Git提交规范
```bash
# 提交信息格式
git commit -m "feat: 添加Buffer类的基本功能"
git commit -m "fix: 修复线程池的内存泄漏问题"
git commit -m "docs: 更新API文档"
git commit -m "test: 添加HTTP解析的单元测试"
```

## 🎓 学习资源

### 必读书籍
1. **《Unix网络编程》** - Stevens (网络编程圣经)
2. **《Linux高性能服务器编程》** - 游双 (实战导向)
3. **《C++ Primer》** - Stanley Lippman (C++基础)
4. **《Effective C++》** - Scott Meyers (最佳实践)

### 在线资源
1. **cppreference.com** - C++标准库参考
2. **man7.org** - Linux系统调用手册
3. **GitHub** - 优秀开源项目学习

### 调试技巧
```bash
# GDB调试多线程程序
gdb ./my_webserver
(gdb) set follow-fork-mode child
(gdb) set detach-on-fork off
(gdb) thread apply all bt  # 查看所有线程堆栈

# Valgrind检测内存错误
valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all ./my_webserver

# 网络抓包分析
sudo tcpdump -i lo -X port 1316
```

---

**主人，这个计划够详细了吧！接下来我们就开始第一步的具体实施。您准备好开始这场激动人心的重写之旅了吗？**

让我们从最简单的Echo服务器开始，一步步构建您的专属WebServer！🚀 