/*
 * <AUTHOR> mark
 * @Date         : 2020-06-26
 * @copyleft Apache 2.0
 * 
 * Buffer 高效缓冲区类实现
 * 核心技术：
 * 1. 基于vector的动态内存管理
 * 2. 原子操作保证线程安全
 * 3. readv分散读取实现零拷贝
 * 4. 智能内存整理避免频繁分配
 */ 

#include "buffer.h"

/**
 * @brief Buffer构造函数
 * @param initBuffSize 初始缓冲区大小
 * 
 * 初始化列表的使用体现了现代C++的高效初始化方式：
 * - buffer_(initBuffSize): 直接构造vector，避免默认构造+赋值
 * - readPos_(0), writePos_(0): 原子变量的直接初始化
 */
Buffer::Buffer(int initBuffSize) : buffer_(initBuffSize), readPos_(0), writePos_(0) {}

// ==================== 状态查询函数实现 ====================

/**
 * @brief 获取可读字节数
 * @return size_t 可读数据的字节数
 * 
 * 实现原理：写指针位置减去读指针位置
 * 原子操作确保在多线程环境下的数据一致性
 */
size_t Buffer::ReadableBytes() const {
    return writePos_ - readPos_;
}

/**
 * @brief 获取可写字节数
 * @return size_t 可写入的字节数
 * 
 * 实现原理：缓冲区总大小减去已写入的数据量
 */
size_t Buffer::WritableBytes() const {
    return buffer_.size() - writePos_;
}

/**
 * @brief 获取预留字节数
 * @return size_t 读指针前的预留空间
 * 
 * 【详细解释：预留空间 vs 已读数据的区别】
 * 
 * 
 * 想象缓冲区是一个书架：
 * [已读完的书][正在读的书][空位置放新书]
 *      ↑           ↑         ↑
 *   readPos_   writePos_  buffer_.size()
 * 
 * 关键理解：readPos_前面的空间有两种用途！
 * 
 * 1. 【已读数据】视角：
 *    - 这里确实曾经存放过已经读取完的数据
 *    - 但读取后，这些数据在逻辑上已经"无效"了
 *    - 就像读完的书可以收起来，空出位置
 * 
 * 2. 【预留空间】视角：
 *    - 虽然这里有"已读数据的痕迹"，但现在可以重新利用！
 *    - 这个空间可以用来向前插入数据（prepend操作）
 *    - 比如：给现有数据添加协议头、长度信息等
 * 
 * 实际应用举例：
 * 假设我们有数据"Hello World"，但后来需要在前面加长度"11:"
 * 
 * 步骤1：先读掉一些数据，readPos_移动，前面有空间了
 * 步骤2：利用这个空间，向前写入"11:"
 * 最终："11:Hello World"
 * 
 * 所以PrependableBytes()返回的是"可用的预留空间大小"
 * 虽然这个位置曾经是已读数据，但现在是可重复利用的宝贵空间！
 * 
 * 这就是高效缓冲区设计的精髓：变废为宝，充分利用每一字节！
 */
size_t Buffer::PrependableBytes() const {
    return readPos_;
}

// ==================== 数据访问函数实现 ====================

/**
 * @brief 获取可读数据的起始指针
 * @return const char* 指向可读数据的指针
 * 
 * 关键技术：指针运算
 * BeginPtr_() + readPos_ 计算出当前可读数据的起始位置
 */
/**
 * @brief 获取可读数据的起始指针
 * @return const char* 指向可读数据的指针
 * 
 * 【超详细解释：指针运算在缓冲区中的应用】
 * 
 * 1. 【函数功能】：
 *    这个函数的作用是告诉我们"当前可以读取的数据从哪里开始"
 *    就像在一本书里，告诉你"现在应该从第几页开始读"
 * 
 * 2. 【BeginPtr_()的含义】：
 *    BeginPtr_()返回的是整个buffer_数组的起始地址
 *    想象一下：buffer_就像一个很长的数组，BeginPtr_()指向第0个位置
 *    
 *    数组示意图：
 *    [0][1][2][3][4][5][6][7][8][9]...
 *     ↑
 *   BeginPtr_()指向这里
 * 
 * 3. 【readPos_的作用】：
 *    readPos_是一个数字，表示"当前读取位置的偏移量"
 *    比如readPos_ = 3，意思是"前面3个位置的数据已经读过了"
 *    
 *    状态示意图：
 *    [已读][已读][已读][可读][可读][可读][空闲]...
 *     0    1    2    3    4    5    6
 *                    ↑
 *                readPos_=3，从这里开始是可读数据
 * 
 * 4. 【指针运算：BeginPtr_() + readPos_】：
 *    这是C++中的指针算术运算
 *    - BeginPtr_()：数组起始地址（第0个元素的地址）
 *    - readPos_：偏移量（要跳过多少个元素）
 *    - 结果：第readPos_个元素的地址
 *    
 *    具体例子：
 *    假设BeginPtr_()返回地址1000，readPos_=3
 *    那么BeginPtr_() + readPos_ = 1000 + 3 = 1003
 *    意思是：指向数组中第3个位置的元素
 * 
 * 5. 【生活化类比】：
 *    想象你在看一本连环画：
 *    - 整本书就是buffer_数组
 *    - BeginPtr_()是书的第1页地址
 *    - readPos_=5意思是"前面5页已经看过了"
 *    - Peek()返回的就是"第6页的地址"（从这里开始是没看过的内容）
 * 
 * 6. 【为什么叫Peek】：
 *    "Peek"在英语里是"偷看、窥视"的意思
 *    这个函数只是"看看"数据在哪里，但不移动读指针
 *    就像偷看一眼书的下一页，但不翻页
 * 
 * 7. 【const的重要性】：
 *    返回const char*意味着：
 *    - 你可以读取这个位置的数据
 *    - 但不能修改这个位置的数据
 *    - 这是一种保护机制，防止意外修改缓冲区内容
 */
const char* Buffer::Peek() const {
    return BeginPtr_() + readPos_;  // 起始地址 + 读偏移量 = 可读数据的起始位置
}

// ==================== 数据读取函数实现 ====================

/**
 * @brief 从缓冲区取出指定长度的数据
 * @param len 要取出的字节数
 * 
 * 实现要点：
 * 1. assert确保不会读取超出范围的数据
 * 2. 只移动读指针，不实际删除数据（延迟删除策略）
 * 3. 原子操作保证线程安全
 */
void Buffer::Retrieve(size_t len) {
    assert(len <= ReadableBytes());  // 防御性编程：确保不越界
    readPos_ += len;                 // 原子操作：移动读指针
}

/**
 * @brief 取出数据直到指定位置
 * @param end 结束位置指针
 * 
 * 应用场景：解析HTTP协议时，读取到行结束符为止
 */
void Buffer::RetrieveUntil(const char* end) {
    assert(Peek() <= end);           // 确保end指针在有效范围内
    Retrieve(end - Peek());          // 计算长度并调用Retrieve
}

/**
 * @brief 清空所有数据
 * 
 * 实现策略：
 * 1. bzero清零整个缓冲区（安全考虑，避免敏感数据残留）
 * 2. 重置读写指针到初始位置
 * 3. 这是一个"重置"操作，不改变缓冲区大小
 */
void Buffer::RetrieveAll() {
    bzero(&buffer_[0], buffer_.size());  // 清零内存，防止数据泄露
    readPos_ = 0;                        // 重置读指针
    writePos_ = 0;                       // 重置写指针
}

/**
 * @brief 取出所有数据并转为字符串
 * @return std::string 包含所有数据的字符串
 * 
 * 详细解释std::string的构造过程：
 * 1. std::string有一个构造函数：string(const char* s, size_t n)
 * 2. 这个构造函数会从指定内存位置复制n个字符来创建字符串
 * 3. Peek()返回可读数据的起始地址，ReadableBytes()返回可读数据的长度
 * 4. 相当于告诉string："从这个地址开始，复制这么多个字节来创建字符串"
 */
std::string Buffer::RetrieveAllToStr() {
    // 分步解释这行代码的含义：
    // Peek() -> 返回缓冲区中可读数据的起始位置指针 (const char*)
    // ReadableBytes() -> 返回缓冲区中可读数据的字节数 (size_t)
    // std::string构造函数会从Peek()指向的内存位置开始，
    // 复制ReadableBytes()个字节的数据来创建新的字符串对象
    std::string str(Peek(), ReadableBytes());  
    
    RetrieveAll();                             // 清空缓冲区
    return str;                                // 返回值优化(RVO)
}

// ==================== 写入位置访问函数实现 ====================

/**
 * @brief 获取写入位置指针（只读版本）
 * @return const char* 指向可写位置的指针
 * 
 * 详细解释这个C++语法：
 * 1. 函数签名分析：const char* Buffer::BeginWriteConst() const
 *    - const char*：返回类型，表示返回一个指向常量字符的指针
 *      * 这意味着通过这个指针不能修改所指向的数据
 *      * 相当于说："我给你一个地址，但你只能读，不能写"
 *    
 *    - Buffer::BeginWriteConst()：这是Buffer类的成员函数
 *    
 *    - 最后的const：这是常量成员函数修饰符
 *      * 表示这个函数不会修改对象的任何成员变量
 *      * 相当于向编译器承诺："我这个函数只是查看数据，绝不修改"
 *      * 只有const成员函数才能被const对象调用
 * 
 * 2. 为什么需要这样设计？
 *    - 当你有一个const Buffer对象时，只能调用const成员函数
 *    - 这个函数提供了一种安全的方式来获取写入位置，但不允许修改
 *    - 体现了C++的类型安全和const正确性原则
 * 
 * 3. 生活化比喻：
 *    - 就像图书馆管理员给你一张纸条，上面写着某本书的位置
 *    - 你可以根据这个位置找到书并阅读（const char*）
 *    - 但管理员承诺在给你纸条的过程中不会移动任何书籍（const成员函数）
 */
const char* Buffer::BeginWriteConst() const {
    return BeginPtr_() + writePos_;
}

/**
 * @brief 获取写入位置指针（可写版本）
 * @return char* 指向可写位置的指针
 * 
 * 用途：允许外部代码直接写入缓冲区，提高性能
 */
char* Buffer::BeginWrite() {
    return BeginPtr_() + writePos_;
}

/**
 * @brief 标记已写入指定长度的数据
 * @param len 已写入的字节数
 * 
 * 详细解释这个函数的作用和原理：
 * 
 * 1. 这个函数是做什么的？
 *    - 这个函数的作用是"告诉缓冲区：我已经写入了len个字节的数据"
 *    - 它不负责实际写入数据，只是更新内部的记录
 *    - 就像你在一个登记本上记录："今天又增加了5页内容"
 * 
 * 2. 为什么需要这个函数？
 *    - 有时候我们需要直接操作内存来提高性能
 *    - 比如从网络socket直接读取数据到缓冲区
 *    - 这时我们绕过了Buffer的Append函数，直接写入内存
 *    - 但Buffer对象并不知道我们写了多少数据
 *    - 所以需要手动告诉它："我写了这么多字节"
 * 
 * 3. 配合BeginWrite()的使用流程：
 *    第1步：char* writePtr = buffer.BeginWrite();     // 获取写入位置
 *    第2步：int bytesWritten = recv(socket, writePtr, 1024, 0);  // 直接写入数据
 *    第3步：buffer.HasWritten(bytesWritten);          // 告诉buffer写了多少
 * 
 * 4. 生活化比喻：
 *    - 想象你有一个笔记本，还有一个书签标记"当前写到哪里了"
 *    - BeginWrite()就是告诉你："书签现在在第X页"
 *    - 你直接在第X页开始写内容
 *    - HasWritten(5)就是告诉笔记本："我写了5页，请把书签往后移5页"
 * 
 * 5. writePos_的含义：
 *    - writePos_是Buffer内部的一个成员变量
 *    - 它记录着"下一个可以写入的位置"
 *    - writePos_ += len 就是把这个位置向后移动len个字节
 *    - 这样Buffer就知道了新的数据边界
 * 
 * 6. 为什么说是"原子操作"？
 *    - 这里的"原子"是指这个操作很简单、不可分割
 *    - 就是一个简单的加法运算，不会被中断
 *    - 相对于复杂的内存拷贝操作来说，这个更新指针的操作非常快
 */
void Buffer::HasWritten(size_t len) {
    writePos_ += len;  // 更新写指针位置：告诉缓冲区数据边界向后移动了len个字节
} 

// ==================== 数据写入函数实现 ====================

/**
 * @brief 追加字符串数据
 * @param str 要追加的字符串
 * 
 * 委托实现：调用更通用的Append(const char*, size_t)版本
 */
void Buffer::Append(const std::string& str) {
    Append(str.data(), str.length());
}

/**
 * @brief 追加任意类型数据到缓冲区
 * @param data 数据指针（可以是任意类型的数据）
 * @param len 数据长度（字节数）
 * 
 * 让我详细解释这个函数的每一个细节：
 * 
 * 1. 这个函数是做什么的？
 *    - 这是一个"万能"的数据追加函数
 *    - 它可以接收任意类型的数据（int、double、结构体等）
 *    - 然后把这些数据当作字节流添加到缓冲区中
 *    - 就像一个"数据转换器"，把各种类型的数据都变成字节存储
 * 
 * 2. 为什么参数是const void*？
 *    - void*是C++中的"万能指针"，可以指向任何类型的数据
 *    - const表示我们不会修改原始数据，只是读取它
 *    - 这样设计让函数可以接收各种类型：
 *      int num = 42;
 *      buffer.Append(&num, sizeof(int));     // 可以存储整数
 *      
 *      struct Person p = {"张三", 25};
 *      buffer.Append(&p, sizeof(Person));    // 可以存储结构体
 * 
 * 3. static_cast<const char*>(data)是什么意思？
 *    - static_cast是C++的类型转换操作符，比C语言的强制转换更安全
 *    - 它把void*指针转换成char*指针
 *    - 为什么要转换成char*？因为：
 *      * char是1字节，用char*可以按字节访问任何数据
 *      * 最终的Append(const char*, size_t)函数需要char*参数
 *      * 这就像把"任意类型的盒子"转换成"字节盒子"
 * 
 * 4. assert(data)的作用：
 *    - assert是断言，用于调试时检查条件
 *    - 如果data是nullptr（空指针），程序会立即终止并报错
 *    - 这是"防御性编程"：在问题发生前就发现并阻止它
 *    - 就像门卫检查："你必须有有效的通行证才能进入"
 * 
 * 5. 整个函数的工作流程：
 *    第1步：检查传入的指针是否有效（assert(data)）
 *    第2步：把万能指针转换成字节指针（static_cast）
 *    第3步：调用真正的实现函数（Append(const char*, size_t)）
 * 
 * 6. 生活化比喻：
 *    - 想象这个函数是一个"万能打包机"
 *    - 你可以放入任何东西：苹果、书本、玩具
 *    - 打包机不关心是什么东西，只是把它们按字节打包
 *    - 最后统一交给"字节处理车间"进行最终处理
 * 
 * 7. 为什么需要这个函数？
 *    - 提供了类型安全的接口
 *    - 避免用户直接进行危险的类型转换
 *    - 统一了不同类型数据的处理方式
 *    - 让Buffer可以存储任何类型的数据
 */
void Buffer::Append(const void* data, size_t len) {
    assert(data);  // 防御性编程：确保指针有效，防止空指针访问
    Append(static_cast<const char*>(data), len);  // 类型安全转换：万能指针→字节指针
}

/**
 * @brief 追加字符数组数据（核心实现函数）
 * @param str 字符数组指针（要添加的数据）
 * @param len 数据长度（要添加多少个字节）
 * 
 * 让我详细解释这个核心函数的每一个细节：
 * 
 * 1. 这个函数是做什么的？
 *    - 这是Buffer类最核心的数据添加函数
 *    - 它负责把字符数组（字节数据）添加到缓冲区中
 *    - 就像往一个可以自动扩容的"数据容器"里放东西
 * 
 * 2. 参数详解：
 *    - const char* str：指向要添加的数据的指针
 *      * const表示我们不会修改原始数据，只是读取
 *      * char*表示这是字节数据（每个char占1字节）
 *      * 比如："Hello"这个字符串就是char*类型
 *    
 *    - size_t len：要添加的数据长度
 *      * size_t是无符号整数类型，专门用来表示大小
 *      * 告诉函数要复制多少个字节
 *      * 比如："Hello"的长度是5
 * 
 * 3. 函数执行的4个步骤（按顺序）：
 * 
 *    第1步：assert(str) - 安全检查
 *    - assert是"断言"，用来检查条件是否成立
 *    - 如果str是空指针（nullptr），程序会立即停止并报错
 *    - 这是"防御性编程"：防止程序崩溃
 *    - 就像门卫检查："你必须有有效证件才能进入"
 * 
 *    第2步：EnsureWriteable(len) - 确保空间足够
 *    - 检查缓冲区是否有足够的空间来存储新数据
 *    - 如果空间不够，会自动扩容或整理空间
 *    - 就像检查行李箱是否还能放下新衣服，不够就换个大箱子
 * 
 *    第3步：std::copy(str, str + len, BeginWrite()) - 复制数据
 *    - std::copy是C++标准库的高效复制函数
 *    - str：复制的起始位置（从哪里开始复制）
 *    - str + len：复制的结束位置（复制到哪里结束）
 *    - BeginWrite()：目标位置（复制到缓冲区的哪个位置）
 *    - 就像用复印机把文件复制到新的地方
 * 
 *    第4步：HasWritten(len) - 更新写指针
 *    - 告诉缓冲区："我已经写入了len个字节的数据"
 *    - 更新内部的写指针位置，为下次写入做准备
 *    - 就像在笔记本上写完字后，把笔移到下一行的开头
 * 
 * 4. 生活化比喻：
 *    想象这个函数是一个"智能文件管理员"：
 *    - 第1步：检查你给的文件是否存在（assert）
 *    - 第2步：检查文件柜是否有足够空间，没有就换大柜子（EnsureWriteable）
 *    - 第3步：把文件复制到文件柜里（std::copy）
 *    - 第4步：在登记册上记录新增了多少文件（HasWritten）
 * 
 * 5. 为什么这样设计？
 *    - 安全性：先检查指针，防止程序崩溃
 *    - 自动化：自动处理空间不足的问题
 *    - 高效性：使用std::copy进行优化的内存复制
 *    - 一致性：统一更新内部状态
 * 
 * 6. 这个函数在整个Buffer系统中的地位：
 *    - 这是所有其他Append函数的"最终实现"
 *    - 其他Append函数最终都会调用这个函数
 *    - 就像所有的河流最终都汇入大海一样
 */
void Buffer::Append(const char* str, size_t len) {
    assert(str);                              // 第1步：安全检查 - 确保指针有效，防止空指针访问
    EnsureWriteable(len);                     // 第2步：空间管理 - 确保有足够的写入空间
    std::copy(str, str + len, BeginWrite());  // 第3步：数据复制 - 高效地将数据复制到缓冲区
    HasWritten(len);                          // 第4步：状态更新 - 更新写指针位置
}

/**
 * @brief 追加另一个缓冲区的数据
 * @param buff 源缓冲区
 * 
 * 缓冲区间数据传输的高效实现
 */
void Buffer::Append(const Buffer& buff) {
    Append(buff.Peek(), buff.ReadableBytes());
}

/**
 * @brief 确保有足够的可写空间
 * @param len 需要的空间大小
 * 
 * 智能空间管理：
 * 1. 首先检查当前可写空间是否足够
 * 2. 不足时调用MakeSpace_()进行空间整理或扩容
 * 3. 使用assert确保操作成功
 */
void Buffer::EnsureWriteable(size_t len) {
    if(WritableBytes() < len) {
        MakeSpace_(len);  // 空间不足时进行整理或扩容
    }
    assert(WritableBytes() >= len);  // 确保操作成功
}

// ==================== 文件IO函数实现 ====================

/**
 * @brief 从文件描述符读取数据到缓冲区
 * @param fd 文件描述符
 * @param saveErrno 错误码保存指针
 * @return ssize_t 实际读取的字节数
 * 
 * 核心技术：readv分散读取实现零拷贝
 * 
 * 设计思想：
 * 1. 使用栈上临时缓冲区处理超出缓冲区容量的数据
 * 2. readv一次性读取到两个缓冲区，减少系统调用次数
 * 3. 根据实际读取量智能处理数据分布
 */
ssize_t Buffer::ReadFd(int fd, int* saveErrno) {
    char buff[65535];  // 栈上临时缓冲区，64KB大小
    struct iovec iov[2];  // 分散读取的向量数组
    const size_t writable = WritableBytes();  // 当前可写空间
    
    /* 
     * 分散读取配置：
     * iov[0]: 指向缓冲区的可写区域
     * iov[1]: 指向栈上的临时缓冲区
     * 
     * 这样设计的优势：
     * 1. 优先填充缓冲区的可写空间
     * 2. 超出部分暂存到栈缓冲区
     * 3. 一次系统调用读取更多数据
     */
    iov[0].iov_base = BeginPtr_() + writePos_;  // 缓冲区可写位置
    iov[0].iov_len = writable;                  // 缓冲区可写大小
    iov[1].iov_base = buff;                     // 临时缓冲区
    iov[1].iov_len = sizeof(buff);              // 临时缓冲区大小

    // readv系统调用：一次性读取到多个缓冲区
    // readv(文件描述符, 向量数组, 向量个数)
    // 这里会同时向iov[0]和iov[1]两个缓冲区读取数据
    // 返回值是总共读取的字节数，如果出错返回-1
    const ssize_t len = readv(fd, iov, 2);
    
    if(len < 0) {
        // 读取失败，保存错误码
        *saveErrno = errno;
    }
    else if(static_cast<size_t>(len) <= writable) {
        // 读取的数据完全放入了缓冲区
        writePos_ += len;
    }
    else {
        // 数据超出缓冲区容量，需要处理临时缓冲区的数据
        writePos_ = buffer_.size();                    // 缓冲区已满
        // 这里的关键理解：缓冲区"满"了，但Append会自动扩容！
        // 
        // 当前状态分析：
        // - writePos_ = buffer_.size() 表示缓冲区的预分配空间已用完
        // - 但vector可以动态扩容，Append函数内部会调用EnsureWriteable()
        // - EnsureWriteable()会重新分配更大的内存空间
        // 
        // 数据分布情况：
        // - 缓冲区已经装入了 writable 字节的数据
        // - 临时缓冲区buff中还有 (len - writable) 字节的数据需要处理
        // - 通过Append将临时缓冲区的剩余数据追加到扩容后的缓冲区末尾
        //
        // 这就是为什么即使"满了"还能继续添加数据的原因：
        // vector的动态扩容特性 + Append的自动扩容逻辑
        Append(buff, len - writable);                  // 追加临时缓冲区的数据
    }
    return len;
}

/**
 * @brief 将缓冲区数据写入文件描述符
 * @param fd 文件描述符
 * @param saveErrno 错误码保存指针
 * @return ssize_t 实际写入的字节数
 * 
 * 实现要点：
 * 1. 获取所有可读数据进行写入
 * 2. 根据实际写入量更新读指针
 * 3. 错误处理和状态维护
 */
ssize_t Buffer::WriteFd(int fd, int* saveErrno) {
    size_t readSize = ReadableBytes();           // 获取可读数据量
    ssize_t len = write(fd, Peek(), readSize);   // 写入所有可读数据
    
    if(len < 0) {
        // 写入失败，保存错误码
        *saveErrno = errno;
        return len;
    } 
    
    readPos_ += len;  // 更新读指针，标记已写入的数据为已读
    return len;
}

// ==================== 私有辅助函数实现 ====================

/**
 * @brief 获取缓冲区起始指针（可写版本）
 * @return char* 缓冲区起始地址
 * 
 * 技术要点：
 * &*buffer_.begin() 是获取vector底层数组指针的标准方法
 * 等价于 buffer_.data()（C++11后推荐使用data()）
 */
char* Buffer::BeginPtr_() {
    return &*buffer_.begin();
}

/**
 * @brief 获取缓冲区起始指针（只读版本）
 * @return const char* 缓冲区起始地址
 */
const char* Buffer::BeginPtr_() const {
    return &*buffer_.begin();
}

/**
 * @brief 智能空间管理函数 - 确保缓冲区有足够的写入空间
 * @param len 需要的空间大小（字节数）
 * 
 * 主人，让我用最简单的方式给您解释这个函数！
 * 
 * 【这个函数是做什么的？】
 * 想象您有一个笔记本，现在要写新内容，但发现空白页不够了。
 * 这个函数就是帮您解决"空间不够"问题的智能管家！
 * 
 * 【两种解决方案】：
 * 
 * 方案1：整理现有空间（省钱方案）
 * - 就像整理笔记本：把前面已经看过的内容撕掉，腾出空间
 * - 把还有用的内容移到笔记本前面，后面就有空白页了
 * - 好处：不用买新笔记本，省钱！
 * 
 * 方案2：买更大的笔记本（土豪方案）
 * - 当整理后空间还是不够时，就买个更大的笔记本
 * - 把所有内容都搬到新笔记本里
 * - 好处：空间绝对够用！
 * 
 * 【详细执行过程】：
 */
void Buffer::MakeSpace_(size_t len) {
    // 第1步：计算总的可用空间
    // WritableBytes() = 后面的空白空间
    // PrependableBytes() = 前面已读完的废弃空间  
    // 就像计算：笔记本后面空白页 + 前面可以撕掉的页数
    
    if(WritableBytes() + PrependableBytes() < len) {
        // 情况1：即使整理空间也不够，必须扩容
        // 就像：即使把废纸都撕掉，空间还是不够写新内容
        
        // 解决方案：买个更大的笔记本
        // writePos_ + len + 1 的含义：
        // - writePos_：当前已用的空间
        // - len：新需要的空间  
        // - +1：额外的安全空间（防止刚好用完）
        // 【详细解释buffer_.resize()这行代码】
        // 
        // 1. 【buffer_是什么？】
        //    buffer_是一个std::vector<char>类型的成员变量
        //    想象成一个可以自动变大变小的"魔法数组"
        //    就像一个可以伸缩的橡皮筋盒子
        //
        // 2. 【resize()函数是做什么的？】
        //    resize()是std::vector的成员函数，作用是"改变数组大小"
        //    - 如果新大小比原来大：就在后面添加新的空位置
        //    - 如果新大小比原来小：就把后面多余的位置删掉
        //    就像告诉魔法盒子："请变成这么大的尺寸"
        //
        // 3. 【writePos_ + len + 1 是什么意思？】
        //    让我们分解这个计算：
        //    
        //    writePos_：当前写指针的位置（已经用了多少空间）
        //    比如：writePos_ = 50，意思是前50个位置已经有数据了
        //    
        //    len：这次需要写入的数据长度
        //    比如：len = 30，意思是要写入30个字节的新数据
        //    
        //    +1：额外的安全空间
        //    防止刚好用完，留一点缓冲
        //    
        //    所以：writePos_ + len + 1 = 50 + 30 + 1 = 81
        //    意思是："我需要一个至少81个位置的数组"
        //
        // 4. 【具体执行过程】：
        //    假设原来buffer_大小是60：
        //    [数据][数据]...[数据][空][空]...[空]  <- 总共60个位置
        //     0    1      49   50  51     59
        //                     ↑
        //                  writePos_=50
        //    
        //    现在要写入30个字节，但只剩10个空位置（60-50=10），不够用！
        //    
        //    执行resize(81)后：
        //    [数据][数据]...[数据][空][空]...[空][空]...[空]  <- 总共81个位置
        //     0    1      49   50  51     59  60     80
        //                     ↑
        //                  writePos_=50，现在后面有31个空位置够用了！
        //
        // 5. 【生活化比喻】：
        //    想象您有一个文件夹，里面已经放了50张纸
        //    现在要再放30张纸，但文件夹只能装60张，空间不够
        //    resize()就像换一个能装81张纸的大文件夹
        //    把原来的50张纸放进去，还能再放31张新纸
        //
        // 6. 【为什么要+1？】
        //    这是程序员的"保险习惯"
        //    就像买菜时多买一点，防止不够用
        //    万一计算有微小误差，多1个位置可以避免越界错误
        //
        // 7. 【内存分配过程】：
        //    当调用resize()时，std::vector会：
        //    - 申请一块新的更大的内存空间
        //    - 把原来的数据复制到新空间
        //    - 释放原来的旧空间
        //    - 更新内部指针指向新空间
        //    就像搬家：找个大房子，把东西搬过去，退掉小房子
        
        buffer_.resize(writePos_ + len + 1);  // 扩容：让数组变成足够大的尺寸
        
        // 生活化比喻：
        // 原来笔记本100页，现在需要150页，就买个200页的（多买50页做备用）
    } 
    else {
        // 情况2：整理现有空间就够用了
        // 就像：把前面看过的内容撕掉，腾出的空间够写新内容
        
        // 第1步：记住还有多少有用的内容
        size_t readable = ReadableBytes();  // 还没读完的内容有多少
        
        // 第2步：把有用的内容移到笔记本开头
        // 详细解释这行代码：
        // - BeginPtr_() + readPos_：有用内容的开始位置
        // - BeginPtr_() + writePos_：有用内容的结束位置  
        // - BeginPtr_()：笔记本的第1页
        // 意思：把"第readPos_页到第writePos_页"的内容，搬到"第1页开始"的位置
        std::copy(BeginPtr_() + readPos_, BeginPtr_() + writePos_, BeginPtr_());
        
        // 第3步：更新书签位置
        readPos_ = 0;                    // 读书签放到第1页（因为内容移到开头了）
        writePos_ = readPos_ + readable; // 写书签 = 读书签 + 内容长度
        
        // 第4步：检查搬家是否成功
        // 确保搬家后，有用内容的数量没有变化
        assert(readable == ReadableBytes());
        
        // 生活化比喻：
        // 1. 原来笔记本：[废纸][废纸][有用内容][空白页不够]
        // 2. 整理后：[有用内容][空白页够用了][空白页][空白页]
        // 3. 更新书签：读书签指向有用内容开头，写书签指向空白页开头
    }
    
    // 【总结】：
    // 这个函数就像一个聪明的空间管理员
    // 优先选择"整理空间"（省资源）
    // 实在不够才选择"扩容"（花资源）
    // 确保您随时都有足够的空间写入新数据！
}