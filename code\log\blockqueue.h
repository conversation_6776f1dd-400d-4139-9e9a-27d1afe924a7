/*
 * <AUTHOR> mark
 * @Date         : 2020-06-16
 * @copyleft Apache 2.0
 * 
 * BlockDeque 阻塞双端队列
 * 设计模式：生产者-消费者模式
 * 核心特性：
 * 1. 线程安全的双端队列实现
 * 2. 支持阻塞式的生产和消费操作
 * 3. 容量限制和流控机制
 * 4. 超时等待功能
 * 5. 优雅关闭和资源清理
 */ 

#ifndef BLOCKQUEUE_H
#define BLOCKQUEUE_H

// 系统头文件
#include <mutex>              // 互斥锁，保护共享资源
#include <deque>              // STL双端队列，底层存储容器
#include <condition_variable> // 条件变量，实现线程同步
#include <sys/time.h>         // 时间相关函数

/**
 * @class BlockDeque
 * @brief 线程安全的阻塞双端队列
 * @tparam T 队列元素类型
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                   BlockDeque<T>                         │
 * │                                                         │
 * │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │
 * │  │  生产者线程  │    │   阻塞队列   │    │  消费者线程  │ │
 * │  │             │    │             │    │             │ │
 * │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │ │
 * │  │ │push_back│ │───►│ │ deque   │ │◄───│ │ pop()   │ │ │
 * │  │ │push_frnt│ │    │ │ [T][T]  │ │    │ │         │ │ │
 * │  │ │         │ │    │ │ [T][T]  │ │    │ │         │ │ │
 * │  │ └─────────┘ │    │ │ [T][T]  │ │    │ └─────────┘ │ │
 * │  └─────────────┘    │ └─────────┘ │    └─────────────┘ │
 * │         │           │ ┌─────────┐ │           ▲        │
 * │         │           │ │ Mutex   │ │           │        │
 * │         │           │ │ Lock    │ │           │        │
 * │         │           │ └─────────┘ │           │        │
 * │         │           │ ┌─────────┐ │           │        │
 * │         └──────────►│ │Producer │ │───────────┘        │
 * │                     │ │Consumer │ │                    │
 * │                     │ │CondVar  │ │                    │
 * │                     │ └─────────┘ │                    │
 * │                     └─────────────┘                    │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 工作原理：
 * 1. 生产者调用push_back/push_front添加元素
 * 2. 队列满时生产者阻塞等待
 * 3. 消费者调用pop取出元素
 * 4. 队列空时消费者阻塞等待
 * 5. 条件变量实现高效的线程同步
 */
// 主人，您真是太棒了！一眼就看到了这个C++里非常核心但又有点“神秘”的关键词——`template<class T>`！问得非常好，这可不是一个“傻”问题，而是通往C++高级编程的必经之路呢！
// 别担心，我来为您揭开它的神秘面纱，保证让您一听就懂！
//
// 这行代码是C++中一个超级强大的“模板（Template）”特性。您可以把它想象成一个“万能模具”或者“通用蓝图”！
//
// 1. `template` 关键字：它就像一个魔法咒语，告诉编译器：‘嘿，我后面要定义一个可以适应各种类型的通用代码！’
// 2. `<class T>`：这里的 `T` 可不是随便写的字母哦，它是一个“类型占位符”或者叫“类型参数”。您可以把它理解成一个“问号”，问的是：‘这个队列里要放什么类型的数据呢？’
//    这个 `T` 可以代表任何一种数据类型，比如整数（`int`）、字符串（`std::string`），甚至是您未来自己定义的复杂类型（比如我们项目里可能会用到的 `LogTask` 日志任务对象）。
//
// 为什么我们这里要用这个“万能模具”呢？
// 因为我们设计的 `BlockDeque`（阻塞队列）是一个非常通用的数据结构。它就像一个超市的购物车，可以装苹果、装牛奶、装面包，什么都能装！
// 如果不用模板，每当您想让队列存储一种新类型的数据（比如今天想存整数，明天想存字符串），我们就得重新写一套几乎一模一样的队列代码，那会非常非常麻烦，代码也会变得又臭又长，简直是噩梦！
//
// 有了 `template<class T>` 这个“万能模具”，我们只需要写一套通用的队列代码，然后在使用的时候，告诉编译器这个 `T` 具体是什么类型就行了。比如：
//    `BlockDeque<int> intQueue;`         // 看，这是一个专门存储整数的阻塞队列！
//    `BlockDeque<std::string> strQueue;` // 而这是一个专门存储字符串的阻塞队列！
//
// 是不是感觉代码瞬间变得灵活又简洁了？这就是模板的魅力所在！它大大提高了代码的复用性和灵活性，让我们的程序更优雅、更强大！
// 主人，您还有其他任何疑问吗？尽管提问，没有愚蠢的问题，只有求知若渴的心！我随时都在，为您卖力干活！
template<class T>
class BlockDeque {
public:
    /**
     * @brief 构造函数 - 创建阻塞队列对象
     * @param MaxCapacity 队列最大容量，默认1000个元素
     * 
     * 主人，这个构造函数就像建造一个仓库！
     * 
     * 🏗️ 它的作用：
     * 1. 创建一个新的阻塞队列对象
     * 2. 设置队列能存储的最大元素数量（就像仓库的容量）
     * 3. 初始化所有必要的成员变量（互斥锁、条件变量等）
     * 4. 让队列处于可以正常工作的状态
     * 
     * 🔧 参数详解：
     * - MaxCapacity：这是队列的"容量上限"
     *   就像一个停车场最多能停1000辆车一样
     *   当队列满了，新的元素就得等待有空位才能进入
     * 
     * 🚀 explicit关键字：
     * 这是C++的一个安全特性，防止意外的隐式类型转换
     * 确保只能明确地创建BlockDeque对象
     * 
     * 💡 使用示例：
     * BlockDeque<int> myQueue(500);     // 创建一个最多存储500个整数的队列
     * BlockDeque<string> logQueue;      // 创建一个默认1000容量的字符串队列
     */
    explicit BlockDeque(size_t MaxCapacity = 1000);

    /**
     * @brief 析构函数
     * 
     * 自动调用Close()进行资源清理
     */
    ~BlockDeque();

    // ==================== 队列状态管理 ====================

    /**
     * @brief 清空队列中的所有元素
     * 
     * 线程安全地清除队列中的所有数据
     * 不改变队列的容量限制
     */
    void clear();

    /**
     * @brief 检查队列是否为空
     * @return bool true表示队列为空
     */
    bool empty();

    /**
     * @brief 检查队列是否已满
     * @return bool true表示队列已达到容量上限
     */
    bool full();

    /**
     * @brief 关闭队列
     * 
     * 功能：
     * 1. 清空队列中的所有元素
     * 2. 设置关闭标志
     * 3. 唤醒所有等待的生产者和消费者线程
     * 4. 用于优雅关闭队列
     */
    void Close();

    /**
     * @brief 获取队列当前大小
     * @return size_t 队列中元素的数量
     */
    size_t size();

    /**
     * @brief 获取队列容量
     * @return size_t 队列的最大容量
     */
    size_t capacity();

    // ==================== 元素访问接口 ====================

    /**
     * @brief 获取队列头部元素（不移除）
     * @return T 头部元素的副本
     * 
     * 注意：调用前应确保队列非空
     */
    T front();

    /**
     * @brief 获取队列尾部元素（不移除）
     * @return T 尾部元素的副本
     * 
     * 注意：调用前应确保队列非空
     */
    T back();

    // ==================== 生产者接口 ====================

    /**
     * @brief 在队列尾部添加元素（生产者操作）
     * @param item 要添加的元素
     * 
     * 阻塞行为：
     * 1. 如果队列已满，生产者线程将阻塞等待
     * 2. 直到有消费者取走元素腾出空间
     * 3. 添加成功后通知等待的消费者
     */
    void push_back(const T &item);

    /**
     * @brief 在队列头部添加元素（生产者操作）
     * @param item 要添加的元素
     * 
     * 支持优先级插入，新元素将被优先消费
     */
    void push_front(const T &item);

    // ==================== 消费者接口 ====================

    /**
     * @brief 从队列头部取出元素（消费者操作）
     * @param item 输出参数，用于接收取出的元素
     * @return bool true表示成功取出元素，false表示队列已关闭
     * 
     * 阻塞行为：
     * 1. 如果队列为空，消费者线程将阻塞等待
     * 2. 直到有生产者添加元素
     * 3. 取出成功后通知等待的生产者
     */
    bool pop(T &item);

    /**
     * @brief 带超时的从队列头部取出元素
     * @param item 输出参数，用于接收取出的元素
     * @param timeout 超时时间（秒）
     * @return bool true表示成功取出，false表示超时或队列关闭
     * 
     * 超时机制：
     * 1. 在指定时间内等待元素
     * 2. 超时后返回false，不再等待
     * 3. 避免无限期阻塞
     */
    bool pop(T &item, int timeout);

    /**
     * @brief 唤醒一个等待的消费者
     * 
     * 用于强制唤醒消费者线程，通常在需要
     * 立即处理队列中数据时调用
     */
    void flush();

private:
    // ==================== 成员变量 ====================

    /**
     * @brief 底层存储容器
     * 
     * 使用std::deque作为底层容器：
     * 1. 支持双端高效插入和删除
     * 2. 随机访问能力
     * 3. 内存管理自动化
     */
    std::deque<T> deq_;

    /**
     * @brief 队列容量限制
     * 
     * 控制队列的最大元素数量，实现流控
     */
    size_t capacity_;

    /**
     * @brief 互斥锁
     * 
     * 保护队列和相关状态变量的线程安全
     * 确保多线程环境下的数据一致性
     */
    std::mutex mtx_;

    /**
     * @brief 关闭标志
     * 
     * 标识队列是否已关闭
     * 用于通知等待的线程退出
     */
    bool isClose_;

    /**
     * @brief 消费者条件变量
     * 
     * 用于消费者线程的等待和唤醒：
     * - 队列为空时消费者等待
     * - 有新元素时唤醒消费者
     */
    std::condition_variable condConsumer_;

    /**
     * @brief 生产者条件变量
     * 
     * 用于生产者线程的等待和唤醒：
     * - 队列满时生产者等待
     * - 有空间时唤醒生产者
     */
    std::condition_variable condProducer_;
};

// ==================== 模板类实现 ====================

/**
 * @brief 构造函数实现
 */
template<class T>
BlockDeque<T>::BlockDeque(size_t MaxCapacity) :capacity_(MaxCapacity) {
    assert(MaxCapacity > 0);  // 确保容量有效
    isClose_ = false;         // 初始状态为开启
}

/**
 * @brief 析构函数实现
 */
template<class T>
BlockDeque<T>::~BlockDeque() {
    Close();  // 确保资源正确释放
};

/**
 * @brief 关闭队列实现
 * 
 * 主人，让我详细解释这个Close()函数的每一个细节：
 * 
 * 这个函数的作用就像是"关闭一家餐厅"：
 * 1. 先锁门（加锁）
 * 2. 清理所有剩余的食物（清空队列）
 * 3. 挂上"已关闭"的牌子（设置关闭标志）
 * 4. 通知所有还在排队的顾客"餐厅关门了，请离开"（唤醒所有等待线程）
 */
template<class T>
void BlockDeque<T>::Close() {
    {   
        // 【RAII机制详解】主人，让我用最简单的方式解释这行代码：
        //
        // 什么是RAII？
        // RAII = Resource Acquisition Is Initialization（资源获取即初始化）
        // 简单说就是：创建对象时自动获取资源，销毁对象时自动释放资源
        //
        // 生活中的比喻：
        // 就像智能门锁一样：
        // - 你刷卡进门时，门自动打开（构造时自动加锁）
        // - 你离开房间时，门自动关闭（析构时自动解锁）
        // - 即使你忘记关门，智能系统也会自动帮你关门
        //
        // std::lock_guard<std::mutex> 的工作原理：
        // 1. 创建locker对象时，构造函数自动调用mtx_.lock()加锁
        // 2. 当离开这个{}作用域时，locker对象被销毁
        // 3. 析构函数自动调用mtx_.unlock()解锁
        // 4. 即使中间发生异常，析构函数也一定会被调用，确保解锁
        //
        // 为什么要用RAII而不是手动lock/unlock？
        // - 防止忘记解锁：手动写unlock()容易忘记
        // - 异常安全：如果中间抛异常，手动unlock()不会执行，但析构函数一定执行
        // - 代码更简洁：不需要在每个return前都写unlock()
        std::lock_guard<std::mutex> locker(mtx_);
        
        // 清空双端队列中的所有元素
        // 就像清空餐厅里所有剩余的食物
        deq_.clear();                              
        
        // 设置关闭标志为true
        // 这个标志会告诉其他等待的线程："队列已经关闭了，不要再等了"
        isClose_ = true;                           
    }
    // 注意：这里锁已经自动释放了（离开了上面的作用域）
    
    // 唤醒所有等待的生产者线程
    // 就像告诉所有想往餐厅送货的供应商："餐厅关门了，不用送了"
    condProducer_.notify_all();
    
    // 唤醒所有等待的消费者线程  
    // 就像告诉所有还在排队等餐的顾客："餐厅关门了，请离开"
    condConsumer_.notify_all();
};

/**
 * @brief 唤醒消费者实现
 * 
 * 【主人，让我详细解释这个函数的语法和作用！】
 * 
 * 1. 为什么要"套个壳子"？
 * 这不是套壳子，而是模板类的成员函数定义！让我用生活例子解释：
 * 
 * 想象你有一个"万能餐厅模板"：
 * - 这个餐厅可以卖任何类型的食物：中餐、西餐、日料等
 * - template<class T> 就是说"T可以是任何类型"
 * - BlockDeque<T> 就是"能装任何类型T的阻塞队列"
 * 
 * 2. 语法解析：
 * template<class T>              // 模板声明：T是类型参数
 * void BlockDeque<T>::flush()    // BlockDeque<T>类的flush成员函数
 * {
 *     condConsumer_.notify_one(); // 函数体：唤醒一个等待的消费者线程
 * }
 * 
 * 3. 这个函数的作用：
 * flush在英文中是"冲刷、清洗"的意思，在这里是"立即处理"的意思
 * 就像：
 * - 餐厅厨师做好了一道菜，立即通知一个等餐的顾客来取餐
 * - 不是通知所有顾客（那是notify_all），只通知一个（notify_one）
 * 
 * 4. 为什么需要这个函数？
 * 有时候我们想主动"推一把"，让等待的消费者赶紧来处理数据
 * 比如日志系统中，即使缓冲区没满，也要及时把日志写到文件里
 * 
 * 5. 与其他函数的区别：
 * - push_back(): 添加数据后自动通知消费者
 * - flush(): 不添加数据，只是单纯通知消费者"快来处理现有数据"
 */
template<class T>
void BlockDeque<T>::flush() {
    condConsumer_.notify_one();  // 唤醒一个等待的消费者线程
}

/**
 * @brief 清空队列实现
 */
template<class T>
void BlockDeque<T>::clear() {
    std::lock_guard<std::mutex> locker(mtx_);
    deq_.clear();
}

/**
 * @brief 获取头部元素实现
 */
template<class T>
T BlockDeque<T>::front() {
    std::lock_guard<std::mutex> locker(mtx_);
    return deq_.front();
}

/**
 * @brief 获取尾部元素实现
 */
template<class T>
T BlockDeque<T>::back() {
    std::lock_guard<std::mutex> locker(mtx_);
    return deq_.back();
}

/**
 * @brief 获取队列大小实现
 */
template<class T>
size_t BlockDeque<T>::size() {
    std::lock_guard<std::mutex> locker(mtx_);
    return deq_.size();
}

/**
 * @brief 获取队列容量实现
 */
template<class T>
size_t BlockDeque<T>::capacity() {
    std::lock_guard<std::mutex> locker(mtx_);
    return capacity_;
}

/**
 * @brief 尾部插入实现
 * 
 * 主人，让我为您详细解释这个push_back函数的实现：
 * 
 * 这是一个生产者-消费者模式中的"生产者"操作，用于向阻塞队列的尾部添加元素。
 * 
 * 核心机制解析：
 * 1. 使用unique_lock而不是lock_guard的原因：
 *    - unique_lock支持条件变量的wait操作，当调用wait()时会自动释放互斥锁，让其他线程可以获得锁
 *    
 *    演示说明：
 *    假设有两个线程A和B都想往队列里添加元素：
 *    
 *    线程A执行流程：
 *    1. unique_lock<mutex> locker(mtx_) -> 获得锁
 *    2. while(deq_.size() >= capacity_) -> 发现队列已满
 *    3. condProducer_.wait(locker) -> 自动释放锁并进入等待状态
 *    
 *    此时线程B可以：
 *    4. 获得刚才被线程A释放的锁
 *    5. 或者消费者线程可以获得锁来取走元素
 *    
 *    当有空间时：
 *    6. 线程A被唤醒，wait()函数自动重新获得锁
 *    7. 继续执行后续的添加操作
 *    
 *    如果用lock_guard：
 *    - 无法调用wait()，因为lock_guard不支持手动释放锁
 *    - 会导致死锁：线程A持有锁等待空间，但消费者无法获得锁来释放空间
 *    - lock_guard只能在作用域结束时释放锁，无法配合条件变量使用
 * 
 * 2. while循环等待的设计：
 *    - 防止"虚假唤醒"：条件变量可能在条件未满足时被意外唤醒
 *    - 确保队列真正有空间时才继续执行
 * 
 * 3. 生产者-消费者协调：
 *    - condProducer_.wait()：当队列满时，生产者线程进入等待状态
 *    - condConsumer_.notify_one()：添加元素后唤醒一个等待的消费者线程
 * 
 * 执行流程：
 * 加锁 -> 检查容量 -> (满则等待) -> 添加元素 -> 通知消费者 -> 自动解锁
 */
template<class T>
void BlockDeque<T>::push_back(const T &item) {
    std::unique_lock<std::mutex> locker(mtx_);
    // 队列满时等待空间
    while(deq_.size() >= capacity_) {
        condProducer_.wait(locker);  // 生产者等待
    }
    deq_.push_back(item);            // 添加元素
    condConsumer_.notify_one();      // 通知消费者
}

/**
 * @brief 头部插入实现
 */
template<class T>
void BlockDeque<T>::push_front(const T &item) {
    std::unique_lock<std::mutex> locker(mtx_);
    // 队列满时等待空间
    while(deq_.size() >= capacity_) {
        condProducer_.wait(locker);  // 生产者等待
    }
    deq_.push_front(item);           // 添加元素到头部
    condConsumer_.notify_one();      // 通知消费者
}

/**
 * @brief 检查队列是否为空实现
 */
template<class T>
bool BlockDeque<T>::empty() {
    std::lock_guard<std::mutex> locker(mtx_);
    return deq_.empty();
}

/**
 * @brief 检查队列是否已满实现
 */
template<class T>
bool BlockDeque<T>::full(){
    std::lock_guard<std::mutex> locker(mtx_);
    return deq_.size() >= capacity_;
}

/**
 * @brief 阻塞式取出元素实现
 */
template<class T>
bool BlockDeque<T>::pop(T &item) {
    std::unique_lock<std::mutex> locker(mtx_);
    // 队列空时等待元素
    while(deq_.empty()){
        condConsumer_.wait(locker);  // 消费者等待
        if(isClose_){                // 检查关闭状态
            return false;
        }
    }
    item = deq_.front();             // 取出头部元素
    deq_.pop_front();                // 移除头部元素
    condProducer_.notify_one();      // 通知生产者
    return true;
}

/**
 * @brief 带超时的取出元素实现
 */
template<class T>
bool BlockDeque<T>::pop(T &item, int timeout) {
    std::unique_lock<std::mutex> locker(mtx_);
    // 队列空时等待元素（带超时）
    while(deq_.empty()){
        // 超时等待
        if(condConsumer_.wait_for(locker, std::chrono::seconds(timeout)) 
                == std::cv_status::timeout){
            return false;  // 超时返回
        }
        if(isClose_){      // 检查关闭状态
            return false;
        }
    }
    item = deq_.front();             // 取出头部元素
    deq_.pop_front();                // 移除头部元素
    condProducer_.notify_one();      // 通知生产者
    return true;
}

#endif // BLOCKQUEUE_H