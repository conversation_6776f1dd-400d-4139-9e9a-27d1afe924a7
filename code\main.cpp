/*
 * <AUTHOR> mark
 * @Date         : 2020-06-18
 * @copyleft Apache 2.0
 * 
 * WebServer 主程序入口
 * 功能：初始化并启动高性能Web服务器
 * 架构：Reactor + 线程池混合模式
 */ 

#include <unistd.h>           // Unix标准函数库，提供daemon等系统调用
#include "server/webserver.h" // WebServer核心类头文件

/**
 * @brief 程序主入口函数
 * @return int 程序退出状态码
 * 
 * 主要功能：
 * 1. 配置服务器参数（端口、数据库、线程池等）
 * 2. 创建WebServer实例
 * 3. 启动服务器主循环
 */
int main() {
    /* 
     * 守护进程配置（可选）
     * daemon(1, 0): 将程序转为后台守护进程运行
     * 参数1: nochdir=1 不改变当前工作目录
     * 参数2: noclose=0 关闭标准输入输出，重定向到/dev/null
     * 生产环境建议启用，开发调试时注释掉
     */
    //daemon(1, 0); 

    /*
     * 创建WebServer实例，配置各项参数
     * 这里体现了现代C++的构造函数参数传递方式
     */
    WebServer server(
        /* 网络配置参数 */
        1316,           // 监听端口号：服务器绑定的TCP端口
        3,              // 触发模式：3表示listenfd和connfd都使用ET(边缘触发)模式
        60000,          // 超时时间：连接超时时间(毫秒)，60秒无活动则关闭连接
        false,          // 优雅退出：false表示立即关闭，true表示等待处理完当前请求
        
        /* MySQL数据库配置参数 */
        3306,           // 数据库端口：MySQL默认端口
        "webserver",    // 数据库用户名：连接MySQL的用户名
        "bht040216",    // 数据库密码：连接MySQL的密码（实际使用时应从配置文件读取）
        "yourdb",       // 数据库名称：要连接的数据库名
        
        /* 资源池配置参数 */
        12,             // 数据库连接池大小：预创建的MySQL连接数量
        6,              // 线程池大小：工作线程数量，通常设为CPU核心数
        true,           // 日志开关：true启用日志记录，false关闭日志
        1,              // 日志等级：0-DEBUG, 1-INFO, 2-WARN, 3-ERROR
        1024            // 日志异步队列容量：异步日志缓冲区大小
    );
    
    /*
     * 启动服务器主循环
     * 这里会进入事件循环，处理客户端连接和请求
     * 程序将在此处阻塞，直到服务器关闭
     */
    server.Start();
    
    return 0; // 程序正常退出
} 
  