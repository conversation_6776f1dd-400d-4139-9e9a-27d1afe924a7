/*
 * <AUTHOR> mark
 * @Date         : 2020-06-15
 * @copyleft Apache 2.0
 * 
 * Epoller epoll事件管理器实现
 * 
 * What (是什么)：
 * Linux epoll系统调用的C++封装，提供高效的IO多路复用事件管理
 * 
 * Why (为什么)：
 * 1. epoll相比select/poll具有更好的性能表现
 * 2. 支持大量并发连接（C10K问题的解决方案）
 * 3. 边缘触发模式减少系统调用次数
 * 4. 封装底层细节提供简洁的API接口
 * 5. 自动管理epoll文件描述符生命周期
 * 
 * How (怎么做)：
 * 1. RAII方式管理epoll文件描述符
 * 2. 使用vector缓存epoll_event结构
 * 3. 提供添加、修改、删除文件描述符的接口
 * 4. 统一的事件等待和结果获取机制
 * 5. 异常安全的资源管理
 */ 

#include "epoller.h"

// ==================== 构造与析构函数 ====================

/**
 * @brief 构造函数，创建epoll实例
 * @param maxEvent 最大同时处理的事件数量
 * 
 * What: 初始化epoll事件管理器并分配事件缓冲区
 * Why:  创建epoll实例用于后续的文件描述符管理和事件监听
 * How:  
 * 1. 调用epoll_create1创建epoll文件描述符
 * 2. 预分配events_向量用于存储就绪事件
 * 3. 验证创建结果确保初始化成功
 */
Epoller::Epoller(int maxEvent):epollFd_(epoll_create(512)), events_(maxEvent){
    assert(epollFd_ >= 0 && events_.size() > 0);
}

/**
 * @brief 析构函数，清理epoll资源
 * 
 * What: 关闭epoll文件描述符，释放系统资源
 * Why:  RAII原则，确保系统资源被正确释放
 * How:  调用close()系统调用关闭epoll文件描述符
 */
Epoller::~Epoller() {
    close(epollFd_);
}

// ==================== 文件描述符管理接口 ====================

/**
 * @brief 向epoll实例添加文件描述符
 * @param fd 要添加的文件描述符
 * @param events 监听的事件类型
 * @return bool 添加是否成功
 * 
 * What: 将新的文件描述符注册到epoll实例中进行事件监听
 * Why:  扩展epoll监听的文件描述符集合，支持新连接的事件处理
 * How:  
 * 1. 构造epoll_event结构体
 * 2. 设置监听的事件类型和文件描述符
 * 3. 调用epoll_ctl进行EPOLL_CTL_ADD操作
 */
bool Epoller::AddFd(int fd, uint32_t events) {
    if(fd < 0) return false;        // 验证文件描述符有效性
    
    epoll_event ev = {0};           // 初始化事件结构体
    ev.data.fd = fd;                // 设置文件描述符
    ev.events = events;             // 设置监听事件类型
    
    // 执行添加操作：EPOLL_CTL_ADD表示添加新的文件描述符
    return 0 == epoll_ctl(epollFd_, EPOLL_CTL_ADD, fd, &ev);
}

/**
 * @brief 修改文件描述符的监听事件
 * @param fd 要修改的文件描述符
 * @param events 新的事件类型
 * @return bool 修改是否成功
 * 
 * What: 更新已注册文件描述符的事件监听类型
 * Why:  根据连接状态动态调整监听事件（如从读事件切换到写事件）
 * How:  
 * 1. 构造新的epoll_event结构体
 * 2. 设置更新后的事件类型
 * 3. 调用epoll_ctl进行EPOLL_CTL_MOD操作
 */
bool Epoller::ModFd(int fd, uint32_t events) {
    if(fd < 0) return false;        // 验证文件描述符有效性
    
    epoll_event ev = {0};           // 初始化事件结构体
    ev.data.fd = fd;                // 设置文件描述符
    ev.events = events;             // 设置新的事件类型
    
    // 执行修改操作：EPOLL_CTL_MOD表示修改已存在的文件描述符
    return 0 == epoll_ctl(epollFd_, EPOLL_CTL_MOD, fd, &ev);
}

/**
 * @brief 从epoll实例删除文件描述符
 * @param fd 要删除的文件描述符
 * @return bool 删除是否成功
 * 
 * What: 从epoll实例中移除文件描述符，停止事件监听
 * Why:  清理已关闭或不再需要监听的文件描述符，避免资源泄漏
 * How:  
 * 1. 构造空的epoll_event结构体（删除操作可以传空指针）
 * 2. 调用epoll_ctl进行EPOLL_CTL_DEL操作
 */
bool Epoller::DelFd(int fd) {
    if(fd < 0) return false;        // 验证文件描述符有效性
    
    epoll_event ev = {0};           // 初始化事件结构体（删除时可为空）
    
    // 执行删除操作：EPOLL_CTL_DEL表示删除文件描述符
    return 0 == epoll_ctl(epollFd_, EPOLL_CTL_DEL, fd, &ev);
}

// ==================== 事件等待与处理接口 ====================

/**
 * @brief 等待文件描述符事件就绪
 * @param timeoutMs 超时时间（毫秒），-1表示永久等待
 * @return int 就绪的事件数量，-1表示出错
 * 
 * What: 阻塞等待监听的文件描述符上有事件发生
 * Why:  实现事件驱动的IO模型，高效处理大量并发连接
 * How:  
 * 1. 调用epoll_wait系统调用等待事件
 * 2. 将就绪事件存储到events_向量中
 * 3. 返回就绪事件的数量供上层处理
 * 
 * 技术细节：
 * - 边缘触发(EPOLLET)：事件只在状态变化时触发一次
 * - 水平触发(默认)：只要条件满足就持续触发
 * - EPOLLONESHOT：事件触发后自动从epoll中移除，避免重复触发
 */
int Epoller::Wait(int timeoutMs) {
    // epoll_wait参数说明：
    // epollFd_: epoll实例文件描述符
    // &events_[0]: 用于存储就绪事件的数组首地址
    // static_cast<int>(events_.size()): 最大事件数量
    // timeoutMs: 超时时间，-1表示永久阻塞，0表示立即返回
    return epoll_wait(epollFd_, &events_[0], static_cast<int>(events_.size()), timeoutMs);
}

// ==================== 事件信息获取接口 ====================

/**
 * @brief 获取就绪事件的文件描述符
 * @param i 事件索引
 * @return int 对应的文件描述符
 * 
 * What: 从就绪事件列表中获取指定索引的文件描述符
 * Why:  为上层代码提供访问就绪事件详细信息的接口
 * How:  直接返回events_向量中指定位置的fd字段
 */
int Epoller::GetEventFd(size_t i) const {
    assert(i >= 0 && i < events_.size());  // 验证索引有效性
    return events_[i].data.fd;             // 返回文件描述符
}

/**
 * @brief 获取就绪事件的事件类型
 * @param i 事件索引
 * @return uint32_t 事件类型掩码
 * 
 * What: 从就绪事件列表中获取指定索引的事件类型
 * Why:  上层代码需要根据事件类型进行相应处理（读/写/错误等）
 * How:  直接返回events_向量中指定位置的events字段
 * 
 * 常见事件类型：
 * - EPOLLIN: 可读事件（有数据到达）
 * - EPOLLOUT: 可写事件（可以发送数据）
 * - EPOLLERR: 错误事件
 * - EPOLLHUP: 挂起事件（连接断开）
 * - EPOLLRDHUP: 对端关闭连接或写半部
 */
uint32_t Epoller::GetEvents(size_t i) const {
    assert(i >= 0 && i < events_.size());  // 验证索引有效性
    return events_[i].events;              // 返回事件类型
}