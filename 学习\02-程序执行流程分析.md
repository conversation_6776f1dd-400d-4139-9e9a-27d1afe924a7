# 第二课：程序执行流程深度分析(自顶向下)

> 主人，这是第二课！我将从程序入口开始，逐行为您分析整个WebServer的启动和运行流程。这是真正的手把手教学，每一行代码都会详细解释！

---

## 🎯 学习目标

通过本课学习，您将掌握：
1. **main函数的启动参数详解**
2. **WebServer构造函数的初始化过程**
3. **服务器启动的完整流程**
4. **事件循环的核心逻辑**

---

## 🚀 1. 程序入口详细分析 (`main.cpp`)

让我们先看完整的main.cpp文件：

```cpp
/*
 * <AUTHOR> mark
 * @Date         : 2020-06-18
 * @copyleft Apache 2.0
 */ 
#include <unistd.h>
#include "server/webserver.h"

int main() {
    /* 守护进程 后台运行 */
    //daemon(1, 0); 

    WebServer server(
        1316, 3, 60000, false,             /* 端口 ET模式 timeoutMs 优雅退出  */
        3306, "webserver", "bht040216", "yourdb", /* Mysql配置 */
        12, 6, true, 1, 1024);             /* 连接池数量 线程池数量 日志开关 日志等级 日志异步队列容量 */
    server.Start();
} 
```

### 1.1 头文件分析

```cpp
#include <unistd.h>              // Unix标准库，提供daemon函数
#include "server/webserver.h"    // WebServer主类头文件
```

**主人，这里的要点**：
- `unistd.h`：提供系统调用接口，包含daemon函数用于创建守护进程
- `webserver.h`：包含了WebServer类的声明

### 1.2 守护进程配置

```cpp
/* 守护进程 后台运行 */
//daemon(1, 0); 
```

**详细解释**：
- `daemon(1, 0)`：将进程转为守护进程
  - 第一个参数1：不改变当前工作目录
  - 第二个参数0：重定向标准输入输出到/dev/null
- **为什么注释掉**：开发调试时保持前台运行，便于观察输出

### 1.3 WebServer构造函数参数详解

```cpp
WebServer server(
    1316, 3, 60000, false,             /* 第一组：网络配置 */
    3306, "webserver", "bht040216", "yourdb", /* 第二组：数据库配置 */
    12, 6, true, 1, 1024);             /* 第三组：资源池配置 */
```

**参数详细分析**：

#### 📌 **第一组：网络配置参数**
```cpp
1316,      // port：监听端口号
3,         // trigMode：触发模式(0=LT+LT, 1=LT+ET, 2=ET+LT, 3=ET+ET)
60000,     // timeoutMS：连接超时时间(毫秒)
false,     // OptLinger：是否启用SO_LINGER选项
```

**主人，这里的关键知识**：
- **端口1316**：自定义端口，避免与系统端口冲突
- **触发模式3**：监听socket和连接socket都使用边缘触发(ET)
- **超时60秒**：超过这个时间的连接会被自动清理
- **SO_LINGER false**：关闭连接时不等待数据发送完成

#### 📌 **第二组：数据库配置参数**
```cpp
3306,           // sqlPort：MySQL端口
"webserver",    // sqlUser：数据库用户名
"bht040216",    // sqlPwd：数据库密码
"yourdb",       // dbName：数据库名
```

#### 📌 **第三组：资源池配置参数**
```cpp
12,     // connPoolNum：数据库连接池大小
6,      // threadNum：线程池线程数量
true,   // openLog：是否开启日志
1,      // logLevel：日志等级(0=DEBUG, 1=INFO, 2=WARN, 3=ERROR)
1024    // logQueSize：异步日志队列大小
```

---

## 🏗️ 2. WebServer构造函数深度分析

现在让我们深入WebServer的构造函数，看看服务器是如何初始化的：

```cpp
WebServer::WebServer(
    int port, int trigMode, int timeoutMS, bool OptLinger,
    int sqlPort, const char* sqlUser, const  char* sqlPwd,
    const char* dbName, int connPoolNum, int threadNum,
    bool openLog, int logLevel, int logQueSize):
    port_(port), openLinger_(OptLinger), timeoutMS_(timeoutMS), isClose_(false),
    timer_(new HeapTimer()), threadpool_(new ThreadPool(threadNum)), epoller_(new Epoller())
{
    srcDir_ = getcwd(nullptr, 256);              // 获取当前工作目录
    assert(srcDir_);
    strncat(srcDir_, "/resources/", 16);         // 拼接资源目录路径
    HttpConn::userCount = 0;                     // 初始化用户连接计数
    HttpConn::srcDir = srcDir_;                  // 设置静态资源目录
    SqlConnPool::Instance()->Init("localhost", sqlPort, sqlUser, sqlPwd, dbName, connPoolNum);
    
    InitEventMode_(trigMode);                    // 初始化事件模式
    if(!InitSocket_()) {                         // 初始化socket
        isClose_ = true;
    }

    if(openLog) {                                // 初始化日志系统
        Log::Instance()->init(logLevel, "./log", ".log", logQueSize);
        if(isClose_) { LOG_ERROR("========== Server init error!=========="); }
        else {
            LOG_INFO("========== Server init ==========");
            LOG_INFO("Port:%d, OpenLinger: %s", port_, OptLinger? "true":"false");
            LOG_INFO("Listen Mode: %s, OpenConn Mode: %s",
                            (listenEvent_ & EPOLLET ? "ET": "LT"),
                            (connEvent_ & EPOLLET ? "ET": "LT"));
            LOG_INFO("LogSys level: %d", logLevel);
            LOG_INFO("srcDir: %s", HttpConn::srcDir);
            LOG_INFO("SqlConnPool num: %d, ThreadPool num: %d", connPoolNum, threadNum);
        }
    }
}
```

### 2.1 成员变量初始化列表分析

```cpp
port_(port), openLinger_(OptLinger), timeoutMS_(timeoutMS), isClose_(false),
timer_(new HeapTimer()), threadpool_(new ThreadPool(threadNum)), epoller_(new Epoller())
```

**主人，这里使用了初始化列表的好处**：
- **效率更高**：直接调用构造函数，避免先默认构造再赋值
- **const成员**：可以初始化const成员变量
- **智能指针**：使用new创建对象，智能指针自动管理内存

### 2.2 工作目录和资源路径设置

```cpp
srcDir_ = getcwd(nullptr, 256);              // 获取当前工作目录
assert(srcDir_);
strncat(srcDir_, "/resources/", 16);         // 拼接资源目录路径
```

**详细解释**：
- `getcwd(nullptr, 256)`：获取当前工作目录，最大256字符
- `assert(srcDir_)`：断言确保获取目录成功
- `strncat()`：安全地拼接字符串，防止缓冲区溢出

### 2.3 静态成员初始化

```cpp
HttpConn::userCount = 0;                     // 初始化用户连接计数
HttpConn::srcDir = srcDir_;                  // 设置静态资源目录
```

**主人，这里的设计思想**：
- **静态成员**：所有HttpConn实例共享同一个用户计数和资源目录
- **全局状态**：便于统计和管理所有连接

### 2.4 数据库连接池初始化

```cpp
SqlConnPool::Instance()->Init("localhost", sqlPort, sqlUser, sqlPwd, dbName, connPoolNum);
```

**单例模式应用**：
- `Instance()`：获取单例实例
- `Init()`：初始化连接池，建立指定数量的数据库连接

### 2.5 事件模式初始化

```cpp
InitEventMode_(trigMode);                    // 初始化事件模式
```

让我们深入这个函数：

```cpp
void WebServer::InitEventMode_(int trigMode) {
    listenEvent_ = EPOLLRDHUP;               // 监听socket事件
    connEvent_ = EPOLLONESHOT | EPOLLRDHUP;  // 连接socket事件
    
    switch (trigMode) {
    case 0:     // LT + LT
        break;
    case 1:     // LT + ET
        connEvent_ |= EPOLLET;
        break;
    case 2:     // ET + LT
        listenEvent_ |= EPOLLET;
        break;
    case 3:     // ET + ET (本项目使用)
        listenEvent_ |= EPOLLET;
        connEvent_ |= EPOLLET;
        break;
    default:
        listenEvent_ |= EPOLLET;
        connEvent_ |= EPOLLET;
        break;
    }
    HttpConn::isET = (connEvent_ & EPOLLET);
}
```

**事件标志详解**：
- `EPOLLRDHUP`：检测对端关闭连接
- `EPOLLONESHOT`：事件只触发一次，防止多线程竞争
- `EPOLLET`：边缘触发模式，高性能但编程复杂

---

## 🌐 3. Socket初始化深度分析

### 3.1 InitSocket_()函数完整解析

```cpp
bool WebServer::InitSocket_() {
    int ret;
    struct sockaddr_in addr;
    if(port_ > 65535 || port_ < 1024) {      // 检查端口范围
        LOG_ERROR("Port:%d error!",  port_);
        return false;
    }
    addr.sin_family = AF_INET;               // IPv4协议族
    addr.sin_addr.s_addr = htonl(INADDR_ANY); // 监听所有网卡
    addr.sin_port = htons(port_);            // 端口号(网络字节序)
    
    struct linger optLinger = { 0 };         // SO_LINGER选项
    if(openLinger_) {
        /* 优雅关闭: 直到所剩数据发送完毕或超时 */
        optLinger.l_onoff = 1;
        optLinger.l_linger = 1;
    }

    listenFd_ = socket(PF_INET, SOCK_STREAM, 0);  // 创建socket
    if(listenFd_ < 0) {
        LOG_ERROR("Create socket error!", port_);
        return false;
    }

    ret = setsockopt(listenFd_, SOL_SOCKET, SO_LINGER, &optLinger, sizeof(optLinger));
    if(ret < 0) {
        close(listenFd_);
        LOG_ERROR("Init linger error!", port_);
        return false;
    }

    int optval = 1;
    /* 端口复用 */
    /* 只有最后一个套接字会正常接收数据 */
    ret = setsockopt(listenFd_, SOL_SOCKET, SO_REUSEADDR, (const void*)&optval, sizeof(int));
    if(ret == -1) {
        LOG_ERROR("set socket setsockopt error !");
        close(listenFd_);
        return false;
    }

    ret = bind(listenFd_, (struct sockaddr *)&addr, sizeof(addr));
    if(ret < 0) {
        LOG_ERROR("Bind Port:%d error!", port_);
        close(listenFd_);
        return false;
    }

    ret = listen(listenFd_, 6);              // 监听队列长度为6
    if(ret < 0) {
        LOG_ERROR("Listen port:%d error!", port_);
        close(listenFd_);
        return false;
    }
    ret = epoller_->AddFd(listenFd_, listenEvent_ | EPOLLIN);  // 添加到epoll
    if(ret == 0) {
        LOG_ERROR("Add listen error!");
        close(listenFd_);
        return false;
    }
    SetFdNonblock(listenFd_);                // 设置非阻塞
    LOG_INFO("Server port:%d", port_);
    return true;
}
```

### 3.2 关键系统调用详解

#### 📌 **socket()创建**
```cpp
listenFd_ = socket(PF_INET, SOCK_STREAM, 0);
```
- `PF_INET`：IPv4协议族
- `SOCK_STREAM`：TCP流式socket
- `0`：自动选择协议(TCP)

#### 📌 **setsockopt()配置**
```cpp
// SO_LINGER：优雅关闭配置
setsockopt(listenFd_, SOL_SOCKET, SO_LINGER, &optLinger, sizeof(optLinger));

// SO_REUSEADDR：端口复用
setsockopt(listenFd_, SOL_SOCKET, SO_REUSEADDR, (const void*)&optval, sizeof(int));
```

**主人，这两个选项的重要性**：
- **SO_LINGER**：控制close()的行为，是否等待数据发送完毕
- **SO_REUSEADDR**：允许重用TIME_WAIT状态的端口，便于快速重启

#### 📌 **bind()绑定**
```cpp
ret = bind(listenFd_, (struct sockaddr *)&addr, sizeof(addr));
```
- 将socket绑定到指定的IP地址和端口
- `INADDR_ANY`表示监听所有网卡

#### 📌 **listen()监听**
```cpp
ret = listen(listenFd_, 6);
```
- `6`：监听队列的最大长度
- 超过这个数量的连接请求会被拒绝

### 3.3 非阻塞设置

```cpp
int WebServer::SetFdNonblock(int fd) {
    assert(fd > 0);
    return fcntl(fd, F_SETFL, fcntl(fd, F_GETFD, 0) | O_NONBLOCK);
}
```

**为什么要设置非阻塞**：
- **提高并发性**：避免单个连接阻塞整个服务器
- **配合Epoll**：边缘触发模式要求非阻塞IO

---

## 🔄 4. 服务器启动流程 (server.Start())

### 4.1 主事件循环解析

```cpp
void WebServer::Start() {
    int timeMS = -1;  /* epoll wait timeout == -1 无事件将阻塞 */
    if(!isClose_) { LOG_INFO("========== Server start =========="); }
    while(!isClose_) {
        if(timeoutMS_ > 0) {
            timeMS = timer_->GetNextTick();  // 获取最近的超时时间
        }
        int eventCnt = epoller_->Wait(timeMS);  // 等待IO事件
        for(int i = 0; i < eventCnt; ++i) {
            /* 处理事件 */
            int fd = epoller_->GetEventFd(i);
            uint32_t events = epoller_->GetEvents(i);
            if(fd == listenFd_) {
                DealListen_();           // 处理新连接
            }
            else if(events & (EPOLLRDHUP | EPOLLHUP | EPOLLERR)) {
                assert(users_.count(fd) > 0);
                CloseConn_(&users_[fd]); // 关闭错误连接
            }
            else if(events & EPOLLIN) {
                assert(users_.count(fd) > 0);
                DealRead_(&users_[fd]);  // 处理读事件
            }
            else if(events & EPOLLOUT) {
                assert(users_.count(fd) > 0);
                DealWrite_(&users_[fd]); // 处理写事件
            } else {
                LOG_ERROR("Unexpected event");
            }
        }
    }
}
```

### 4.2 事件循环流程图

```
┌─────────────────────────────────────────────────────────┐
│                  WebServer 主事件循环                    │
└─────────────────────────────────────────────────────────┘
           │
           ▼
┌─────────────────────┐
│ timer_->GetNextTick()│  获取最近超时时间
└─────────────────────┘
           │
           ▼
┌─────────────────────┐
│ epoller_->Wait()    │  等待IO事件(可能阻塞)
└─────────────────────┘
           │
           ▼
┌─────────────────────┐
│ 遍历就绪的事件       │  for(int i = 0; i < eventCnt; ++i)
└─────────────────────┘
           │
           ▼
┌─────────────────────┐
│ 事件类型判断         │  if(fd == listenFd_) ...
└─────────────────────┘
           │
    ┌──────┼──────┬──────┬──────┐
    ▼      ▼      ▼      ▼      ▼
┌────────┐┌───────┐┌──────┐┌───────┐┌─────────┐
│新连接   ││读事件  ││写事件 ││错误事件││未知事件  │
│处理    ││处理   ││处理  ││处理   ││记录日志  │
└────────┘└───────┘└──────┘└───────┘└─────────┘
    │      │      │      │      │
    └──────┼──────┼──────┼──────┘
           │      │      │
           ▼      ▼      ▼
    ┌──────────────────────┐
    │ 循环继续，等待下次事件 │
    └──────────────────────┘
```

### 4.3 定时器机制分析

```cpp
if(timeoutMS_ > 0) {
    timeMS = timer_->GetNextTick();  // 获取最近的超时时间
}
int eventCnt = epoller_->Wait(timeMS);
```

**主人，这里的巧妙设计**：
- **动态超时**：根据最近的定时器确定epoll等待时间
- **及时处理**：确保超时连接能被及时清理
- **高效等待**：没有定时器时(-1)会无限等待，避免空轮询

---

## 🎯 5. 事件处理函数详解

### 5.1 新连接处理 (DealListen_)

```cpp
void WebServer::DealListen_() {
    struct sockaddr_in addr;
    socklen_t len = sizeof(addr);
    do {
        int fd = accept(listenFd_, (struct sockaddr *)&addr, &len);
        if(fd <= 0) { return; }
        else if(HttpConn::userCount >= MAX_FD) {
            SendError_(fd, "Server busy!");
            LOG_WARN("Clients is full!");
            return;
        }
        AddClient_(fd, addr);                // 添加新客户端
    } while(listenEvent_ & EPOLLET);         // ET模式需要循环处理
}
```

**边缘触发的特殊处理**：
- **do-while循环**：ET模式下一次事件可能对应多个连接
- **循环accept**：直到accept返回错误为止

### 5.2 添加客户端连接 (AddClient_)

```cpp
void WebServer::AddClient_(int fd, sockaddr_in addr) {
    assert(fd > 0);
    users_[fd].init(fd, addr);               // 初始化HttpConn对象
    if(timeoutMS_ > 0) {
        timer_->add(fd, timeoutMS_, std::bind(&WebServer::CloseConn_, this, &users_[fd]));
    }
    epoller_->AddFd(fd, EPOLLIN | connEvent_); // 添加到epoll监听
    SetFdNonblock(fd);                       // 设置非阻塞
    LOG_INFO("Client[%d] in!", users_[fd].GetFd());
}
```

**关键步骤**：
1. **初始化连接对象**：创建HttpConn实例
2. **设置定时器**：添加超时处理
3. **加入epoll监听**：监听读事件
4. **设置非阻塞**：配合ET模式

### 5.3 读事件处理 (DealRead_)

```cpp
void WebServer::DealRead_(HttpConn* client) {
    assert(client);
    ExtentTime_(client);                     // 延长超时时间
    threadpool_->AddTask(std::bind(&WebServer::OnRead_, this, client));
}
```

**异步处理设计**：
- **延长超时**：有活动的连接延长生存时间
- **提交任务**：将具体处理交给线程池

### 5.4 写事件处理 (DealWrite_)

```cpp
void WebServer::DealWrite_(HttpConn* client) {
    assert(client);
    ExtentTime_(client);                     // 延长超时时间
    threadpool_->AddTask(std::bind(&WebServer::OnWrite_, this, client));
}
```

---

## 🧵 6. 线程池任务执行分析

### 6.1 读任务处理 (OnRead_)

```cpp
void WebServer::OnRead_(HttpConn* client) {
    assert(client);
    int ret = -1;
    int readErrno = 0;
    ret = client->read(&readErrno);          // 读取数据
    if(ret <= 0 && readErrno != EAGAIN) {
        CloseConn_(client);                  // 读取错误，关闭连接
        return;
    }
    OnProcess(client);                       // 处理HTTP请求
}
```

### 6.2 写任务处理 (OnWrite_)

```cpp
void WebServer::OnWrite_(HttpConn* client) {
    assert(client);
    int ret = -1;
    int writeErrno = 0;
    ret = client->write(&writeErrno);        // 发送数据
    if(client->ToWriteBytes() == 0) {
        /* 传输完成 */
        if(client->IsKeepAlive()) {
            OnProcess(client);               // 保持连接，处理下一个请求
            return;
        }
    }
    else if(ret < 0) {
        if(writeErrno == EAGAIN) {
            /* 继续传输 */
            epoller_->ModFd(client->GetFd(), connEvent_ | EPOLLOUT);
            return;
        }
    }
    CloseConn_(client);                      // 完成或错误，关闭连接
}
```

### 6.3 HTTP处理 (OnProcess)

```cpp
void WebServer::OnProcess(HttpConn* client) {
    if(client->process()) {                  // 处理HTTP请求
        epoller_->ModFd(client->GetFd(), connEvent_ | EPOLLOUT);
    } else {
        epoller_->ModFd(client->GetFd(), connEvent_ | EPOLLIN);
    }
}
```

**状态转换**：
- **process()成功**：切换到写模式，准备发送响应
- **process()失败**：继续读模式，等待更多数据

---

## 🎯 7. 执行流程总结

### 7.1 完整的请求处理流程

```
1. 程序启动
   ├── main() 创建WebServer实例
   ├── 构造函数初始化各个组件
   ├── InitSocket_() 创建监听socket
   └── server.Start() 进入事件循环

2. 客户端连接
   ├── epoll检测到listenFd的EPOLLIN事件
   ├── DealListen_() 接受新连接
   ├── AddClient_() 创建HttpConn对象
   └── 添加到epoll监听读事件

3. HTTP请求到达
   ├── epoll检测到连接fd的EPOLLIN事件
   ├── DealRead_() 将任务提交给线程池
   ├── OnRead_() 读取数据到缓冲区
   └── OnProcess() 解析HTTP请求

4. 生成HTTP响应
   ├── HttpConn::process() 解析请求
   ├── 生成响应数据
   ├── 修改epoll监听写事件
   └── 等待EPOLLOUT事件

5. 发送响应数据
   ├── epoll检测到EPOLLOUT事件
   ├── DealWrite_() 将任务提交给线程池
   ├── OnWrite_() 发送数据
   └── 根据Connection头决定是否保持连接

6. 连接管理
   ├── 定时器检查超时连接
   ├── 自动清理无活动连接
   └── 释放相关资源
```

### 7.2 关键数据流

```
客户端数据 → Socket缓冲区 → HttpConn::readBuff_ → HTTP解析 → 业务逻辑
                                                              ↓
响应数据 ← Socket缓冲区 ← HttpConn::writeBuff_ ← HTTP响应生成 ←┘
```

---

## 🎯 8. 学习要点总结

主人，通过这第二课的学习，您需要重点掌握：

### 8.1 **程序启动流程**
- ✅ main函数参数配置的含义
- ✅ WebServer构造函数的初始化顺序
- ✅ Socket创建和配置的每个步骤

### 8.2 **事件循环机制**
- ✅ Epoll事件等待和处理流程
- ✅ 不同事件类型的处理方式
- ✅ 定时器与事件循环的结合

### 8.3 **异步处理模型**
- ✅ 主线程负责事件分发
- ✅ 工作线程处理具体业务
- ✅ 事件驱动的状态转换

---

## 📝 课后思考题

1. **为什么使用边缘触发模式需要循环读写？**
2. **如果不设置SO_REUSEADDR会有什么问题？**
3. **定时器的超时时间是如何影响系统性能的？**
4. **EPOLLONESHOT的作用是什么？**

---

## 🔜 下一课预告

下一课我们将深入学习 **基础设施模块**，包括Buffer缓冲区、阻塞队列等核心组件的实现原理。

主人，第二课到此结束！您对程序执行流程还有什么疑问吗？ 🚀 