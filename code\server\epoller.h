/*
 * <AUTHOR> mark
 * @Date         : 2020-06-15
 * @copyleft Apache 2.0
 * 
 * Epoller epoll事件管理器
 * 功能：封装Linux epoll系统调用，提供高效的IO多路复用
 * 核心特性：
 * 1. 支持大量并发连接（默认1024个事件）
 * 2. 边缘触发和水平触发模式
 * 3. 高效的事件通知机制
 * 4. 简洁的API接口设计
 * 5. RAII资源管理
 */ 

#ifndef EPOLLER_H
#define EPOLLER_H

// 系统头文件
#include <sys/epoll.h> // epoll相关系统调用：epoll_create, epoll_ctl, epoll_wait
#include <fcntl.h>     // 文件控制函数：fcntl()
#include <unistd.h>    // Unix标准函数：close()
#include <assert.h>    // 断言宏，用于调试
#include <vector>      // STL动态数组
#include <errno.h>     // 错误码定义

/**
 * @class Epoller
 * @brief epoll事件管理器封装类
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                    Epoller                              │
 * │                                                         │
 * │  ┌─────────────┐              ┌─────────────┐          │
 * │  │   应用层     │              │   内核层     │          │
 * │  │             │              │             │          │
 * │  │ ┌─────────┐ │              │ ┌─────────┐ │          │
 * │  │ │ AddFd() │ │─────────────►│ │ epoll   │ │          │
 * │  │ │ ModFd() │ │              │ │ 红黑树   │ │          │
 * │  │ │ DelFd() │ │              │ │         │ │          │
 * │  │ └─────────┘ │              │ └─────────┘ │          │
 * │  │ ┌─────────┐ │              │ ┌─────────┐ │          │
 * │  │ │ Wait()  │ │◄─────────────│ │ 就绪    │ │          │
 * │  │ │         │ │              │ │ 队列    │ │          │
 * │  │ └─────────┘ │              │ │         │ │          │
 * │  └─────────────┘              │ └─────────┘ │          │
 * │  ┌─────────────┐              └─────────────┘          │
 * │  │ events_[]   │                                       │
 * │  │ 事件数组     │                                       │
 * │  │             │                                       │
 * │  └─────────────┘                                       │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 工作原理：
 * 1. 创建epoll实例，获得epoll文件描述符
 * 2. 通过AddFd()将感兴趣的文件描述符添加到epoll监听
 * 3. 调用Wait()等待事件发生，内核返回就绪事件
 * 4. 通过GetEventFd()和GetEvents()获取具体的事件信息
 * 5. 根据需要调用ModFd()修改或DelFd()删除监听
 */
class Epoller {
public:
    /**
     * @brief 构造函数
     * @param maxEvent 最大事件数量，默认1024
     * 
     * 功能：
     * 1. 创建epoll实例
     * 2. 预分配事件数组空间
     * 3. 初始化相关资源
     */
    explicit Epoller(int maxEvent = 1024);

    /**
     * @brief 析构函数
     * 
     * RAII资源管理：
     * 1. 关闭epoll文件描述符
     * 2. 释放相关资源
     * 3. vector自动释放内存
     */
    ~Epoller();

    // ==================== 文件描述符管理接口 ====================

    /**
     * @brief 添加文件描述符到epoll监听
     * @param fd 要监听的文件描述符
     * @param events 监听的事件类型（EPOLLIN、EPOLLOUT等）
     * @return bool 操作是否成功
     * 
     * 常用事件类型：
     * - EPOLLIN: 可读事件
     * - EPOLLOUT: 可写事件
     * - EPOLLET: 边缘触发模式
     * - EPOLLONESHOT: 一次性事件
     * - EPOLLRDHUP: 对端关闭连接
     */
    bool AddFd(int fd, uint32_t events);

    /**
     * @brief 修改文件描述符的监听事件
     * @param fd 文件描述符
     * @param events 新的事件类型
     * @return bool 操作是否成功
     * 
     * 应用场景：
     * 1. 从监听读事件切换到写事件
     * 2. 添加或移除特定的事件标志
     * 3. 动态调整监听策略
     */
    bool ModFd(int fd, uint32_t events);

    /**
     * @brief 从epoll中删除文件描述符
     * @param fd 要删除的文件描述符
     * @return bool 操作是否成功
     * 
     * 注意：删除后该文件描述符的所有事件都不再被监听
     */
    bool DelFd(int fd);

    // ==================== 事件等待和查询接口 ====================

    /**
     * @brief 等待事件发生
     * @param timeoutMs 超时时间（毫秒），-1表示无限等待
     * @return int 就绪事件的数量，-1表示错误
     * 
     * 核心功能：
     * 1. 调用epoll_wait系统调用等待事件
     * 2. 将就绪事件填充到events_数组中
     * 3. 返回就绪事件的数量
     * 
     * 超时处理：
     * - timeoutMs > 0: 等待指定毫秒数
     * - timeoutMs = 0: 立即返回，不阻塞
     * - timeoutMs = -1: 无限等待直到有事件
     */
    int Wait(int timeoutMs = -1);

    /**
     * @brief 获取指定索引的事件文件描述符
     * @param i 事件索引（0到Wait()返回值-1）
     * @return int 文件描述符
     * 
     * 用法：在Wait()返回后，遍历所有就绪事件
     */
    int GetEventFd(size_t i) const;

    /**
     * @brief 获取指定索引的事件类型
     * @param i 事件索引
     * @return uint32_t 事件类型标志位
     * 
     * 返回值可能包含：
     * - EPOLLIN: 有数据可读
     * - EPOLLOUT: 可以写数据
     * - EPOLLERR: 发生错误
     * - EPOLLHUP: 连接挂起
     * - EPOLLRDHUP: 对端关闭写端
     */
    uint32_t GetEvents(size_t i) const;
        
private:
    // ==================== 成员变量 ====================

    /**
     * @brief epoll文件描述符
     * 
     * 通过epoll_create()创建，用于所有epoll操作
     * 是epoll实例在内核中的标识
     */
    int epollFd_;

    /**
     * @brief 事件数组
     * 
     * 用于存储epoll_wait()返回的就绪事件
     * 大小在构造时确定，避免运行时分配
     */
    std::vector<struct epoll_event> events_;    
};

#endif //EPOLLER_H