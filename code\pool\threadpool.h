/*
 * <AUTHOR> mark
 * @Date         : 2020-06-15
 * @copyleft Apache 2.0
 * 
 * ThreadPool 现代C++线程池实现
 * 设计模式：生产者-消费者模式
 * 核心特性：
 * 1. 基于C++11的现代线程池设计
 * 2. 使用条件变量实现高效的任务调度
 * 3. 支持任意可调用对象作为任务
 * 4. RAII资源管理，自动清理线程
 * 5. 移动语义优化性能
 */ 

#ifndef THREADPOOL_H
#define THREADPOOL_H

// C++11并发编程相关头文件
#include <mutex>              // 互斥锁，保护共享资源
#include <condition_variable> // 条件变量，实现线程间同步
#include <queue>              // STL队列，存储待执行任务
#include <thread>             // C++11线程库
#include <functional>         // 函数对象包装器

/**
 * @class ThreadPool
 * @brief 高性能线程池实现
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                    ThreadPool                           │
 * │                                                         │
 * │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │
 * │  │   主线程     │    │   任务队列   │    │  工作线程池  │ │
 * │  │             │    │             │    │             │ │
 * │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │ │
 * │  │ │AddTask()│ │───►│ │ Task    │ │◄───│ │Thread 1 │ │ │
 * │  │ │         │ │    │ │ Queue   │ │    │ │         │ │ │
 * │  │ └─────────┘ │    │ │         │ │    │ └─────────┘ │ │
 * │  └─────────────┘    │ └─────────┘ │    │ ┌─────────┐ │ │
 * │                     │             │    │ │Thread 2 │ │ │
 * │  ┌─────────────┐    │ ┌─────────┐ │    │ │         │ │ │
 * │  │   条件变量   │    │ │ Mutex   │ │    │ │         │ │ │
 * │  │             │    │ │ Lock    │ │    │     ...     │ │
 * │  │ ┌─────────┐ │    │ │         │ │    │ ┌─────────┐ │ │
 * │  │ │notify() │ │───►│ └─────────┘ │    │ │Thread N │ │ │
 * │  │ │wait()   │ │    │             │    │ │         │ │ │
 * │  │ └─────────┘ │    └─────────────┘    │ └─────────┘ │ │
 * │  └─────────────┘                       └─────────────┘ │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 工作原理：
 * 1. 构造时创建指定数量的工作线程
 * 2. 工作线程循环等待任务队列中的任务
 * 3. 主线程通过AddTask()添加任务到队列
 * 4. 条件变量通知工作线程有新任务
 * 5. 工作线程取出任务并执行
 * 6. 析构时通知所有线程退出并等待清理
 */
class ThreadPool {
public:
    /**
     * @brief 线程池构造函数
     * @param threadCount 线程数量，默认8个线程
     * 
     * 核心实现技术：
     * 1. 使用shared_ptr管理Pool结构体，确保线程安全的生命周期管理
     * 2. lambda表达式捕获shared_ptr，避免悬空指针问题
     * 3. detach()分离线程，让线程在后台独立运行
     * 4. 条件变量实现高效的线程同步
     */
    /**
     * @brief 线程池构造函数 - 从基础概念到高级应用的完整解析
     * @param threadCount 线程数量，默认8个线程
     * 
     * 🎯 主人，让我从最基础的概念开始，逐步为您解析这个构造函数的每一个细节！
     * 
     * ═══════════════════════════════════════════════════════════════
     * 📚 基础概念回顾：什么是线程池？
     * ═══════════════════════════════════════════════════════════════
     * 
     * 想象一个餐厅：
     * - 传统方式：每来一个客人就临时雇佣一个厨师，客人走了厨师就解雇
     * - 线程池方式：餐厅提前雇佣8个厨师，客人的订单放到订单队列，厨师们轮流处理
     * 
     * 线程池的核心优势：
     * 1. 避免频繁创建/销毁线程的开销（就像避免频繁雇佣/解雇厨师）
     * 2. 控制并发数量，防止系统资源耗尽
     * 3. 任务队列缓冲，平滑处理突发请求
     * 
     * ═══════════════════════════════════════════════════════════════
     * 🔧 C++基础知识详解
     * ═══════════════════════════════════════════════════════════════
     */
    explicit ThreadPool(size_t threadCount = 8): pool_(std::make_shared<Pool>()) {
        /*
         * 🔍 第一行代码深度解析：
         * 
         * 1. explicit 关键字：
         *    - 基础概念：防止隐式类型转换
         *    - 为什么需要：ThreadPool pool = 8; // 这样的代码是危险的！
         *    - 正确用法：ThreadPool pool(8);   // 必须显式调用构造函数
         * 
         * 2. size_t 类型：
         *    - 基础概念：无符号整数类型，专门用于表示大小和计数
         *    - 为什么不用int：int可能是负数，线程数量不能为负
         *    - 实际应用：size_t在64位系统上是64位，能表示更大的数值
         * 
         * 3. 默认参数 = 8：
         *    - 基础概念：如果调用时不提供参数，就使用默认值
         *    - 为什么是8：通常CPU核心数的1-2倍是较好的选择
         *    - 使用方式：ThreadPool pool;     // 使用默认8个线程
         *               ThreadPool pool(16); // 使用16个线程
         * 
         * 4. 初始化列表 : pool_(std::make_shared<Pool>())：
         *    - 基础概念：在构造函数体执行前初始化成员变量
         *    - 为什么用初始化列表：比在构造函数体内赋值更高效
         *    - std::make_shared：创建智能指针的推荐方式
         */
        
        assert(threadCount > 0);  
        /*
         * 🔍 assert断言详解：
         * 
         * 基础概念：assert是一个调试工具，用于检查程序的假设条件
         * - 如果条件为真：程序继续执行
         * - 如果条件为假：程序立即终止，并显示错误信息
         * 
         * 为什么在这里使用：
         * - 线程数量为0没有意义，会导致线程池无法工作
         * - 在开发阶段帮助发现错误的参数传递
         * 
         * 注意：Release版本中assert会被编译器移除，不影响性能
         */
        
        // 🏗️ 创建工作线程 - 这是整个线程池的核心！
        for(size_t i = 0; i < threadCount; i++) {
            /*
             * 🔍 for循环基础回顾：
             * 
             * for(初始化; 条件判断; 递增操作)
             * - size_t i = 0：循环计数器，从0开始
             * - i < threadCount：循环条件，创建threadCount个线程
             * - i++：每次循环后i递增1
             * 
             * 为什么用size_t而不是int：
             * - 与threadCount类型保持一致，避免类型转换警告
             * - size_t是无符号类型，不会出现负数比较的问题
             */
            
            std::thread([pool = pool_] {  
                /*
                 * 🔍 std::thread深度解析：
                 * 
                 * 基础概念：C++11引入的线程类，用于创建和管理线程
                 * 
                 * 构造方式：std::thread(可调用对象, 参数...)
                 * - 可调用对象可以是：函数指针、函数对象、lambda表达式
                 * - 这里使用lambda表达式（匿名函数）
                 * 
                 * 🔍 Lambda表达式详解：[捕获列表](参数列表){函数体}
                 * 
                 * [pool = pool_]：捕获列表
                 * - 基础概念：lambda可以"捕获"外部变量，在函数体内使用
                 * - pool = pool_：按值捕获，创建pool_的副本
                 * - 为什么这样做：延长shared_ptr的生命周期！
                 * 
                 * 🎯 生命周期问题详解：
                 * 假设我们写成 [&pool_]（按引用捕获）：
                 * 1. 构造函数执行完毕，局部变量可能被销毁
                 * 2. 工作线程还在运行，但引用的对象已经不存在
                 * 3. 程序崩溃！
                 * 
                 * 使用 [pool = pool_]（按值捕获shared_ptr）：
                 * 1. 每个lambda都持有一个shared_ptr副本
                 * 2. 只要有线程在运行，Pool对象就不会被销毁
                 * 3. 线程安全！
                 */
                
                std::unique_lock<std::mutex> locker(pool->mtx);  
                /*
                 * 🔍 互斥锁和RAII详解：
                 * 
                 * 基础概念：多线程编程中的同步原语
                 * - mutex（互斥锁）：确保同一时间只有一个线程访问共享资源
                 * - 就像厕所门锁：一次只能一个人使用
                 * 
                 * std::unique_lock vs std::lock_guard：
                 * - lock_guard：简单的RAII锁，构造时加锁，析构时解锁
                 * - unique_lock：更灵活，支持手动unlock()和lock()
                 * 
                 * 为什么这里用unique_lock：
                 * - 后面需要手动unlock()释放锁执行任务
                 * - 然后重新lock()继续等待下一个任务
                 * 
                 * RAII原理：
                 * - Resource Acquisition Is Initialization
                 * - 资源获取即初始化：构造时获取资源，析构时释放资源
                 * - 即使发生异常，析构函数也会被调用，确保资源释放
                 */
                
                while(true) {  
                    /*
                     * 🔍 工作线程主循环：
                     * 
                     * 为什么是无限循环：
                     * - 工作线程需要持续运行，等待和处理任务
                     * - 只有在线程池关闭时才退出循环
                     * 
                     * 循环的三种状态：
                     * 1. 有任务：取出任务执行
                     * 2. 无任务但线程池未关闭：等待新任务
                     * 3. 线程池已关闭：退出循环，线程结束
                     */
                    
                    if(!pool->tasks.empty()) {
                        /*
                         * 🔍 任务队列检查：
                         * 
                         * pool->tasks：任务队列（std::queue）
                         * - 基础概念：先进先出（FIFO）的数据结构
                         * - 就像排队买票：先来的先处理
                         * 
                         * empty()方法：
                         * - 返回bool值，true表示队列为空
                         * - !pool->tasks.empty()：队列不为空，有任务可处理
                         */
                        
                        // 有任务可执行
                        auto task = std::move(pool->tasks.front());  
                        /*
                         * 🔍 移动语义深度解析：
                         * 
                         * 基础概念：C++11引入的重要特性
                         * 
                         * 传统拷贝 vs 移动：
                         * - 拷贝：创建一个完全相同的副本（昂贵）
                         * - 移动：转移资源的所有权（便宜）
                         * 
                         * 生活比喻：
                         * - 拷贝：把整个房子重新建一遍
                         * - 移动：把房子的钥匙交给别人
                         * 
                         * std::move()作用：
                         * - 将左值转换为右值引用
                         * - 告诉编译器："这个对象我不再需要，可以移动它"
                         * 
                         * front()方法：
                         * - 返回队列第一个元素的引用
                         * - 不会移除元素，只是访问
                         * 
                         * auto关键字：
                         * - C++11自动类型推导
                         * - 编译器根据右侧表达式推导变量类型
                         * - 这里task的类型是std::function<void()>
                         */
                        
                        pool->tasks.pop();                          
                        /*
                         * 🔍 队列操作：
                         * 
                         * pop()方法：
                         * - 移除队列的第一个元素
                         * - 注意：pop()不返回元素，只是移除
                         * - 这就是为什么先用front()获取，再用pop()移除
                         */
                        
                        locker.unlock();                            
                        /*
                         * 🔍 为什么要手动解锁：
                         * 
                         * 关键思想：缩小锁的作用域，提高并发性能
                         * 
                         * 如果不解锁会怎样：
                         * 1. 当前线程执行任务时，其他线程无法访问任务队列
                         * 2. 其他线程无法添加新任务
                         * 3. 其他工作线程无法取出任务
                         * 4. 严重影响并发性能！
                         * 
                         * 解锁后的好处：
                         * 1. 当前线程执行任务，不占用锁
                         * 2. 其他线程可以正常操作任务队列
                         * 3. 真正实现并行处理
                         */
                        
                        task();                                     
                        /*
                         * 🔍 任务执行：
                         * 
                         * task是std::function<void()>类型
                         * - 可以存储任何无参数、无返回值的可调用对象
                         * - 函数指针、lambda表达式、函数对象都可以
                         * 
                         * task()：函数调用操作符
                         * - 执行存储在task中的函数
                         * - 这就是实际的业务逻辑执行点
                         */
                        
                        locker.lock();                              
                        /*
                         * 🔍 重新加锁：
                         * 
                         * 为什么要重新加锁：
                         * - 任务执行完毕，需要重新检查任务队列
                         * - 访问共享资源（pool->tasks, pool->isClosed）需要加锁
                         * - 保证线程安全
                         * 
                         * 锁的生命周期：
                         * 加锁 -> 检查队列 -> 取任务 -> 解锁 -> 执行任务 -> 加锁 -> 循环
                         */
                    } 
                    else if(pool->isClosed) {
                        /*
                         * 🔍 线程池关闭检查：
                         * 
                         * isClosed标志：
                         * - bool类型，表示线程池是否已关闭
                         * - 在析构函数中设置为true
                         * 
                         * 为什么要检查：
                         * - 当线程池析构时，需要通知所有工作线程退出
                         * - 避免线程无限等待，导致程序无法正常退出
                         */
                        
                        // 线程池已关闭，退出循环
                        break;
                        /*
                         * break语句：
                         * - 跳出while循环
                         * - 线程函数执行完毕，线程自然结束
                         */
                    }
                    else {
                        /*
                         * 🔍 等待新任务：
                         * 
                         * 执行到这里的条件：
                         * 1. 任务队列为空（没有任务）
                         * 2. 线程池未关闭（还需要继续工作）
                         * 
                         * 这时候应该怎么办：
                         * - 不能忙等待（while循环检查），会浪费CPU
                         * - 应该阻塞等待，直到有新任务或线程池关闭
                         */
                        
                        // 无任务且未关闭，等待新任务
                        pool->cond.wait(locker);  
                        /*
                         * 🔍 条件变量深度解析：
                         * 
                         * 基础概念：线程同步的高级工具
                         * - 允许线程等待某个条件成立
                         * - 避免忙等待，节省CPU资源
                         * 
                         * 生活比喻：
                         * - 就像等公交车：不是一直站在路边看，而是坐在候车亭里等通知
                         * 
                         * wait()方法的工作原理：
                         * 1. 自动释放传入的锁（locker）
                         * 2. 线程进入睡眠状态，等待通知
                         * 3. 收到通知后，重新获取锁
                         * 4. 继续执行后续代码
                         * 
                         * 为什么要传入锁：
                         * - 等待时必须释放锁，否则其他线程无法操作共享资源
                         * - 被唤醒时必须重新获取锁，保证线程安全
                         * 
                         * 谁会发送通知：
                         * - AddTask()方法添加任务后会调用notify_one()
                         * - 析构函数关闭线程池时会调用notify_all()
                         * 
                         * 虚假唤醒问题：
                         * - 有时候线程会无缘无故被唤醒
                         * - 这就是为什么用while循环而不是if判断
                         * - 被唤醒后重新检查条件，如果条件不满足继续等待
                         */
                    }
                }
                /*
                 * 🔍 线程函数结束：
                 * 
                 * 执行到这里说明：
                 * - while循环被break跳出
                 * - 线程池已关闭
                 * - 线程函数即将结束
                 * 
                 * 资源清理：
                 * - locker析构时自动释放锁
                 * - lambda捕获的shared_ptr析构时自动减少引用计数
                 * - 线程自然结束，系统回收线程资源
                 */
            }).detach();  
            /*
             * 🔍 线程分离详解：
             * 
             * detach()方法：
             * - 将线程与std::thread对象分离
             * - 线程在后台独立运行，不需要主线程等待
             * 
             * detach() vs join()：
             * - join()：主线程等待子线程结束，然后继续执行
             * - detach()：主线程不等待，子线程独立运行
             * 
             * 为什么选择detach()：
             * - 线程池的工作线程需要长期运行
             * - 不需要主线程等待它们结束
             * - 简化生命周期管理
             * 
             * 注意事项：
             * - detach()后无法再join()
             * - 必须确保线程访问的资源在线程结束前有效
             * - 这就是为什么使用shared_ptr管理Pool对象
             */
        }
        /*
         * 🔍 构造函数总结：
         * 
         * 整个构造过程：
         * 1. 创建shared_ptr管理的Pool对象
         * 2. 循环创建threadCount个工作线程
         * 3. 每个线程运行相同的工作循环
         * 4. 线程分离，在后台独立运行
         * 
         * 设计精髓：
         * 1. RAII资源管理：智能指针自动管理内存
         * 2. 线程安全：互斥锁保护共享资源
         * 3. 高效同步：条件变量避免忙等待
         * 4. 移动语义：减少不必要的拷贝
         * 5. 现代C++：lambda、auto、智能指针等特性
         * 
         * 主人，这个构造函数虽然只有几十行代码，但包含了现代C++的精华！
         * 从基础的循环、条件判断，到高级的多线程、智能指针、移动语义，
         * 每一个细节都体现了高性能编程的最佳实践！
         */
    }

    /**
     * @brief 默认构造函数
     * 创建一个空的线程池，需要后续初始化
     */
    ThreadPool() = default;

    /**
     * @brief 移动构造函数
     * 支持移动语义，提高性能
     */
    ThreadPool(ThreadPool&&) = default;
    
    /**
     * @brief 析构函数
     * 
     * RAII资源管理：
     * 1. 设置关闭标志
     * 2. 通知所有等待的线程
     * 3. shared_ptr自动管理内存
     * 4. 线程自然结束，无需显式join
     */
    ~ThreadPool() {
        if(static_cast<bool>(pool_)) {  // 检查pool_是否有效
            {
                std::lock_guard<std::mutex> locker(pool_->mtx);  // 加锁设置关闭标志
                pool_->isClosed = true;
            }
            pool_->cond.notify_all();  // 通知所有等待的线程
        }
    }

    /**
     * @brief 添加任务到线程池
     * @tparam F 可调用对象类型（函数、lambda、函数对象等）
     * @param task 要执行的任务
     * 
     * 主人，让我用最简单的话解释这个函数：
     * 
     * 【这个函数是干什么的？】
     * 就像往工厂的生产线上放一个新的工作任务！
     * 比如你有一个包子店，顾客点了包子，你就把"做包子"这个任务
     * 放到厨师们的任务清单里，然后喊一声"有新任务了！"
     * 
     * 【为什么要用template<class F>？】
     * 因为任务可以是各种各样的：
     * - 普通函数：void makeFood()
     * - lambda表达式：[](){cout << "hello";}
     * - 函数对象：MyTask()
     * template就像一个万能接口，什么类型的任务都能接受！
     * 
     * 【F&& 是什么意思？】
     * 这叫"万能引用"，既能接收左值，也能接收右值
     * 就像一个万能插座，什么插头都能插！
     * 
     * 【std::forward<F>(task) 完美转发是什么？】
     * 想象你是快递员，客户给你包裹时：
     * - 如果是轻的，你轻拿轻放
     * - 如果是重的，你用力搬运
     * 完美转发就是保持原来的"搬运方式"不变！
     * 
     * 【emplace 和 push 有什么区别？】
     * push：先在外面做好包子，再放到蒸笼里（多一次拷贝）
     * emplace：直接在蒸笼里做包子（效率更高）
     */
    template<class F>
    void AddTask(F&& task) {
        {
            // 【加锁保护】就像进银行金库要先锁门一样
            std::lock_guard<std::mutex> locker(pool_->mtx);  
            
            // 【添加任务】把任务直接放到任务队列里
            // std::forward保持task原来的类型特性
            // emplace直接在队列里构造任务对象，避免多余拷贝
            pool_->tasks.emplace(std::forward<F>(task));
            
            // 【自动解锁】出了这个大括号，锁自动释放
        }
        
        // 【通知工人】告诉一个正在等待的线程："有活干了！"
        // 就像工头喊："谁有空的，来干活！"
        pool_->cond.notify_one();  
    }

private:
    /**
     * @struct Pool
     * @brief 线程池内部数据结构
     * 
     * 设计要点：
     * 1. 将所有共享数据封装在一个结构体中
     * 2. 使用shared_ptr管理，确保线程安全的生命周期
     * 3. 互斥锁保护共享资源
     * 4. 条件变量实现高效同步
     */
    struct Pool {
        std::mutex mtx;                           // 互斥锁，保护共享资源
        std::condition_variable cond;             // 条件变量，线程同步
        bool isClosed;                            // 关闭标志
        std::queue<std::function<void()>> tasks;  // 任务队列，存储待执行的任务
    };
    
    /**
     * @brief 线程池核心数据
     * 使用shared_ptr确保多线程环境下的安全访问
     * 
     * 为什么使用shared_ptr：
     * 1. 多个线程需要访问同一个Pool对象
     * 2. 线程的生命周期可能超过ThreadPool对象
     * 3. shared_ptr提供线程安全的引用计数
     * 4. 避免悬空指针和内存泄漏
     */
    std::shared_ptr<Pool> pool_;
};

#endif //THREADPOOL_H