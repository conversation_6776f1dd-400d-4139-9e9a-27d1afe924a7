/*
 * <AUTHOR> mark
 * @Date         : 2020-06-26
 * @copyleft Apache 2.0
 * 
 * Log 异步日志系统实现
 * 
 * What (是什么)：
 * 高性能异步日志系统，支持多级别日志、文件轮转、线程安全、缓冲优化
 * 
 * Why (为什么)：
 * 1. 异步写入避免阻塞主业务线程
 * 2. 多级别日志便于问题诊断和性能监控
 * 3. 文件轮转管理磁盘空间使用
 * 4. 线程安全支持多线程并发写入
 * 5. 缓冲机制提高IO效率
 * 
 * How (怎么做)：
 * 1. 单例模式确保全局唯一日志实例
 * 2. 生产者消费者模式实现异步写入
 * 3. 条件变量和互斥锁保证线程安全
 * 4. 阻塞队列缓冲日志消息
 * 5. 智能文件命名和大小管理
 */ 

#include "log.h"

using namespace std;

// ==================== 单例模式实现 ====================

/**
 * @brief 获取日志系统单例实例
 * @return Log* 日志系统的唯一实例
 * 
 * What: 线程安全的单例模式实现
 * Why:  确保整个应用程序只有一个日志系统实例，避免资源冲突
 * How:  使用函数内静态变量，C++11保证线程安全的初始化
 */
Log* Log::Instance() {
    static Log inst;        // 函数内静态变量，线程安全的懒汉式单例
    return &inst;
}

// ==================== 异步写入线程函数 ====================

/**
 * @brief 异步日志写入线程函数
 * 
 * What: 独立线程中执行的日志写入循环
 * Why:  将磁盘IO操作从主线程中分离，避免阻塞业务逻辑
 * How:  
 * 1. 无限循环从阻塞队列中取出日志消息
 * 2. 检查文件大小，必要时进行文件轮转
 * 3. 将日志写入文件并立即刷新
 */
void Log::FlushLogThread() {
    Log::Instance()->AsyncWrite_();     // 调用异步写入函数
}

// ==================== 生命周期管理函数 ====================

/**
 * @brief 初始化日志系统
 * @param level 日志级别 (0=DEBUG, 1=INFO, 2=WARN, 3=ERROR)
 * @param path 日志文件存放的文件夹路径
 * @param suffix 日志文件的后缀名（比如 .log）
 * @param maxQueueSize 异步队列最大容量（>=1表示异步模式，0表示同步模式）
 * 
 * 主人，让我用最简单的话给您解释这个初始化函数：
 * 
 * 这个函数就像是"开启日志系统的总开关"，它要做4件重要的事：
 * 1. 设置日志的基本配置（级别、路径等）
 * 2. 决定用同步还是异步模式写日志
 * 3. 创建今天的日志文件
 * 4. 准备好写日志的环境
 */
void Log::init(int level = 1, const char* path = "./log", 
                const char* suffix =".log",
                int maxQueueSize = 1024) {
    
    // ==================== 第一步：基本配置 ====================
    
    isOpen_ = true;                     // 把日志系统的"总开关"打开
    level_ = level;                     // 设置日志级别（比如只记录ERROR以上的日志）
    
    // ==================== 第二步：选择工作模式 ====================
    
    /*
     * 这里要决定用哪种模式写日志：
     * 
     * 同步模式：就像直接用笔写字，写完一行再写下一行
     * - 优点：简单，写完就能立即看到
     * - 缺点：如果写得慢，程序就要等着（可能卡顿）
     * 
     * 异步模式：就像有个助手帮你写，你只管说内容，助手负责写到纸上
     * - 优点：你不用等，说完就能做别的事
     * - 缺点：稍微复杂一点，需要一个"助手"（线程）
     */
    if(maxQueueSize >= 1) {
        // 选择异步模式（有助手帮忙写）
        isAsync_ = true;                
        
        if(!deque_) {
            // 创建一个"传话筒"（阻塞队列），用来传递要写的日志内容
            // 创建智能指针来管理阻塞队列
            // 
            // 主人，让我从最基础开始解释这两行代码：
            //
            // 第一行：unique_ptr<BlockDeque<std::string>> newDeque(new BlockDeque<std::string>);
            // 
            // 什么是 unique_ptr？
            // unique_ptr 是C++的"智能指针"，就像一个"自动管家"：
            // - 普通指针：就像你借了一本书，要记得还书，忘记还就丢了
            // - unique_ptr：就像有个管家帮你管书，到时间自动还书
            // - 好处：不用担心忘记释放内存，避免内存泄漏
            //
            // 什么是 BlockDeque<std::string>？
            // - BlockDeque：阻塞双端队列（Block + Deque）
            // - 就像一个"传话筒"，可以从两头放入和取出消息
            // - "阻塞"的意思：如果队列空了，取消息的人会等待；如果队列满了，放消息的人会等待
            // - <std::string>：表示这个队列里存放的是字符串类型的数据
            //
            // new BlockDeque<std::string> 的作用：
            // - 在内存中创建一个新的阻塞队列对象
            // - 就像买了一个新的"传话筒"
            //
            // 整个第一行的意思：
            // 创建一个名为 newDeque 的智能指针，让它管理一个新创建的阻塞队列
            
            unique_ptr<BlockDeque<std::string>> newDeque(new BlockDeque<std::string>);
            
            // 第二行：deque_ = move(newDeque);
            //
            // 什么是 move？
            // move 是C++11的"移动语义"，就像"转手"：
            // - 不是复制一份新的，而是直接把所有权转移过去
            // - 就像把房子的钥匙直接给别人，而不是复制一把钥匙
            // - 好处：效率高，避免不必要的复制
            //
            // deque_ 是什么？
            // - 这是Log类的成员变量，类型也是 unique_ptr<BlockDeque<std::string>>
            // - 它是Log类用来存储"传话筒"的地方
            //
            // 整个第二行的意思：
            // 把 newDeque 管理的阻塞队列的所有权转移给 deque_
            // 转移后，newDeque 就变成空的了，deque_ 成为了这个队列的新主人
            //
            // 为什么要这样做？
            // 1. 先创建一个临时的智能指针 newDeque
            // 2. 再转移给成员变量 deque_
            // 这样可以确保创建过程是安全的，如果创建失败，不会影响原来的 deque_
            
            deque_ = move(newDeque);
            
            // 雇佣一个"助手"（线程），专门负责写日志
            std::unique_ptr<std::thread> NewThread(new thread(FlushLogThread));
            writeThread_ = move(NewThread);
        }
        
        // 告诉"传话筒"最多能存多少条消息
        deque_->SetMaxCapacity(maxQueueSize);   
    } else {
        // 选择同步模式（自己直接写）
        isAsync_ = false;               
    }
    
    // ==================== 第三步：准备今天的日志文件 ====================
    
    lineCount_ = 0;                     // 重置行数计数器（从0开始数）
    
    // 获取当前时间，用来给日志文件起名字
    auto now = chrono::system_clock::now();
    auto t = chrono::system_clock::to_time_t(now);
    struct tm *sysTime = localtime(&t);
    struct tm t_ = *sysTime;
    
    path_ = path;                       // 记住日志文件要放在哪个文件夹
    suffix_ = suffix;                   // 记住日志文件的后缀名
    
    // 生成今天的日志文件名，格式：路径/2024_03_15.log
    char fileName[LOG_NAME_LEN] = {0};
    snprintf(fileName, LOG_NAME_LEN - 1, "%s/%04d_%02d_%02d%s", 
            path_, t_.tm_year + 1900, t_.tm_mon + 1, t_.tm_mday, suffix_);
    toDay_ = t_.tm_mday;                // 记住今天是几号（用于判断是否要换新文件）
    
    // ==================== 第四步：打开文件准备写入 ====================
    
    {
        // 用锁保护，确保多个线程不会同时操作文件（线程安全）
        lock_guard<mutex> locker(mtx_); 
        
        buff_.RetrieveAll();            // 清空缓冲区（就像清空草稿纸）
        
        // 如果之前有打开的文件，先关闭它
        if(fp_) { 
            flush();                    // 把缓存的内容全部写入文件
            fclose(fp_);                // 关闭文件
        }

        // 尝试打开今天的日志文件（追加模式，不会覆盖之前的内容）
        fp_ = fopen(fileName, "a");     
        if(fp_ == nullptr) {
            // 如果打开失败，可能是文件夹不存在，先创建文件夹
            mkdir(path_, 0777);
            fp_ = fopen(fileName, "a"); // 再次尝试打开
        } 
        assert(fp_ != nullptr);         // 确保文件一定要打开成功，否则程序报错
    }
}

// ==================== 日志写入核心函数 ====================

/**
 * @brief 写入日志消息的核心函数
 * @param level 日志级别
 * @param format 格式化字符串
 * @param vaList 可变参数列表
 * 
 * What: 统一的日志写入接口，处理格式化和输出逻辑
 * Why:  提供一致的日志格式和线程安全的写入机制
 * How:  
 * 1. 检查日志级别和开启状态
 * 2. 格式化时间戳和日志内容
 * 3. 检查文件轮转条件
 * 4. 根据模式选择同步或异步写入
 */
void Log::write(int level, const char *format, ...) {
    /*
     * 主人，让我从最基础的概念开始给您解释这个函数：
     * 
     * 1. 什么是函数签名？
     * void Log::write(int level, const char *format, ...)
     * - void：表示这个函数不返回任何值，就像一个"只做事不回话"的助手
     * - Log::：表示这是Log类的成员函数，:: 是作用域运算符
     * - write：函数名，表示"写入"的动作
     * - (int level, const char *format, ...)：参数列表
     *   * int level：日志级别，用数字表示（0=DEBUG, 1=INFO, 2=WARN, 3=ERROR）
     *   * const char *format：格式化字符串，就像printf的第一个参数
     *   * ...：这叫"可变参数"，表示可以接受任意数量的额外参数
     * 
     * 2. 什么是可变参数？
     * 就像printf函数一样：printf("我叫%s，今年%d岁", "小明", 18)
     * - "我叫%s，今年%d岁" 是format参数
     * - "小明" 和 18 就是可变参数，可以有0个、1个或更多个
     */
    
    // ==================== 第一步：获取当前时间 ====================
    
    /*
     * 为什么需要获取时间？
     * 因为每条日志都要记录"什么时候发生的"，就像日记要写日期一样
     * 
     * 这里用到了几个C语言的时间相关结构体和函数：
     */
    
    // 1. struct timeval：精确到微秒的时间结构体
    struct timeval now = {0, 0};        // 初始化为0，防止垃圾数据
    /*
     * timeval结构体包含两个成员：
     * - tv_sec：秒数（从1970年1月1日开始计算）
     * - tv_usec：微秒数（1秒=1,000,000微秒）
     * 这样我们就能得到非常精确的时间，比如：2024-03-15 14:30:25.123456
     */
    
    // 2. gettimeofday：获取当前系统时间
    gettimeofday(&now, nullptr);
    /*
     * 这个函数的作用：
     * - 把当前系统时间填充到now这个结构体里
     * - &now：取now的地址，因为函数需要修改now的内容
     * - nullptr：第二个参数我们不需要，所以传空指针
     */
    
    // 3. 提取秒数部分
    time_t tSec = now.tv_sec;
    /*
     * time_t是一个整数类型，表示从1970年1月1日到现在的秒数
     * 比如：1710484225 这个数字就代表某个具体的时间点
     */
    
    // 4. 转换为人类可读的时间格式
    struct tm *sysTime = localtime(&tSec);
    /*
     * localtime函数的作用：
     * - 把"秒数"转换成"年月日时分秒"的格式
     * - 返回一个指向tm结构体的指针
     * - tm结构体包含：tm_year(年)、tm_mon(月)、tm_mday(日)等
     */
    
    // 5. 复制时间结构体
    struct tm t = *sysTime;
    /*
     * 为什么要复制？
     * - localtime返回的是指针，指向一个静态变量
     * - 如果不复制，下次调用localtime时内容可能被覆盖
     * - *sysTime 表示"取指针指向的内容"，这叫解引用
     */
    
    // ==================== 第二步：准备处理可变参数 ====================
    
    va_list vaList;                     // 可变参数列表
    /*
     * va_list是什么？
     * - va 代表 "variable arguments"（可变参数）
     * - 这是一个特殊的数据类型，用来处理...这些参数
     * - 就像一个"参数容器"，里面装着所有传进来的额外参数
     * 
     * 使用可变参数的完整流程（后面会用到）：
     * 1. va_start(vaList, format)  // 开始处理，从format参数后面开始取
     * 2. va_arg(vaList, type)      // 取出一个指定类型的参数
     * 3. va_end(vaList)           // 结束处理，清理资源
     * 
     * 举个例子：
     * 如果调用 write(1, "用户%s登录，ID=%d", "张三", 123)
     * - level = 1
     * - format = "用户%s登录，ID=%d"  
     * - vaList 里就包含了 "张三" 和 123 这两个参数
     */
    
    // ==================== 文件轮转检查 ====================
    
    /* 
     * 日志分割条件：
     * 1. 日期发生变化（新的一天）
     * 2. 当前文件行数超过限制
     */
    /*
     * ==================== 日志文件轮转逻辑详解 ====================
     * 
     * 什么是日志文件轮转？
     * - 想象你有一个笔记本，写满了就换一个新的
     * - 日志轮转就是"换笔记本"的过程
     * - 当满足某些条件时，关闭当前日志文件，创建新的日志文件
     * 
     * 为什么需要轮转？
     * 1. 防止单个文件过大（几GB的文件很难打开查看）
     * 2. 按日期分类，方便查找特定时间的日志
     * 3. 便于日志文件的管理和清理
     */
    
    // 轮转条件检查：什么时候需要"换笔记本"？
    if (toDay_ != t.tm_mday || (lineCount_ && (lineCount_ % MAX_LINES == 0)))
    /*
     * 条件1：toDay_ != t.tm_mday
     * - toDay_：上次记录的日期（比如15号）
     * - t.tm_mday：当前的日期（比如16号）
     * - 如果不相等，说明日期变了，需要换文件
     * 
     * 条件2：(lineCount_ && (lineCount_ % MAX_LINES == 0))
     * - lineCount_：当前文件已写入的行数
     * - MAX_LINES：每个文件最多允许多少行
     * - lineCount_ % MAX_LINES == 0：行数刚好是最大行数的整数倍
     * - lineCount_ &&：确保行数不为0（避免刚开始就轮转）
     * 
     * ||：只要其中一个条件满足就轮转
     * 
     * 举例说明：
     * - 假设MAX_LINES = 1000
     * - 当写到第1000行时：1000 % 1000 = 0，触发轮转
     * - 当写到第2000行时：2000 % 1000 = 0，再次触发轮转
     */
    {
        // ==================== 第一步：准备轮转环境 ====================
        
        unique_lock<mutex> locker(mtx_);
        /*
         * 为什么要先加锁？
         * - 因为要修改共享资源（文件指针、计数器等）
         * - 防止其他线程同时写入导致数据混乱
         */
        
        locker.unlock();                // 临时释放锁
        /*
         * 为什么要立即释放锁？
         * - 接下来要做文件操作（关闭、打开文件）
         * - 文件操作比较耗时，长时间持锁会阻塞其他线程
         * - 先释放锁，让其他线程可以继续工作
         * - 等文件操作完成后再重新加锁
         */
        
        // ==================== 第二步：准备新文件名 ====================
        
        char newFile[LOG_NAME_LEN];     // 存储新文件的完整路径
        char tail[36] = {0};            // 存储文件名的时间部分
        /*
         * 为什么需要这两个字符数组？
         * - newFile：最终的完整文件路径，比如 "/logs/2024_03_15.log"
         * - tail：只是时间部分，比如 "2024_03_15"
         * - 先生成时间部分，再组装成完整路径
         */
        
        // 生成新文件名的时间部分：YYYY_MM_DD格式
        snprintf(tail, 36, "%04d_%02d_%02d", t.tm_year + 1900, t.tm_mon + 1, t.tm_mday);
        /*
         * snprintf函数详解：
         * - 功能：格式化字符串并存储到缓冲区
         * - 参数1 tail：目标缓冲区
         * - 参数2 36：缓冲区最大长度（防止溢出）
         * - 参数3 格式串：%04d表示4位数字，不足补0
         * - 后面的参数：要格式化的数据
         * 
         * 时间字段解释：
         * - t.tm_year：从1900年开始的年数，所以+1900得到真实年份
         * - t.tm_mon：月份，范围0-11，所以+1得到真实月份
         * - t.tm_mday：日期，范围1-31，直接使用
         * 
         * 结果示例：tail = "2024_03_15"
         */
        
        // ==================== 第三步：根据轮转原因生成文件名 ====================
        
        if (toDay_ != t.tm_mday)
        {
            // 情况1：日期变化导致的轮转
            snprintf(newFile, LOG_NAME_LEN - 72, "%s/%s%s", path_, tail, suffix_);
            /*
             * 文件名格式：路径/时间.后缀
             * - path_：日志文件目录，比如 "/var/log"
             * - tail：时间部分，比如 "2024_03_15"  
             * - suffix_：文件后缀，比如 ".log"
             * - 结果："/var/log/2024_03_15.log"
             * 
             * 为什么是 LOG_NAME_LEN - 72？
             * - 为路径和后缀预留足够空间，防止缓冲区溢出
             */
            
            toDay_ = t.tm_mday;         // 更新记录的日期
            lineCount_ = 0;             // 重置行计数（新文件从0开始）
        }
        else {
            // 情况2：同一天内文件行数过多导致的轮转
            snprintf(newFile, LOG_NAME_LEN - 72, "%s/%s-%d%s", path_, tail, (lineCount_ / MAX_LINES), suffix_);
            /*
             * 文件名格式：路径/时间-编号.后缀
             * - 比如第一个文件："/var/log/2024_03_15.log"
             * - 当行数超限时："/var/log/2024_03_15-1.log"
             * - 再次超限时："/var/log/2024_03_15-2.log"
             * 
             * (lineCount_ / MAX_LINES) 计算编号：
             * - 假设MAX_LINES = 1000
             * - 第1000行时：1000/1000 = 1，文件编号为1
             * - 第2000行时：2000/1000 = 2，文件编号为2
             * 
             * 注意：这里不重置lineCount_，因为还是同一天
             */
        }
        
        // ==================== 第四步：执行文件切换 ====================
        
        locker.lock();                  // 重新获取锁
        /*
         * 为什么要重新加锁？
         * - 现在要操作共享资源了（文件指针fp_）
         * - 确保在切换文件期间，其他线程不会同时操作
         */
        
        flush();                        // 刷新当前文件缓冲区
        /*
         * flush()的作用：
         * - 将缓冲区中未写入的数据强制写入磁盘
         * - 确保关闭文件前所有数据都被保存
         * - 类似于按Ctrl+S保存文件
         */
        
        fclose(fp_);                    // 关闭当前日志文件
        /*
         * fclose()：
         * - 关闭文件指针，释放系统资源
         * - 类似于"合上笔记本"
         */
        
        fp_ = fopen(newFile, "a");      // 打开新的日志文件
        /*
         * fopen()参数说明：
         * - newFile：要打开的文件路径
         * - "a"：追加模式（append）
         *   - 如果文件存在，从文件末尾开始写入
         *   - 如果文件不存在，创建新文件
         *   - 类似于"翻到笔记本的最后一页继续写"
         */
        
        assert(fp_ != nullptr);         // 确保文件打开成功
        /*
         * assert()断言：
         * - 如果fp_为空（文件打开失败），程序会立即终止
         * - 这是一种调试手段，确保关键操作成功
         * - 在生产环境中，通常会用更温和的错误处理方式
         */
    }
    /*
     * 整个轮转流程总结：
     * 1. 检查是否需要轮转（日期变化或行数超限）
     * 2. 暂时释放锁，避免长时间阻塞
     * 3. 根据轮转原因生成合适的新文件名
     * 4. 重新加锁，安全地切换文件
     * 5. 刷新并关闭旧文件，打开新文件
     * 
     * 这就像换笔记本的过程：
     * - 先判断当前笔记本是否需要换（写满了或新的一天）
     * - 准备新笔记本的标签（文件名）
     * - 把当前笔记本收好（关闭旧文件）
     * - 拿出新笔记本开始写（打开新文件）
     */

    // ==================== 日志格式化和写入详细解析 ====================
    
    /*
     * 主人，这段代码是整个日志写入的核心部分！让我一步一步详细解释：
     * 
     * 整体功能：把一条日志消息完整地组装起来并写入
     * 就像写一封完整的信件：先写日期，再写称呼，最后写正文
     */
    
    {
        /*
         * ==================== 第一步：线程安全保护 ====================
         */
        unique_lock<mutex> locker(mtx_);
        /*
         * unique_lock是什么？
         * - 这是一个"智能锁"，比普通的lock_guard更灵活
         * - 当这行代码执行时，自动给mtx_加锁
         * - 当离开这个{}块时，自动释放锁
         * - 就像进入房间时自动锁门，出去时自动开锁
         * 
         * 为什么用unique_lock而不是lock_guard？
         * - unique_lock可以手动unlock()和lock()
         * - lock_guard只能在析构时自动释放
         * - 这里可能需要中途释放锁，所以用更灵活的unique_lock
         */
        
        /*
         * ==================== 第二步：行数计数 ====================
         */
        lineCount_++;                   // 增加行计数
        /*
         * lineCount_的作用：
         * - 记录当前日志文件已经写了多少行
         * - 当达到MAX_LINES时，触发文件轮转
         * - ++是前置自增，先加1再使用
         * 
         * 为什么要计数行数？
         * - 防止单个文件过大（比如限制每个文件1万行）
         * - 便于文件管理和查找
         * - 提高文件读取性能
         */
        
        /*
         * ==================== 第三步：格式化时间戳 ====================
         */
        // 格式化时间戳：年-月-日 时:分:秒.微秒
        int n = snprintf(buff_.BeginWrite(), 128, "%d-%02d-%02d %02d:%02d:%02d.%06ld ",
                    t.tm_year + 1900, t.tm_mon + 1, t.tm_mday,
                    t.tm_hour, t.tm_min, t.tm_sec, now.tv_usec);
        /*
         * snprintf函数详解：
         * - snprintf：安全版本的sprintf，防止缓冲区溢出
         * - buff_.BeginWrite()：获取缓冲区中可写入的起始位置
         * - 128：最多写入128个字符，防止越界
         * - "%d-%02d-%02d %02d:%02d:%02d.%06ld "：格式化字符串
         * 
         * 格式化字符串详解：
         * - %d：普通整数（年份）
         * - %02d：2位整数，不足2位前面补0（月、日、时、分、秒）
         * - %06ld：6位长整数，不足6位前面补0（微秒）
         * - 空格：在时间戳后面加一个空格，分隔时间和日志内容
         * 
         * 时间字段说明：
         * - t.tm_year + 1900：tm_year是从1900年开始计算的，要加1900
         * - t.tm_mon + 1：tm_mon是0-11，要加1变成1-12月
         * - now.tv_usec：微秒数，提供精确的时间
         * 
         * 最终效果：2024-03-15 14:30:25.123456 
         */
        
        /*
         * ==================== 第四步：更新缓冲区写位置 ====================
         */
        buff_.HasWritten(n);            // 更新缓冲区写位置
        /*
         * HasWritten(n)的作用：
         * - 告诉缓冲区"我已经写入了n个字符"
         * - 缓冲区内部会更新写指针的位置
         * - 为下一次写入做准备
         * 
         * 为什么需要告诉缓冲区？
         * - 缓冲区不知道snprintf写了多少字符
         * - 必须手动更新，否则下次写入会覆盖之前的内容
         * - 类似于告诉记事本"我写到第几行了"
         */
        
        /*
         * ==================== 第五步：添加日志级别标识 ====================
         */
        // 添加日志级别标识
        AppendLogLevelTitle_(level);
        /*
         * AppendLogLevelTitle_函数的作用：
         * - 根据level参数添加对应的级别标识
         * - level=0 -> "[debug]: "
         * - level=1 -> "[info] : "
         * - level=2 -> "[warn] : "
         * - level=3 -> "[error]: "
         * 
         * 为什么需要级别标识？
         * - 便于快速识别日志的重要程度
         * - 便于使用工具过滤特定级别的日志
         * - 提高问题排查效率
         */
        
        /*
         * ==================== 第六步：格式化用户日志内容 ====================
         */
        // 格式化用户提供的日志内容
        va_start(vaList, format);
        /*
         * va_start(vaList, format)：
         * - 初始化可变参数列表
         * - vaList：用来遍历可变参数的"指针"
         * - format：最后一个固定参数，告诉va_start从哪里开始
         * 
         * 举例说明：
         * 如果调用：write(1, "用户%s登录，ID=%d", "张三", 123)
         * - format = "用户%s登录，ID=%d"
         * - vaList会指向"张三"和123这两个参数
         */
        
        int m = vsnprintf(buff_.BeginWrite(), buff_.WritableBytes(), format, vaList);
        /*
         * vsnprintf函数详解：
         * - v：表示接受va_list参数的版本
         * - buff_.BeginWrite()：当前缓冲区可写位置
         * - buff_.WritableBytes()：缓冲区剩余可写字节数
         * - format：格式化字符串（如"用户%s登录，ID=%d"）
         * - vaList：可变参数列表
         * 
         * 返回值m：实际写入的字符数
         * 
         * 这一步的作用：
         * - 将用户提供的格式化字符串和参数组合成最终的日志内容
         * - 类似于printf，但结果写入缓冲区而不是屏幕
         */
        
        va_end(vaList);
        /*
         * va_end(vaList)：
         * - 清理可变参数列表，释放相关资源
         * - 这是va_start的配对函数，必须成对使用
         * - 类似于malloc和free的关系
         */
        
        /*
         * ==================== 第七步：更新缓冲区并添加结束符 ====================
         */
        buff_.HasWritten(m);            // 更新缓冲区写位置
        /*
         * 再次更新缓冲区位置，告诉它我们又写入了m个字符
         */
        
        buff_.Append("\n\0", 2);        // 添加换行符和字符串结束符
        /*
         * Append("\n\0", 2)详解：
         * - "\n"：换行符，让每条日志占一行
         * - "\0"：字符串结束符（null terminator）
         * - 2：表示添加2个字符
         * 
         * 为什么需要这两个字符？
         * - \n：确保每条日志独占一行，便于阅读
         * - \0：C字符串的标准结束标记，确保字符串正确结束
         */

        /*
         * ==================== 第八步：输出模式选择 ====================
         */
        
        if(isAsync_ && deque_ && !deque_->full()) {
            /*
             * 异步模式条件检查：
             * - isAsync_：是否开启异步模式
             * - deque_：异步队列是否存在（不为空指针）
             * - !deque_->full()：队列是否还有空间（没满）
             * 
             * 为什么要检查这三个条件？
             * - isAsync_：确保用户确实想要异步写入
             * - deque_：确保队列对象存在，避免空指针访问
             * - !full()：如果队列满了，降级为同步写入，避免丢失日志
             */
            
            // 异步模式：将日志消息推入队列
            deque_->push_back(buff_.RetrieveAllToStr());
            /*
             * 异步模式处理：
             * - buff_.RetrieveAllToStr()：将缓冲区的所有内容转换为string
             * - deque_->push_back()：将string推入队列尾部
             * 
             * 异步的好处：
             * - 主线程不需要等待磁盘IO完成
             * - 提高程序响应速度
             * - 后台线程负责实际的文件写入
             */
        } else {
            /*
             * 同步模式或异步队列满的情况
             */
            // 同步模式：直接写入文件
            fputs(buff_.Peek(), fp_);
            /*
             * 同步模式处理：
             * - buff_.Peek()：获取缓冲区内容但不移除
             * - fputs()：直接写入文件指针fp_
             * 
             * 同步的特点：
             * - 立即写入磁盘，数据更安全
             * - 可能阻塞主线程，影响性能
             * - 适合对实时性要求高的场景
             */
        }
        
        /*
         * ==================== 第九步：清理缓冲区 ====================
         */
        buff_.RetrieveAll();            // 清空缓冲区
        /*
         * RetrieveAll()的作用：
         * - 清空缓冲区的所有内容
         * - 重置读写指针到起始位置
         * - 为下一条日志的写入做准备
         * 
         * 为什么要清空？
         * - 避免本次的日志内容影响下次写入
         * - 重用缓冲区内存，避免频繁分配
         * - 类似于清空草稿纸，准备写下一条
         */
    }
    /*
     * 整个过程总结（就像写一条完整的日志记录）：
     * 
     * 1. 🔒 加锁保护（确保线程安全）
     * 2. 📊 增加行数计数（记录写了多少行）
     * 3. ⏰ 添加时间戳（2024-03-15 14:30:25.123456）
     * 4. 🏷️  添加级别标识（[info] : ）
     * 5. 📝 添加用户内容（用户张三登录，ID=123）
     * 6. ➡️  添加换行符（准备下一行）
     * 7. 📤 选择输出方式（异步队列 or 直接写文件）
     * 8. 🧹 清理缓冲区（准备下次使用）
     * 9. 🔓 自动解锁（离开作用域）
     * 
     * 最终日志效果：
     * 2024-03-15 14:30:25.123456 [info] : 用户张三登录，ID=123
     * 
     * 这就是一条完整日志的诞生过程！
     */
}

// ==================== 异步写入实现 ====================

/**
 * @brief 异步日志写入实现
 * 
 * What: 在独立线程中执行的日志写入循环
 * Why:  将磁盘IO操作从主线程分离，提高主程序性能
 * How:  从阻塞队列中取出日志消息并写入文件
 */
void Log::AsyncWrite_() {
    /*
     * 主人，让我详细解释这个异步写入函数！
     * 
     * 这个函数是在后台线程中运行的"专职写手"！
     * 就像有一个专门的秘书，负责把老板交代的事情记录到文件里
     */
    
    string str = "";                    // 用来存储从队列中取出的日志消息
    /*
     * string str的作用：
     * - 这是一个临时容器，用来接收从队列里弹出的日志内容
     * - 每次循环都会被新的日志消息覆盖
     * - 就像一个中转站，消息从队列→str→文件
     */
    
    // 无限循环处理日志消息
    while(deque_->pop(str)) {
    /*
     * while循环详解：
     * 
     * deque_->pop(str)做了什么？
     * - deque_是一个阻塞队列（前面讲过的那个"传送带"）
     * - pop(str)：从队列头部取出一条日志消息，放到str里
     * - 如果队列为空，pop()会阻塞等待，直到有新消息
     * - 返回值：成功取出返回true，队列被关闭返回false
     * 
     * 循环的执行逻辑：
     * 1. 尝试从队列取消息
     * 2. 如果取到了（返回true），进入循环体处理
     * 3. 如果队列为空，pop()会等待（不是返回false）
     * 4. 只有当队列被显式关闭时，pop()才返回false，退出循环
     * 
     * 这就像一个24小时值班的邮递员：
     * - 一直在邮箱旁等待
     * - 有信就立即处理
     * - 没信就等着
     * - 直到邮局关门才下班
     */
        
        lock_guard<mutex> locker(mtx_);  // 获取互斥锁
        /*
         * 为什么这里要加锁？
         * 
         * 多线程安全问题：
         * - 主线程可能在调用write()，也需要操作文件指针fp_
         * - 如果主线程和后台线程同时写文件，会导致：
         *   * 文件内容错乱（两行日志混在一起）
         *   * 程序崩溃（文件指针状态不一致）
         * 
         * lock_guard的工作原理：
         * - 构造时自动给mtx_加锁
         * - 析构时自动释放锁（离开{}时）
         * - 即使函数异常退出，锁也会被正确释放
         * 
         * 为什么不用unique_lock？
         * - 这里只需要简单的"进来锁，出去解锁"
         * - 不需要手动控制锁的时机
         * - lock_guard更轻量，性能更好
         * 
         * 临界区：
         * - 从这里开始到}结束，只有一个线程能执行
         * - 保证文件写入的原子性
         */
        
        fputs(str.c_str(), fp_);         // 写入文件
        /*
         * fputs函数详解：
         * 
         * str.c_str()：
         * - str是C++的string对象
         * - c_str()方法返回C风格的字符串指针（char*）
         * - fputs需要char*类型，所以要转换
         * 
         * fputs(str.c_str(), fp_)：
         * - 第一个参数：要写入的字符串
         * - 第二个参数：文件指针（目标文件）
         * - 功能：把字符串写入到指定文件
         * 
         * 为什么用fputs而不是其他函数？
         * - fputs是C库函数，性能较好
         * - 不会添加额外的格式化（像printf那样）
         * - 直接写入，简单高效
         * 
         * 写入过程：
         * 队列中的消息 → str → 文件
         * 比如："2024-03-15 14:30:25.123456 [info] : 用户登录\n"
         */
    }
    /*
     * 整个AsyncWrite_函数的工作流程：
     * 
     * 1. 🔄 无限循环等待消息
     * 2. 📥 从队列中取出一条日志
     * 3. 🔒 加锁保护文件操作
     * 4. 📝 将日志写入文件
     * 5. 🔓 自动释放锁
     * 6. ⏮️  回到步骤1，继续处理下一条
     * 
     * 退出条件：
     * - 只有当队列被关闭时（比如程序要结束了）
     * - pop()才会返回false，跳出循环
     * - 后台线程自然结束
     * 
     * 这个设计的巧妙之处：
     * ✅ 主线程只管往队列塞消息，不阻塞
     * ✅ 后台线程专心写文件，效率高
     * ✅ 队列起到缓冲作用，平衡生产和消费速度
     * ✅ 即使磁盘很慢，也不会影响主程序响应
     * 
     * 生活中的类比：
     * - 就像餐厅的厨房和服务员
     * - 厨师（主线程）做好菜放到传菜窗口（队列）
     * - 服务员（后台线程）从窗口取菜送给客人（写文件）
     * - 厨师不用等服务员，服务员也按自己节奏工作
     * - 传菜窗口起到缓冲作用
     */
}

// ==================== 日志级别处理 ====================

/**
 * @brief 添加日志级别标题
 * @param level 日志级别数值
 * 
 * What: 根据日志级别添加相应的标识前缀
 * Why:  便于日志分析和问题定位
 * How:  switch语句映射级别到标识字符串
 */
void Log::AppendLogLevelTitle_(int level) {
    switch(level) {
    case 0:
        buff_.Append("[debug]: ", 9);   // 调试信息
        break;
    case 1:
        buff_.Append("[info] : ", 9);   // 普通信息
        break;
    case 2:
        buff_.Append("[warn] : ", 9);   // 警告信息
        break;
    case 3:
        buff_.Append("[error]: ", 9);   // 错误信息
        break;
    default:
        buff_.Append("[info] : ", 9);   // 默认为info级别
        break;
    }
}

// ==================== 缓冲区管理函数 ====================

/**
 * @brief 强制刷新日志缓冲区
 * 
 * What: 立即将缓冲区内容写入磁盘
 * Why:  确保重要日志及时持久化，防止程序异常退出时丢失
 * How:  调用系统的fflush()函数
 */
void Log::flush() {
    if(isAsync_) { 
        deque_->flush();                // 异步模式：刷新队列
    }
    fflush(fp_);                        // 刷新文件流缓冲区
}

// ==================== 状态查询接口 ====================

/**
 * @brief 获取当前日志级别
 * @return int 当前设置的日志级别
 * 
 * What: 返回当前日志系统的级别设置
 * Why:  外部代码需要了解当前日志级别来决定是否记录
 * How:  直接返回level_成员变量
 */
int Log::GetLevel() {
    lock_guard<mutex> locker(mtx_);
    return level_;
}

/**
 * @brief 设置日志级别
 * @param level 新的日志级别
 * 
 * What: 动态调整日志系统的记录级别
 * Why:  运行时调整日志详细程度，便于调试和性能优化
 * How:  线程安全地更新level_成员变量
 */
void Log::SetLevel(int level) {
    lock_guard<mutex> locker(mtx_);
    level_ = level;
}

/**
 * @brief 检查日志系统是否开启
 * @return bool true表示开启，false表示关闭
 * 
 * What: 查询日志系统的运行状态
 * Why:  允许外部代码根据日志状态优化性能
 * How:  返回isOpen_标志位
 */
bool Log::IsOpen() {
    lock_guard<mutex> locker(mtx_);
    return isOpen_;
}