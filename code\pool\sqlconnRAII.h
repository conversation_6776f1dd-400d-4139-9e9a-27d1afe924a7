/*
 * <AUTHOR> mark
 * @Date         : 2020-06-19
 * @copyleft Apache 2.0
 * 
 * SqlConnRAII 数据库连接RAII管理类
 * 设计模式：RAII（Resource Acquisition Is Initialization）
 * 核心特性：
 * 1. 构造时自动获取数据库连接
 * 2. 析构时自动归还连接到连接池
 * 3. 异常安全的资源管理
 * 4. 简化连接使用的代码逻辑
 * 5. 防止连接泄漏
 */ 

#ifndef SQLCONNRAII_H
#define SQLCONNRAII_H

#include "sqlconnpool.h"  // 数据库连接池

/**
 * @class SqlConnRAII
 * @brief 数据库连接RAII管理类
 * 
 * RAII设计模式应用：
 * ┌─────────────────────────────────────────────────────────┐
 * │                   SqlConnRAII                           │
 * │                                                         │
 * │  ┌─────────────┐              ┌─────────────┐          │
 * │  │   构造阶段   │              │   析构阶段   │          │
 * │  │             │              │             │          │
 * │  │ ┌─────────┐ │              │ ┌─────────┐ │          │
 * │  │ │ 获取    │ │              │ │ 归还    │ │          │
 * │  │ │ 连接    │ │              │ │ 连接    │ │          │
 * │  │ │         │ │              │ │         │ │          │
 * │  │ └─────────┘ │              │ └─────────┘ │          │
 * │  └─────────────┘              └─────────────┘          │
 * │         │                              ▲               │
 * │         ▼                              │               │
 * │  ┌─────────────────────────────────────────────────────┐ │
 * │  │              使用阶段                                │ │
 * │  │                                                     │ │
 * │  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐│ │
 * │  │  │ SQL     │  │ 查询    │  │ 更新    │  │ 事务    ││ │
 * │  │  │ 执行    │  │ 操作    │  │ 操作    │  │ 处理    ││ │
 * │  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘│ │
 * │  └─────────────────────────────────────────────────────┘ │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 使用示例：
 * ```cpp
 * void processUser(int userId) {
 *     MYSQL* sql = nullptr;
 *     SqlConnRAII sqlRAII(&sql, SqlConnPool::Instance());
 *     // sql现在指向有效的数据库连接
 *     
 *     // 执行数据库操作
 *     mysql_query(sql, "SELECT * FROM users WHERE id = ?");
 *     
 *     // 函数结束时，sqlRAII析构自动归还连接
 *     // 即使发生异常也能保证连接被正确归还
 * }
 * ```
 * 
 * RAII优势：
 * 1. 自动化资源管理，减少人为错误
 * 2. 异常安全，即使抛出异常也能正确释放资源
 * 3. 代码简洁，无需手动管理连接生命周期
 * 4. 防止资源泄漏，确保连接总是被归还
 */
class SqlConnRAII {
public:
    /**
     * @brief 构造函数 - 自动获取数据库连接
     * @param sql 输出参数，用于接收获取到的数据库连接指针
     * @param connpool 数据库连接池指针
     * 
     * 构造流程：
     * 1. 检查连接池有效性
     * 2. 从连接池获取一个可用连接
     * 3. 将连接指针赋值给输出参数
     * 4. 保存连接和连接池的引用供析构使用
     * 
     * 设计要点：
     * - 使用双指针传递，允许修改外部指针的值
     * - assert确保连接池指针有效
     * - 构造即获取，体现RAII核心思想
     */
    SqlConnRAII(MYSQL** sql, SqlConnPool *connpool) {
        assert(connpool);                    // 确保连接池指针有效
        *sql = connpool->GetConn();          // 从连接池获取连接
        sql_ = *sql;                         // 保存连接指针
        connpool_ = connpool;                // 保存连接池指针
    }
    
    /**
     * @brief 析构函数 - 自动归还数据库连接
     * 
     * 析构流程：
     * 1. 检查连接指针有效性
     * 2. 将连接归还给连接池
     * 3. 连接池负责后续的连接管理
     * 
     * 异常安全保证：
     * - 析构函数不抛出异常
     * - 即使在异常情况下也能正确归还连接
     * - 防止连接泄漏导致连接池耗尽
     */
    ~SqlConnRAII() {
        if(sql_) { 
            connpool_->FreeConn(sql_);       // 归还连接到连接池
        }
    }
    
private:
    // ==================== 成员变量 ====================
    
    /**
     * @brief 数据库连接指针
     * 
     * 保存从连接池获取的MySQL连接
     * 用于析构时归还连接
     */
    MYSQL *sql_;
    
    /**
     * @brief 连接池指针
     * 
     * 保存连接池的引用
     * 用于析构时调用FreeConn()归还连接
     */
    SqlConnPool* connpool_;
};

#endif //SQLCONNRAII_H