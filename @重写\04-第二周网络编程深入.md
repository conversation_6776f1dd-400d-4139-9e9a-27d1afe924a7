# 第二周：网络编程深入学习

> **主人，第二周我们要深入网络编程的核心！从最基础的概念到高级的I/O多路复用，每个知识点都配有完整的代码实例！**

## 📅 第二周时间安排

### Day 1: Socket编程基础原理

#### 上午任务 (3小时)：理解网络通信本质

**1. 网络通信基础概念**
```cpp
// 创建文件：day1_network_basics.cpp
#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>

// 任务1：理解IP地址和端口的概念
void understand_network_addressing() {
    std::cout << "=== 网络地址基础 ===" << std::endl;
    
    // IP地址的本质：32位整数（IPv4）
    std::string ip_str = "*************";
    struct in_addr ip_addr;
    
    // 字符串IP转换为网络格式
    if (inet_aton(ip_str.c_str(), &ip_addr) != 0) {
        std::cout << "IP地址 " << ip_str << " 转换为网络格式: " 
                  << std::hex << ntohl(ip_addr.s_addr) << std::endl;
    }
    
    // 网络格式转换为字符串
    char ip_buffer[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &ip_addr, ip_buffer, INET_ADDRSTRLEN);
    std::cout << "网络格式转回字符串: " << ip_buffer << std::endl;
    
    // 端口号的概念
    uint16_t port_host = 8080;           // 主机字节序
    uint16_t port_network = htons(port_host); // 网络字节序
    
    std::cout << "主机字节序端口: " << port_host << std::endl;
    std::cout << "网络字节序端口: " << port_network << std::endl;
    std::cout << "转回主机字节序: " << ntohs(port_network) << std::endl;
}

// 任务2：理解Socket的本质
void understand_socket_concept() {
    std::cout << "\n=== Socket概念深度理解 ===" << std::endl;
    
    std::cout << "Socket就像电话系统：" << std::endl;
    std::cout << "1. socket() = 买一部电话" << std::endl;
    std::cout << "2. bind() = 申请电话号码" << std::endl;
    std::cout << "3. listen() = 告诉电信局可以接电话了" << std::endl;
    std::cout << "4. accept() = 接听来电" << std::endl;
    std::cout << "5. read()/write() = 对话" << std::endl;
    std::cout << "6. close() = 挂电话" << std::endl;
    
    // 创建socket的详细过程
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        perror("socket creation failed");
        return;
    }
    
    std::cout << "\n创建了socket，文件描述符: " << sockfd << std::endl;
    std::cout << "AF_INET = " << AF_INET << " (IPv4协议族)" << std::endl;
    std::cout << "SOCK_STREAM = " << SOCK_STREAM << " (TCP协议)" << std::endl;
    
    // Socket选项的含义
    int opt = 1;
    if (setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        perror("setsockopt failed");
    } else {
        std::cout << "设置SO_REUSEADDR成功，允许地址重用" << std::endl;
    }
    
    close(sockfd);
}

int main() {
    understand_network_addressing();
    understand_socket_concept();
    return 0;
}
```

**2. TCP连接的三次握手原理**
```cpp
// 创建文件：day1_tcp_handshake.cpp
#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>

// 详细注释TCP三次握手过程
class TCPHandshakeDemo {
public:
    static void explain_handshake() {
        std::cout << "=== TCP三次握手详解 ===" << std::endl;
        std::cout << "1. 客户端发送SYN包到服务器" << std::endl;
        std::cout << "   - 客户端状态: CLOSED -> SYN_SENT" << std::endl;
        std::cout << "   - 消息: SYN, seq=x" << std::endl;
        
        std::cout << "2. 服务器回复SYN+ACK包" << std::endl;
        std::cout << "   - 服务器状态: LISTEN -> SYN_RECEIVED" << std::endl;
        std::cout << "   - 消息: SYN+ACK, seq=y, ack=x+1" << std::endl;
        
        std::cout << "3. 客户端发送ACK包" << std::endl;
        std::cout << "   - 客户端状态: SYN_SENT -> ESTABLISHED" << std::endl;
        std::cout << "   - 服务器状态: SYN_RECEIVED -> ESTABLISHED" << std::endl;
        std::cout << "   - 消息: ACK, seq=x+1, ack=y+1" << std::endl;
        
        std::cout << "连接建立完成！" << std::endl;
    }
    
    // 演示服务器端的socket状态变化
    static void demonstrate_server_states() {
        std::cout << "\n=== 服务器端状态演示 ===" << std::endl;
        
        // 1. 创建socket (CLOSED状态)
        int listen_fd = socket(AF_INET, SOCK_STREAM, 0);
        std::cout << "1. 创建socket，状态: CLOSED" << std::endl;
        
        // 2. 绑定地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;
        server_addr.sin_port = htons(8080);
        
        if (bind(listen_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            perror("bind failed");
            close(listen_fd);
            return;
        }
        std::cout << "2. 绑定地址成功" << std::endl;
        
        // 3. 开始监听 (LISTEN状态)
        if (listen(listen_fd, 128) < 0) {
            perror("listen failed");
            close(listen_fd);
            return;
        }
        std::cout << "3. 开始监听，状态: LISTEN" << std::endl;
        std::cout << "   等待队列长度: 128" << std::endl;
        
        std::cout << "4. 等待客户端连接..." << std::endl;
        std::cout << "   此时可以用 'telnet localhost 8080' 测试" << std::endl;
        std::cout << "   按回车键跳过实际连接等待..." << std::endl;
        std::cin.get();
        
        close(listen_fd);
        std::cout << "5. 关闭socket" << std::endl;
    }
};

int main() {
    TCPHandshakeDemo::explain_handshake();
    TCPHandshakeDemo::demonstrate_server_states();
    return 0;
}
```

#### 下午任务 (3小时)：实现基础Socket通信

**3. 最简单的客户端-服务器通信**
```cpp
// 创建文件：day1_simple_client_server.cpp
#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>
#include <thread>

// 简单的服务器实现
class SimpleServer {
public:
    void start(int port) {
        std::cout << "=== 启动简单服务器 ===" << std::endl;
        
        // 步骤1: 创建socket
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            perror("socket failed");
            return;
        }
        std::cout << "✓ 创建socket成功, fd = " << listen_fd_ << std::endl;
        
        // 步骤2: 设置socket选项
        int opt = 1;
        if (setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
            perror("setsockopt failed");
            close(listen_fd_);
            return;
        }
        std::cout << "✓ 设置socket选项成功" << std::endl;
        
        // 步骤3: 绑定地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;  // 监听所有网卡
        server_addr.sin_port = htons(port);
        
        if (bind(listen_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            perror("bind failed");
            close(listen_fd_);
            return;
        }
        std::cout << "✓ 绑定端口 " << port << " 成功" << std::endl;
        
        // 步骤4: 开始监听
        if (listen(listen_fd_, 5) < 0) {
            perror("listen failed");
            close(listen_fd_);
            return;
        }
        std::cout << "✓ 开始监听连接" << std::endl;
        
        // 步骤5: 接受连接循环
        accept_connections();
    }
    
private:
    int listen_fd_;
    
    void accept_connections() {
        while (true) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            std::cout << "等待客户端连接..." << std::endl;
            int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
            
            if (client_fd < 0) {
                perror("accept failed");
                continue;
            }
            
            // 打印客户端信息
            char client_ip[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
            uint16_t client_port = ntohs(client_addr.sin_port);
            
            std::cout << "✓ 新客户端连接: " << client_ip << ":" << client_port 
                      << " (fd=" << client_fd << ")" << std::endl;
            
            // 处理客户端
            handle_client(client_fd, client_ip, client_port);
            
            close(client_fd);
            std::cout << "✓ 客户端连接已关闭" << std::endl;
        }
    }
    
    void handle_client(int client_fd, const char* client_ip, uint16_t client_port) {
        char buffer[1024];
        
        // 发送欢迎消息
        std::string welcome = "欢迎连接到服务器！请输入消息，输入'quit'退出。\n";
        send(client_fd, welcome.c_str(), welcome.length(), 0);
        
        while (true) {
            memset(buffer, 0, sizeof(buffer));
            ssize_t bytes_received = recv(client_fd, buffer, sizeof(buffer) - 1, 0);
            
            if (bytes_received <= 0) {
                if (bytes_received == 0) {
                    std::cout << "[" << client_ip << ":" << client_port << "] 客户端断开连接" << std::endl;
                } else {
                    perror("recv failed");
                }
                break;
            }
            
            buffer[bytes_received] = '\0';
            std::string message(buffer);
            
            // 移除换行符
            if (!message.empty() && message.back() == '\n') {
                message.pop_back();
            }
            
            std::cout << "[" << client_ip << ":" << client_port << "] 收到: " << message << std::endl;
            
            // 检查退出命令
            if (message == "quit") {
                std::string goodbye = "再见！\n";
                send(client_fd, goodbye.c_str(), goodbye.length(), 0);
                break;
            }
            
            // 回显消息
            std::string response = "服务器回复: " + message + "\n";
            send(client_fd, response.c_str(), response.length(), 0);
        }
    }
};

// 简单的客户端实现
class SimpleClient {
public:
    void connect_to_server(const std::string& server_ip, int port) {
        std::cout << "=== 启动简单客户端 ===" << std::endl;
        
        // 创建socket
        int sock_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (sock_fd < 0) {
            perror("socket failed");
            return;
        }
        std::cout << "✓ 创建socket成功" << std::endl;
        
        // 设置服务器地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        
        if (inet_pton(AF_INET, server_ip.c_str(), &server_addr.sin_addr) <= 0) {
            perror("inet_pton failed");
            close(sock_fd);
            return;
        }
        
        // 连接服务器
        std::cout << "正在连接 " << server_ip << ":" << port << "..." << std::endl;
        if (connect(sock_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            perror("connect failed");
            close(sock_fd);
            return;
        }
        std::cout << "✓ 连接服务器成功!" << std::endl;
        
        // 通信循环
        communicate_with_server(sock_fd);
        
        close(sock_fd);
        std::cout << "✓ 断开连接" << std::endl;
    }
    
private:
    void communicate_with_server(int sock_fd) {
        char buffer[1024];
        
        // 接收欢迎消息
        ssize_t bytes_received = recv(sock_fd, buffer, sizeof(buffer) - 1, 0);
        if (bytes_received > 0) {
            buffer[bytes_received] = '\0';
            std::cout << "服务器消息: " << buffer;
        }
        
        std::string input;
        while (true) {
            std::cout << "请输入消息 (输入quit退出): ";
            std::getline(std::cin, input);
            
            // 发送消息
            input += "\n";
            send(sock_fd, input.c_str(), input.length(), 0);
            
            if (input.find("quit") != std::string::npos) {
                break;
            }
            
            // 接收回复
            memset(buffer, 0, sizeof(buffer));
            bytes_received = recv(sock_fd, buffer, sizeof(buffer) - 1, 0);
            if (bytes_received > 0) {
                buffer[bytes_received] = '\0';
                std::cout << buffer;
            }
        }
    }
};

void run_server() {
    SimpleServer server;
    server.start(8080);
}

void run_client() {
    std::this_thread::sleep_for(std::chrono::seconds(1));  // 等待服务器启动
    SimpleClient client;
    client.connect_to_server("127.0.0.1", 8080);
}

int main() {
    std::cout << "选择运行模式:" << std::endl;
    std::cout << "1. 服务器模式" << std::endl;
    std::cout << "2. 客户端模式" << std::endl;
    std::cout << "3. 自动演示模式 (同时运行服务器和客户端)" << std::endl;
    
    int choice;
    std::cin >> choice;
    std::cin.ignore();  // 忽略换行符
    
    switch (choice) {
        case 1:
            run_server();
            break;
        case 2:
            run_client();
            break;
        case 3: {
            std::thread server_thread(run_server);
            std::thread client_thread(run_client);
            
            client_thread.join();
            server_thread.detach();  // 客户端结束后，让服务器继续运行
            break;
        }
        default:
            std::cout << "无效选择" << std::endl;
            break;
    }
    
    return 0;
}
```

### Day 2: 阻塞与非阻塞I/O深入理解

#### 上午任务 (3小时)：理解I/O模型

**1. 阻塞I/O的问题演示**
```cpp
// 创建文件：day2_blocking_io_problems.cpp
#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <thread>
#include <vector>
#include <chrono>

// 演示阻塞I/O的性能问题
class BlockingIODemo {
public:
    static void demonstrate_blocking_problem() {
        std::cout << "=== 阻塞I/O性能问题演示 ===" << std::endl;
        
        // 创建服务器
        int listen_fd = socket(AF_INET, SOCK_STREAM, 0);
        int opt = 1;
        setsockopt(listen_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;
        server_addr.sin_port = htons(8080);
        
        bind(listen_fd, (struct sockaddr*)&server_addr, sizeof(server_addr));
        listen(listen_fd, 128);
        
        std::cout << "服务器启动，监听端口 8080" << std::endl;
        std::cout << "问题演示：一次只能处理一个客户端" << std::endl;
        
        while (true) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            std::cout << "\n等待客户端连接..." << std::endl;
            int client_fd = accept(listen_fd, (struct sockaddr*)&client_addr, &client_len);
            
            if (client_fd < 0) {
                perror("accept failed");
                continue;
            }
            
            char client_ip[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
            std::cout << "客户端连接: " << client_ip << ":" << ntohs(client_addr.sin_port) << std::endl;
            
            // 这里是问题所在：阻塞处理单个客户端
            handle_client_blocking(client_fd, client_ip);
            
            close(client_fd);
            std::cout << "客户端断开，可以接受下一个连接" << std::endl;
        }
        
        close(listen_fd);
    }
    
private:
    static void handle_client_blocking(int client_fd, const char* client_ip) {
        std::cout << "[" << client_ip << "] 开始处理（阻塞模式）" << std::endl;
        
        char buffer[1024];
        while (true) {
            std::cout << "[" << client_ip << "] 等待数据..." << std::endl;
            
            // 这里会阻塞！如果客户端不发送数据，服务器就卡住了
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read <= 0) {
                std::cout << "[" << client_ip << "] 连接断开或出错" << std::endl;
                break;
            }
            
            buffer[bytes_read] = '\0';
            std::cout << "[" << client_ip << "] 收到数据: " << buffer;
            
            // 模拟处理时间
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            // 回送数据
            write(client_fd, buffer, bytes_read);
            std::cout << "[" << client_ip << "] 已回送数据" << std::endl;
        }
    }
};

// 创建多个客户端来测试
class MultipleClients {
public:
    static void create_test_clients(int count) {
        std::cout << "=== 创建 " << count << " 个测试客户端 ===" << std::endl;
        
        std::vector<std::thread> client_threads;
        
        for (int i = 0; i < count; ++i) {
            client_threads.emplace_back([i]() {
                std::this_thread::sleep_for(std::chrono::milliseconds(i * 100));
                create_single_client(i + 1);
            });
        }
        
        for (auto& t : client_threads) {
            t.join();
        }
    }
    
private:
    static void create_single_client(int client_id) {
        int sock_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (sock_fd < 0) {
            perror("socket failed");
            return;
        }
        
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(8080);
        inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr);
        
        std::cout << "客户端 " << client_id << " 尝试连接..." << std::endl;
        
        if (connect(sock_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            perror("connect failed");
            close(sock_fd);
            return;
        }
        
        std::cout << "客户端 " << client_id << " 连接成功" << std::endl;
        
        // 发送一些数据
        std::string message = "Hello from client " + std::to_string(client_id) + "\n";
        write(sock_fd, message.c_str(), message.length());
        
        // 等待一段时间再发送数据，演示阻塞问题
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        message = "Second message from client " + std::to_string(client_id) + "\n";
        write(sock_fd, message.c_str(), message.length());
        
        close(sock_fd);
        std::cout << "客户端 " << client_id << " 断开连接" << std::endl;
    }
};

int main() {
    std::cout << "阻塞I/O问题演示" << std::endl;
    std::cout << "1. 运行服务器" << std::endl;
    std::cout << "2. 创建多个客户端测试" << std::endl;
    
    int choice;
    std::cin >> choice;
    
    if (choice == 1) {
        BlockingIODemo::demonstrate_blocking_problem();
    } else if (choice == 2) {
        MultipleClients::create_test_clients(3);
    }
    
    return 0;
}
```

**2. 非阻塞I/O的实现**
```cpp
// 创建文件：day2_nonblocking_io.cpp
#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <vector>
#include <algorithm>

// 设置文件描述符为非阻塞模式
void set_nonblocking(int fd) {
    int flags = fcntl(fd, F_GETFL, 0);
    if (flags == -1) {
        perror("fcntl F_GETFL");
        return;
    }
    
    if (fcntl(fd, F_SETFL, flags | O_NONBLOCK) == -1) {
        perror("fcntl F_SETFL");
        return;
    }
    
    std::cout << "文件描述符 " << fd << " 设置为非阻塞模式" << std::endl;
}

// 非阻塞I/O演示
class NonBlockingIODemo {
public:
    void start_server() {
        std::cout << "=== 非阻塞I/O服务器演示 ===" << std::endl;
        
        // 创建监听socket
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            perror("socket failed");
            return;
        }
        
        // 设置为非阻塞
        set_nonblocking(listen_fd_);
        
        // 设置地址复用
        int opt = 1;
        setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 绑定地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;
        server_addr.sin_port = htons(8080);
        
        if (bind(listen_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            perror("bind failed");
            close(listen_fd_);
            return;
        }
        
        if (listen(listen_fd_, 128) < 0) {
            perror("listen failed");
            close(listen_fd_);
            return;
        }
        
        std::cout << "非阻塞服务器启动，监听端口 8080" << std::endl;
        
        // 非阻塞事件循环
        event_loop();
        
        close(listen_fd_);
    }
    
private:
    int listen_fd_;
    std::vector<int> client_fds_;
    
    void event_loop() {
        std::cout << "进入非阻塞事件循环" << std::endl;
        
        while (true) {
            // 检查新连接
            handle_new_connections();
            
            // 处理现有连接的数据
            handle_client_data();
            
            // 短暂休眠，避免CPU空转
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
    
    void handle_new_connections() {
        while (true) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
            
            if (client_fd < 0) {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    // 没有新连接，这是正常的
                    break;
                } else {
                    perror("accept failed");
                    break;
                }
            }
            
            // 设置客户端socket为非阻塞
            set_nonblocking(client_fd);
            
            char client_ip[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
            
            std::cout << "新客户端连接: " << client_ip << ":" 
                      << ntohs(client_addr.sin_port) << " (fd=" << client_fd << ")" << std::endl;
            
            client_fds_.push_back(client_fd);
        }
    }
    
    void handle_client_data() {
        for (auto it = client_fds_.begin(); it != client_fds_.end(); ) {
            int client_fd = *it;
            char buffer[1024];
            
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                std::cout << "从客户端 " << client_fd << " 收到: " << buffer;
                
                // 回送数据
                write(client_fd, buffer, bytes_read);
                ++it;
                
            } else if (bytes_read == 0) {
                // 客户端关闭连接
                std::cout << "客户端 " << client_fd << " 断开连接" << std::endl;
                close(client_fd);
                it = client_fds_.erase(it);
                
            } else {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    // 没有数据可读，继续下一个客户端
                    ++it;
                } else {
                    // 读取错误
                    perror("read failed");
                    close(client_fd);
                    it = client_fds_.erase(it);
                }
            }
        }
    }
};

int main() {
    NonBlockingIODemo server;
    server.start_server();
    return 0;
}
```

#### 下午任务 (3小时)：select/poll多路复用

**3. select多路复用详解**
```cpp
// 创建文件：day2_select_multiplexing.cpp
#include <iostream>
#include <sys/socket.h>
#include <sys/select.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <vector>
#include <algorithm>

class SelectServer {
public:
    void start() {
        std::cout << "=== Select多路复用服务器 ===" << std::endl;
        
        if (!initialize_server()) {
            return;
        }
        
        std::cout << "Select服务器启动成功，监听端口 8080" << std::endl;
        select_event_loop();
        
        cleanup();
    }
    
private:
    int listen_fd_;
    std::vector<int> client_fds_;
    
    bool initialize_server() {
        // 创建监听socket
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            perror("socket failed");
            return false;
        }
        
        // 设置地址复用
        int opt = 1;
        setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 绑定地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;
        server_addr.sin_port = htons(8080);
        
        if (bind(listen_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            perror("bind failed");
            close(listen_fd_);
            return false;
        }
        
        if (listen(listen_fd_, 128) < 0) {
            perror("listen failed");
            close(listen_fd_);
            return false;
        }
        
        return true;
    }
    
    void select_event_loop() {
        std::cout << "进入select事件循环" << std::endl;
        
        while (true) {
            fd_set read_fds;
            int max_fd = prepare_fd_set(read_fds);
            
            std::cout << "等待I/O事件，最大fd: " << max_fd << std::endl;
            
            // 设置超时时间
            struct timeval timeout;
            timeout.tv_sec = 5;   // 5秒超时
            timeout.tv_usec = 0;
            
            int ready = select(max_fd + 1, &read_fds, nullptr, nullptr, &timeout);
            
            if (ready < 0) {
                perror("select failed");
                break;
            } else if (ready == 0) {
                std::cout << "Select超时，当前连接数: " << client_fds_.size() << std::endl;
                continue;
            }
            
            std::cout << "有 " << ready << " 个文件描述符就绪" << std::endl;
            
            // 处理就绪的文件描述符
            handle_ready_fds(read_fds);
        }
    }
    
    int prepare_fd_set(fd_set& read_fds) {
        FD_ZERO(&read_fds);
        
        // 添加监听socket
        FD_SET(listen_fd_, &read_fds);
        int max_fd = listen_fd_;
        
        // 添加所有客户端socket
        for (int client_fd : client_fds_) {
            FD_SET(client_fd, &read_fds);
            max_fd = std::max(max_fd, client_fd);
        }
        
        std::cout << "监视 " << (1 + client_fds_.size()) << " 个文件描述符" << std::endl;
        return max_fd;
    }
    
    void handle_ready_fds(const fd_set& read_fds) {
        // 检查监听socket
        if (FD_ISSET(listen_fd_, &read_fds)) {
            std::cout << "监听socket就绪，有新连接" << std::endl;
            handle_new_connection();
        }
        
        // 检查客户端socket（从后往前遍历，便于删除）
        for (auto it = client_fds_.rbegin(); it != client_fds_.rend(); ) {
            int client_fd = *it;
            
            if (FD_ISSET(client_fd, &read_fds)) {
                std::cout << "客户端 " << client_fd << " 有数据可读" << std::endl;
                
                if (!handle_client_data(client_fd)) {
                    // 客户端断开，移除
                    std::cout << "移除客户端 " << client_fd << std::endl;
                    close(client_fd);
                    // 注意：reverse_iterator的erase需要转换
                    auto forward_it = std::next(it).base();
                    client_fds_.erase(forward_it);
                    ++it;
                } else {
                    ++it;
                }
            } else {
                ++it;
            }
        }
    }
    
    void handle_new_connection() {
        struct sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);
        
        int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
        if (client_fd < 0) {
            perror("accept failed");
            return;
        }
        
        char client_ip[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
        
        std::cout << "新客户端连接: " << client_ip << ":" 
                  << ntohs(client_addr.sin_port) << " (fd=" << client_fd << ")" << std::endl;
        
        client_fds_.push_back(client_fd);
        std::cout << "当前连接数: " << client_fds_.size() << std::endl;
    }
    
    bool handle_client_data(int client_fd) {
        char buffer[1024];
        ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
        
        if (bytes_read > 0) {
            buffer[bytes_read] = '\0';
            std::cout << "从客户端 " << client_fd << " 收到: " << buffer;
            
            // 回送数据
            write(client_fd, buffer, bytes_read);
            return true;
            
        } else if (bytes_read == 0) {
            std::cout << "客户端 " << client_fd << " 断开连接" << std::endl;
            return false;
            
        } else {
            perror("read failed");
            return false;
        }
    }
    
    void cleanup() {
        for (int client_fd : client_fds_) {
            close(client_fd);
        }
        close(listen_fd_);
        std::cout << "服务器已关闭" << std::endl;
    }
};

int main() {
    SelectServer server;
    server.start();
    return 0;
}
```

### Day 3-7: 继续其他网络编程主题...

**主人，这样的详细程度如何？每个概念我都提供了：**

1. **理论解释** - 为什么需要这个技术
2. **详细代码** - 完整的可运行示例
3. **逐步注释** - 每行代码的作用
4. **实战演示** - 可以直接测试的程序
5. **问题分析** - 技术的优缺点对比

您希望我继续完成第二周剩下的几天内容，还是先看看这个深度是否符合您的需求？我可以根据您的反馈调整后续内容的详细程度！ 