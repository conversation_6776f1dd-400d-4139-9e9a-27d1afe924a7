# 实战第一步：从零开始写Echo服务器

> **主人，现在开始我们的第一个实战项目！我们从最简单的Echo服务器开始，逐步掌握网络编程的核心概念！**

## 🎯 本章目标

通过编写一个完整的Echo服务器，您将掌握：
- Socket编程的基本流程
- 网络字节序转换
- 基本的错误处理
- 简单的多客户端支持
- 为后续复杂功能打下基础

## 📋 项目规划

我们将分5个版本逐步完善Echo服务器：

```
版本1：最简单的回显服务器 (50行)
├── 创建socket
├── 绑定地址
├── 监听连接
└── 单客户端回显

版本2：多客户端支持 (100行)
├── 循环接受连接
├── 基本错误处理
└── 连接信息显示

版本3：非阻塞I/O (150行)
├── 设置非阻塞模式
├── select多路复用
└── 优雅错误处理

版本4：基础类封装 (200行)
├── Socket类封装
├── 地址类封装
└── 面向对象设计

版本5：异常安全版本 (250行)
├── RAII资源管理
├── 智能指针使用
└── 完整错误处理
```

---

## 📝 版本1：最简单的Echo服务器

### 理论准备：TCP服务器的基本流程

```
TCP服务器的标准流程：
1. socket()  - 创建套接字
2. bind()    - 绑定地址和端口
3. listen()  - 开始监听
4. accept()  - 接受客户端连接
5. read()    - 读取客户端数据
6. write()   - 发送数据给客户端
7. close()   - 关闭连接
```

### 代码实现

```cpp
// 创建文件：version1_simple_echo.cpp

// 【头文件详解】主人，我先给您解释这些头文件的作用：
#include <iostream>      // 标准输入输出流，用于cout、cerr等
#include <string>        // 字符串类，用于处理文本
#include <cstring>       // C风格字符串函数，如memset、strerror
#include <sys/socket.h>  // Socket编程的核心头文件，包含socket()、bind()等函数
#include <netinet/in.h>  // 网络地址结构体，如sockaddr_in
#include <arpa/inet.h>   // IP地址转换函数，如inet_ntoa、htons
#include <unistd.h>      // Unix标准函数，如read、write、close

class SimpleEchoServer {
public:
    void start(int port) {
        std::cout << "=== 版本1：最简单的Echo服务器 ===" << std::endl;
        
        // 【步骤1：创建socket】
        // socket()函数详解：
        // - AF_INET: 地址族，表示IPv4网络
        // - SOCK_STREAM: 套接字类型，表示TCP协议（可靠传输）
        // - 0: 协议，0表示自动选择（TCP）
        // 返回值：成功返回文件描述符(fd)，失败返回-1
        int listen_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd < 0) {
            // errno是全局变量，存储最后一次系统调用的错误码
            // strerror()将错误码转换为可读的错误信息
            std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
            return;
        }
        std::cout << "✓ Socket创建成功，fd = " << listen_fd << std::endl;
        
        // 【步骤2：设置地址复用】
        // 主人，这个很重要！解决"Address already in use"错误
        // 当服务器重启时，端口可能还被系统占用，这个选项允许立即重用
        int opt = 1;  // 1表示启用该选项
        // setsockopt()函数详解：
        // - listen_fd: 要设置的socket
        // - SOL_SOCKET: 套接字级别的选项
        // - SO_REUSEADDR: 允许地址重用的选项
        // - &opt: 选项值的指针
        // - sizeof(opt): 选项值的大小
        if (setsockopt(listen_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
            std::cerr << "设置socket选项失败: " << strerror(errno) << std::endl;
            close(listen_fd);  // 出错时要关闭已创建的socket
            return;
        }
        std::cout << "✓ 地址复用设置成功" << std::endl;
        
        // 【步骤3：绑定地址和端口】
        // 主人，这里要创建一个地址结构体，告诉系统我们要监听哪个地址和端口
        struct sockaddr_in server_addr;  // IPv4地址结构体
        
        // memset()将结构体清零，这是个好习惯，避免垃圾数据
        memset(&server_addr, 0, sizeof(server_addr));
        
        // 设置地址族为IPv4
        server_addr.sin_family = AF_INET;
        
        // INADDR_ANY表示监听所有网卡接口（0.0.0.0）
        // 这样无论客户端从哪个IP连接都能接收到
        server_addr.sin_addr.s_addr = INADDR_ANY;
        
        // 【重要】网络字节序转换！
        // htons()将主机字节序转换为网络字节序
        // 网络传输统一使用大端序，但CPU可能是小端序
        server_addr.sin_port = htons(port);
        
        // bind()函数将socket与地址绑定
        // 参数说明：
        // - listen_fd: 要绑定的socket
        // - (struct sockaddr*)&server_addr: 地址结构体（需要类型转换）
        // - sizeof(server_addr): 地址结构体的大小
        if (bind(listen_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            std::cerr << "绑定地址失败: " << strerror(errno) << std::endl;
            close(listen_fd);
            return;
        }
        std::cout << "✓ 绑定地址成功，监听端口 " << port << std::endl;
        
        // 【步骤4：开始监听】
        // listen()让socket进入监听状态，等待客户端连接
        // 参数说明：
        // - listen_fd: 监听的socket
        // - 128: 等待队列长度（backlog），表示最多可以有128个连接在排队等待accept
        if (listen(listen_fd, 128) < 0) {
            std::cerr << "监听失败: " << strerror(errno) << std::endl;
            close(listen_fd);
            return;
        }
        std::cout << "✓ 开始监听，等待客户端连接..." << std::endl;
        
        // 【步骤5：接受连接并处理】
        // 主循环：不断接受新的客户端连接
        while (true) {
            // 准备存储客户端地址信息的结构体
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            // accept()函数等待并接受一个客户端连接
            // 这是一个阻塞函数，会一直等到有客户端连接
            // 参数说明：
            // - listen_fd: 监听socket
            // - (struct sockaddr*)&client_addr: 用来存储客户端地址信息
            // - &client_len: 地址结构体大小的指针
            // 返回值：成功返回新的socket文件描述符，失败返回-1
            int client_fd = accept(listen_fd, (struct sockaddr*)&client_addr, &client_len);
            if (client_fd < 0) {
                std::cerr << "接受连接失败: " << strerror(errno) << std::endl;
                continue;  // 继续等待下一个连接
            }
            
            // 【显示客户端信息】
            // inet_ntoa()将IP地址从二进制转换为点分十进制字符串
            // ntohs()将端口号从网络字节序转换为主机字节序
            std::cout << "✓ 新客户端连接: " << inet_ntoa(client_addr.sin_addr) 
                      << ":" << ntohs(client_addr.sin_port) 
                      << " (fd=" << client_fd << ")" << std::endl;
            
            // 处理该客户端的数据（调用私有函数）
            handle_client(client_fd);
            
            // 处理完毕后关闭客户端连接
            close(client_fd);
            std::cout << "✓ 客户端连接已关闭" << std::endl;
        }
        
        // 程序结束时关闭监听socket
        close(listen_fd);
    }
    
private:
    // 【处理单个客户端的函数】
    void handle_client(int client_fd) {
        char buffer[1024];  // 缓冲区，用来存储接收到的数据
        
        // 循环处理该客户端的所有数据
        while (true) {
            // 【读取客户端数据】
            // read()函数从socket读取数据
            // 参数说明：
            // - client_fd: 客户端socket文件描述符
            // - buffer: 存储数据的缓冲区
            // - sizeof(buffer) - 1: 最多读取的字节数（留一个位置给'\0'）
            // 返回值：实际读取的字节数，0表示连接关闭，-1表示出错
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            // 【判断读取结果】
            if (bytes_read <= 0) {
                if (bytes_read == 0) {
                    // 返回0表示客户端正常关闭连接（发送了FIN包）
                    std::cout << "客户端主动断开连接" << std::endl;
                } else {
                    // 返回-1表示读取出错
                    std::cerr << "读取数据失败: " << strerror(errno) << std::endl;
                }
                break;  // 跳出循环，结束处理该客户端
            }
            
            // 【处理接收到的数据】
            // 在缓冲区末尾添加字符串结束符，这样就可以当作C字符串处理
            buffer[bytes_read] = '\0';
            std::cout << "收到数据 (" << bytes_read << " 字节): " << buffer;
            
            // 【回显数据给客户端】
            // write()函数将数据写入socket发送给客户端
            // 参数说明：
            // - client_fd: 客户端socket文件描述符
            // - buffer: 要发送的数据
            // - bytes_read: 要发送的字节数
            // 返回值：实际发送的字节数，-1表示出错
            ssize_t bytes_written = write(client_fd, buffer, bytes_read);
            if (bytes_written < 0) {
                std::cerr << "发送数据失败: " << strerror(errno) << std::endl;
                break;  // 发送失败，结束处理
            }
            
            std::cout << "回显数据 (" << bytes_written << " 字节)" << std::endl;
        }
    }
};

int main() {
    SimpleEchoServer server;
    server.start(8080);  // 监听8080端口
    return 0;
}
```

### 编译和测试

```bash
# 编译
g++ -o version1_echo version1_simple_echo.cpp

# 运行服务器
./version1_echo

# 在另一个终端测试
telnet localhost 8080
# 或者
echo "Hello World" | nc localhost 8080
```

### 🔍 核心概念详解

**1. 字节序转换（主人必须理解的重要概念！）**
```cpp
server_addr.sin_port = htons(port);  // 主机字节序 -> 网络字节序

// 【为什么需要字节序转换？】
// 想象一个数字 0x12345678 在内存中的存储：
// 
// 大端序（Big Endian）：    [12][34][56][78]  ← 高位在前
// 小端序（Little Endian）： [78][56][34][12]  ← 低位在前
//
// 网络传输统一使用大端序，但不同CPU架构可能不同：
// - Intel x86/x64: 小端序
// - ARM: 可配置
// - 网络: 统一大端序
//
// 转换函数：
// htons() = Host TO Network Short (16位)
// htonl() = Host TO Network Long (32位)  
// ntohs() = Network TO Host Short
// ntohl() = Network TO Host Long
```

**2. 地址结构体详解**
```cpp
struct sockaddr_in server_addr;  // IPv4专用地址结构体

// 【结构体成员详解】
server_addr.sin_family = AF_INET;        // 地址族：AF_INET表示IPv4
server_addr.sin_addr.s_addr = INADDR_ANY; // IP地址：INADDR_ANY = 0.0.0.0（所有网卡）
server_addr.sin_port = htons(port);      // 端口号：必须转换为网络字节序

// 【为什么要用INADDR_ANY？】
// 服务器通常有多个网卡接口：
// - 127.0.0.1 (本地回环)
// - ************* (局域网)
// - ********* (另一个网络)
// 使用INADDR_ANY可以监听所有接口，客户端从任何IP都能连接
```

**3. 错误处理机制**
```cpp
if (socket_fd < 0) {
    std::cerr << "错误信息: " << strerror(errno) << std::endl;
}

// 【errno详解】
// errno是全局变量，存储最后一次系统调用的错误码
// 常见错误码：
// EADDRINUSE (98)  - 地址已被使用
// EACCES (13)      - 权限不足
// ECONNREFUSED (111) - 连接被拒绝
// 
// strerror()将数字错误码转换为可读的错误描述
```

**4. 文件描述符（fd）概念**
```cpp
int listen_fd = socket(...);  // 返回文件描述符

// 【什么是文件描述符？】
// 在Unix/Linux中，"一切皆文件"：
// - 普通文件: fd = 3, 4, 5...
// - Socket: 也是文件，也有fd
// - 标准输入: fd = 0
// - 标准输出: fd = 1  
// - 标准错误: fd = 2
//
// fd就像是文件的"身份证号"，系统通过fd来识别和操作文件/socket
```

---

## 🔄 版本2：支持多客户端的Echo服务器

### 问题分析

版本1的问题：
- 同时只能处理一个客户端
- 一个客户端断开后才能接受新连接
- 没有优雅的关闭机制

### 解决方案

```cpp
// 创建文件：version2_multi_client.cpp

// 【新增头文件解释】
#include <iostream>      // 标准输入输出
#include <string>        // 字符串处理
#include <cstring>       // C风格字符串函数
#include <vector>        // 动态数组（这里用来管理客户端fd列表）
#include <sys/socket.h>  // Socket编程
#include <netinet/in.h>  // 网络地址结构
#include <arpa/inet.h>   // IP地址转换
#include <unistd.h>      // Unix系统调用（read、write、close、fork等）
#include <signal.h>      // 信号处理（处理SIGPIPE、SIGINT等信号）

class MultiClientEchoServer {
public:
    MultiClientEchoServer() : running_(true) {
        // 【信号处理详解】
        // SIGPIPE信号：当向已关闭的socket写数据时，系统会发送此信号
        // 默认行为是终止程序，我们选择忽略它，自己处理写入错误
        // SIG_IGN表示忽略该信号
        signal(SIGPIPE, SIG_IGN);
    }
    
    void start(int port) {
        std::cout << "=== 版本2：多客户端Echo服务器 ===" << std::endl;
        
        listen_fd_ = create_and_bind(port);
        if (listen_fd_ < 0) {
            return;
        }
        
        if (start_listen() < 0) {
            return;
        }
        
        std::cout << "服务器启动成功，按Ctrl+C停止" << std::endl;
        
        // 主事件循环
        while (running_) {
            handle_new_connection();
        }
        
        cleanup();
    }
    
    void stop() {
        running_ = false;
    }
    
private:
    int listen_fd_;
    bool running_;
    std::vector<int> client_fds_;  // 保存所有客户端fd
    
    int create_and_bind(int port) {
        int fd = socket(AF_INET, SOCK_STREAM, 0);
        if (fd < 0) {
            std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
            return -1;
        }
        
        // 设置地址复用
        int opt = 1;
        setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 绑定地址
        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = INADDR_ANY;
        addr.sin_port = htons(port);
        
        if (bind(fd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            std::cerr << "绑定失败: " << strerror(errno) << std::endl;
            close(fd);
            return -1;
        }
        
        std::cout << "✓ 绑定端口 " << port << " 成功" << std::endl;
        return fd;
    }
    
    int start_listen() {
        if (listen(listen_fd_, 128) < 0) {
            std::cerr << "监听失败: " << strerror(errno) << std::endl;
            close(listen_fd_);
            return -1;
        }
        
        std::cout << "✓ 开始监听" << std::endl;
        return 0;
    }
    
    void handle_new_connection() {
        struct sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);
        
        int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
        if (client_fd < 0) {
            if (errno != EINTR) {  // 忽略被信号中断的情况
                std::cerr << "接受连接失败: " << strerror(errno) << std::endl;
            }
            return;
        }
        
        // 显示客户端信息
        char client_ip[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
        uint16_t client_port = ntohs(client_addr.sin_port);
        
        std::cout << "✓ 新连接: " << client_ip << ":" << client_port 
                  << " (fd=" << client_fd << ")" << std::endl;
        
        // 为每个客户端创建子进程处理
        pid_t pid = fork();
        if (pid == 0) {
            // 子进程：处理客户端
            close(listen_fd_);  // 子进程不需要监听socket
            handle_client(client_fd, client_ip, client_port);
            close(client_fd);
            exit(0);
        } else if (pid > 0) {
            // 父进程：继续接受新连接
            close(client_fd);  // 父进程不需要客户端socket
        } else {
            std::cerr << "创建进程失败: " << strerror(errno) << std::endl;
            close(client_fd);
        }
    }
    
    void handle_client(int client_fd, const char* client_ip, uint16_t client_port) {
        std::cout << "[" << client_ip << ":" << client_port << "] 开始处理" << std::endl;
        
        char buffer[1024];
        
        while (true) {
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read <= 0) {
                if (bytes_read == 0) {
                    std::cout << "[" << client_ip << ":" << client_port << "] 客户端断开" << std::endl;
                } else {
                    std::cerr << "[" << client_ip << ":" << client_port << "] 读取失败: " 
                              << strerror(errno) << std::endl;
                }
                break;
            }
            
            buffer[bytes_read] = '\0';
            
            // 添加时间戳和客户端信息
            std::string response = "[Echo from " + std::string(client_ip) + ":" + 
                                  std::to_string(client_port) + "] " + std::string(buffer);
            
            ssize_t bytes_written = write(client_fd, response.c_str(), response.length());
            if (bytes_written < 0) {
                std::cerr << "[" << client_ip << ":" << client_port << "] 发送失败: " 
                          << strerror(errno) << std::endl;
                break;
            }
            
            std::cout << "[" << client_ip << ":" << client_port << "] " 
                      << "收到 " << bytes_read << " 字节，回显 " << bytes_written << " 字节" << std::endl;
        }
    }
    
    void cleanup() {
        if (listen_fd_ >= 0) {
            close(listen_fd_);
            std::cout << "✓ 服务器已关闭" << std::endl;
        }
    }
};

// 全局变量，用于信号处理
MultiClientEchoServer* g_server = nullptr;

void signal_handler(int sig) {
    if (g_server && sig == SIGINT) {
        std::cout << "\n收到停止信号，正在关闭服务器..." << std::endl;
        g_server->stop();
    }
}

int main() {
    MultiClientEchoServer server;
    g_server = &server;
    
    // 注册信号处理器
    signal(SIGINT, signal_handler);
    
    server.start(8080);
    return 0;
}
```

### 测试多客户端

```bash
# 编译
g++ -o version2_echo version2_multi_client.cpp

# 运行服务器
./version2_echo

# 在多个终端同时连接测试
# 终端1
telnet localhost 8080

# 终端2  
telnet localhost 8080

# 终端3
echo "Hello from client 3" | nc localhost 8080
```

### 新特性说明

**1. 多进程处理**
```cpp
pid_t pid = fork();
if (pid == 0) {
    // 子进程处理客户端
    handle_client(client_fd, client_ip, client_port);
    exit(0);
} else {
    // 父进程继续接受新连接
    close(client_fd);
}
```

**2. 信号处理**
```cpp
signal(SIGINT, signal_handler);   // 处理Ctrl+C
signal(SIGPIPE, SIG_IGN);        // 忽略broken pipe
```

**3. 更好的日志输出**
```cpp
std::cout << "[" << client_ip << ":" << client_port << "] 状态信息" << std::endl;
```

---

## 🚀 版本3：非阻塞I/O与select多路复用

### 问题分析

版本2的问题：
- 创建进程开销大
- 进程数量有限制
- 资源管理复杂

### 解决方案：I/O多路复用

```cpp
// 创建文件：version3_select_based.cpp
#include <iostream>
#include <string>
#include <cstring>
#include <vector>
#include <map>
#include <algorithm>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/select.h>
#include <errno.h>

class SelectEchoServer {
public:
    void start(int port) {
        std::cout << "=== 版本3：Select多路复用Echo服务器 ===" << std::endl;
        
        if (!initialize(port)) {
            return;
        }
        
        std::cout << "服务器启动成功，使用select进行I/O多路复用" << std::endl;
        
        // 主事件循环
        run();
        
        cleanup();
    }
    
private:
    int listen_fd_;
    std::vector<int> client_fds_;
    std::map<int, std::string> client_info_;  // fd -> "ip:port"
    
    bool initialize(int port) {
        // 创建监听socket
        listen_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd_ < 0) {
            std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        // 【设置为非阻塞模式】
        // 主人，这是关键改进！非阻塞模式让程序不会"卡住"等待
        set_nonblocking(listen_fd_);
        
        // 设置地址复用
        int opt = 1;
        setsockopt(listen_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 绑定地址
        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = INADDR_ANY;
        addr.sin_port = htons(port);
        
        if (bind(listen_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            std::cerr << "绑定失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        if (listen(listen_fd_, 128) < 0) {
            std::cerr << "监听失败: " << strerror(errno) << std::endl;
            return false;
        }
        
        std::cout << "✓ 监听端口 " << port << " (非阻塞模式)" << std::endl;
        return true;
    }
    
    void set_nonblocking(int fd) {
        // 【非阻塞设置详解】
        // fcntl()是文件控制函数，可以修改文件描述符的属性
        // F_GETFL: 获取当前的文件状态标志
        int flags = fcntl(fd, F_GETFL, 0);
        
        // F_SETFL: 设置文件状态标志
        // O_NONBLOCK: 非阻塞标志
        // 使用按位或(|)操作添加非阻塞标志，保留其他标志
        fcntl(fd, F_SETFL, flags | O_NONBLOCK);
        
        // 【阻塞 vs 非阻塞的区别】
        // 阻塞模式：read()会等到有数据才返回，accept()会等到有连接才返回
        // 非阻塞模式：立即返回，没有数据就返回-1并设置errno为EAGAIN
    }
    
    void run() {
        while (true) {
            fd_set read_fds;
            int max_fd = prepare_select(read_fds);
            
            // 设置超时时间（1秒）
            struct timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            // 【select系统调用详解】
            // select()是I/O多路复用的核心函数
            // 参数说明：
            // - max_fd + 1: 要监控的最大fd值+1（历史原因）
            // - &read_fds: 监控读事件的fd集合
            // - nullptr: 监控写事件的fd集合（我们不需要）
            // - nullptr: 监控异常事件的fd集合（我们不需要）
            // - &timeout: 超时时间，避免无限等待
            // 返回值：就绪的fd数量，0表示超时，-1表示出错
            int ready = select(max_fd + 1, &read_fds, nullptr, nullptr, &timeout);
            
            if (ready < 0) {
                if (errno == EINTR) continue;  // 被信号中断，继续循环
                std::cerr << "select失败: " << strerror(errno) << std::endl;
                break;
            }
            
            if (ready == 0) {
                // 超时了，没有fd就绪，可以做一些定期任务
                print_statistics();
                continue;
            }
            
            // 处理就绪的文件描述符
            handle_ready_fds(read_fds);
        }
    }
    
    int prepare_select(fd_set& read_fds) {
        // 【准备select监控的文件描述符集合】
        // fd_set是一个位图，每一位代表一个文件描述符
        // FD_ZERO清空所有位，相当于重置
        FD_ZERO(&read_fds);
        
        // 【添加监听socket】
        // FD_SET将指定的fd添加到集合中（设置对应的位为1）
        FD_SET(listen_fd_, &read_fds);
        int max_fd = listen_fd_;
        
        // 【添加所有客户端socket】
        // 遍历所有已连接的客户端，都加入监控
        for (int client_fd : client_fds_) {
            FD_SET(client_fd, &read_fds);
            // select需要知道最大的fd值，所以要记录
            max_fd = std::max(max_fd, client_fd);
        }
        
        // 返回最大fd值，select函数需要这个参数
        return max_fd;
    }
    
    void handle_ready_fds(const fd_set& read_fds) {
        // 检查监听socket
        if (FD_ISSET(listen_fd_, &read_fds)) {
            handle_new_connection();
        }
        
        // 检查客户端socket（从后往前遍历，便于删除）
        for (auto it = client_fds_.rbegin(); it != client_fds_.rend(); ) {
            int client_fd = *it;
            
            if (FD_ISSET(client_fd, &read_fds)) {
                if (!handle_client_data(client_fd)) {
                    // 客户端断开，移除
                    close_client(client_fd);
                    // 注意：reverse_iterator的erase需要转换
                    client_fds_.erase(std::next(it).base());
                    ++it;  // 继续下一个
                } else {
                    ++it;
                }
            } else {
                ++it;
            }
        }
    }
    
    void handle_new_connection() {
        while (true) {  // 非阻塞模式下可能有多个连接等待
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(listen_fd_, (struct sockaddr*)&client_addr, &client_len);
            
            if (client_fd < 0) {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    // 没有更多连接等待
                    break;
                } else {
                    std::cerr << "accept失败: " << strerror(errno) << std::endl;
                    break;
                }
            }
            
            // 设置客户端socket为非阻塞
            set_nonblocking(client_fd);
            
            // 保存客户端信息
            char client_ip[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
            uint16_t client_port = ntohs(client_addr.sin_port);
            
            client_fds_.push_back(client_fd);
            client_info_[client_fd] = std::string(client_ip) + ":" + std::to_string(client_port);
            
            std::cout << "✓ 新连接: " << client_info_[client_fd] 
                      << " (fd=" << client_fd << "), 当前连接数: " << client_fds_.size() << std::endl;
        }
    }
    
    bool handle_client_data(int client_fd) {
        char buffer[1024];
        
        while (true) {  // 非阻塞模式下需要读取所有可用数据
            ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
            
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                
                // 构造回应消息
                std::string response = "[Echo from " + client_info_[client_fd] + "] " + std::string(buffer);
                
                // 发送回应
                ssize_t bytes_written = write(client_fd, response.c_str(), response.length());
                if (bytes_written < 0) {
                    if (errno != EAGAIN && errno != EWOULDBLOCK) {
                        std::cerr << "[" << client_info_[client_fd] << "] 发送失败: " 
                                  << strerror(errno) << std::endl;
                        return false;
                    }
                } else {
                    std::cout << "[" << client_info_[client_fd] << "] " 
                              << "处理 " << bytes_read << " 字节" << std::endl;
                }
                
            } else if (bytes_read == 0) {
                // 客户端正常断开
                std::cout << "[" << client_info_[client_fd] << "] 客户端断开" << std::endl;
                return false;
                
            } else {
                // 读取出错或没有更多数据
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    // 没有更多数据可读
                    break;
                } else {
                    std::cerr << "[" << client_info_[client_fd] << "] 读取失败: " 
                              << strerror(errno) << std::endl;
                    return false;
                }
            }
        }
        
        return true;  // 连接仍然有效
    }
    
    void close_client(int client_fd) {
        close(client_fd);
        client_info_.erase(client_fd);
        std::cout << "✓ 连接已关闭，当前连接数: " << client_fds_.size() - 1 << std::endl;
    }
    
    void print_statistics() {
        static int count = 0;
        if (++count % 10 == 0) {  // 每10秒打印一次
            std::cout << "📊 统计信息: 当前连接数 " << client_fds_.size() << std::endl;
        }
    }
    
    void cleanup() {
        for (int client_fd : client_fds_) {
            close(client_fd);
        }
        
        if (listen_fd_ >= 0) {
            close(listen_fd_);
        }
        
        std::cout << "✓ 服务器已关闭" << std::endl;
    }
};

int main() {
    SelectEchoServer server;
    server.start(8080);
    return 0;
}
```

### 版本3的关键改进

**1. 非阻塞I/O设置**
```cpp
void set_nonblocking(int fd) {
    int flags = fcntl(fd, F_GETFL, 0);
    fcntl(fd, F_SETFL, flags | O_NONBLOCK);
}
```

**2. select多路复用**
```cpp
fd_set read_fds;
FD_ZERO(&read_fds);
FD_SET(listen_fd_, &read_fds);  // 监听新连接
for (int client_fd : client_fds_) {
    FD_SET(client_fd, &read_fds);  // 监听客户端数据
}

int ready = select(max_fd + 1, &read_fds, nullptr, nullptr, &timeout);
```

**3. 状态管理**
```cpp
std::vector<int> client_fds_;                    // 客户端fd列表
std::map<int, std::string> client_info_;         // fd到客户端信息的映射
```

---

## 🎯 版本4：面向对象封装版本

现在我们将前面的代码重构为面向对象的设计，这为后续的WebServer开发打下基础。

```cpp
// 创建文件：version4_oop_server.cpp
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/epoll.h>

// 【Socket地址封装类】
// 主人，这个类把复杂的sockaddr_in结构体封装起来，使用更简单！
class InetAddress {
public:
    // 【构造函数：用IP和端口创建地址对象】
    InetAddress(const std::string& ip, uint16_t port) {
        // 清零结构体，避免垃圾数据
        memset(&addr_, 0, sizeof(addr_));
        addr_.sin_family = AF_INET;
        addr_.sin_port = htons(port);  // 自动转换字节序
        
        // 【智能IP地址处理】
        if (ip == "0.0.0.0" || ip.empty()) {
            // 空IP或0.0.0.0表示监听所有网卡
            addr_.sin_addr.s_addr = INADDR_ANY;
        } else {
            // inet_pton()将字符串IP转换为二进制格式
            // 比inet_addr()更安全，支持IPv6
            inet_pton(AF_INET, ip.c_str(), &addr_.sin_addr);
        }
    }
    
    InetAddress(const struct sockaddr_in& addr) : addr_(addr) {}
    
    std::string to_string() const {
        char ip[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &addr_.sin_addr, ip, INET_ADDRSTRLEN);
        return std::string(ip) + ":" + std::to_string(ntohs(addr_.sin_port));
    }
    
    const struct sockaddr_in& get_sockaddr() const { return addr_; }
    struct sockaddr_in& get_sockaddr() { return addr_; }
    
private:
    struct sockaddr_in addr_;
};

// 【Socket操作封装类】
// 主人，这个类使用RAII技术，自动管理socket的生命周期！
class Socket {
public:
    // 【默认构造函数：创建新socket】
    Socket() : fd_(socket(AF_INET, SOCK_STREAM, 0)) {
        if (fd_ < 0) {
            // 使用异常处理，比返回错误码更安全
            throw std::runtime_error("Failed to create socket: " + std::string(strerror(errno)));
        }
    }
    
    // 【显式构造函数：接管已有的socket】
    // explicit防止隐式转换，避免意外的类型转换
    explicit Socket(int fd) : fd_(fd) {}
    
    // 【析构函数：自动关闭socket】
    // RAII原则：资源获取即初始化，析构时自动释放
    ~Socket() {
        if (fd_ >= 0) {
            close(fd_);
        }
    }
    
    // 【禁止拷贝，允许移动】
    // 主人，这很重要！socket不能被复制（一个fd不能属于两个对象）
    // 但可以移动（转移所有权）
    Socket(const Socket&) = delete;              // 禁止拷贝构造
    Socket& operator=(const Socket&) = delete;   // 禁止拷贝赋值
    
    // 【移动构造函数】
    // noexcept承诺不抛异常，提高性能
    Socket(Socket&& other) noexcept : fd_(other.fd_) {
        other.fd_ = -1;  // 将原对象的fd设为无效，转移所有权
    }
    
    // 【移动赋值运算符】
    Socket& operator=(Socket&& other) noexcept {
        if (this != &other) {  // 防止自赋值
            if (fd_ >= 0) close(fd_);  // 先关闭当前socket
            fd_ = other.fd_;           // 接管新socket
            other.fd_ = -1;            // 原对象失去所有权
        }
        return *this;
    }
    
    void set_reuse_addr() {
        int opt = 1;
        if (setsockopt(fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
            throw std::runtime_error("Failed to set SO_REUSEADDR: " + std::string(strerror(errno)));
        }
    }
    
    void set_nonblocking() {
        int flags = fcntl(fd_, F_GETFL, 0);
        if (flags == -1 || fcntl(fd_, F_SETFL, flags | O_NONBLOCK) == -1) {
            throw std::runtime_error("Failed to set nonblocking: " + std::string(strerror(errno)));
        }
    }
    
    void bind(const InetAddress& addr) {
        if (::bind(fd_, (struct sockaddr*)&addr.get_sockaddr(), sizeof(struct sockaddr_in)) < 0) {
            throw std::runtime_error("Failed to bind: " + std::string(strerror(errno)));
        }
    }
    
    void listen(int backlog = 128) {
        if (::listen(fd_, backlog) < 0) {
            throw std::runtime_error("Failed to listen: " + std::string(strerror(errno)));
        }
    }
    
    std::unique_ptr<Socket> accept(InetAddress& client_addr) {
        struct sockaddr_in addr;
        socklen_t len = sizeof(addr);
        
        int client_fd = ::accept(fd_, (struct sockaddr*)&addr, &len);
        if (client_fd < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                return nullptr;  // 非阻塞模式下没有连接
            }
            throw std::runtime_error("Failed to accept: " + std::string(strerror(errno)));
        }
        
        client_addr = InetAddress(addr);
        return std::make_unique<Socket>(client_fd);
    }
    
    ssize_t read(void* buffer, size_t size) {
        return ::read(fd_, buffer, size);
    }
    
    ssize_t write(const void* buffer, size_t size) {
        return ::write(fd_, buffer, size);
    }
    
    int get_fd() const { return fd_; }
    
private:
    int fd_;
};

// Epoll封装类
class Epoller {
public:
    explicit Epoller(int max_events = 1024) : max_events_(max_events) {
        epoll_fd_ = epoll_create1(EPOLL_CLOEXEC);
        if (epoll_fd_ < 0) {
            throw std::runtime_error("Failed to create epoll: " + std::string(strerror(errno)));
        }
        
        events_.resize(max_events);
    }
    
    ~Epoller() {
        if (epoll_fd_ >= 0) {
            close(epoll_fd_);
        }
    }
    
    void add_fd(int fd, uint32_t events) {
        struct epoll_event event;
        event.events = events;
        event.data.fd = fd;
        
        if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, fd, &event) < 0) {
            throw std::runtime_error("Failed to add fd to epoll: " + std::string(strerror(errno)));
        }
    }
    
    void modify_fd(int fd, uint32_t events) {
        struct epoll_event event;
        event.events = events;
        event.data.fd = fd;
        
        if (epoll_ctl(epoll_fd_, EPOLL_CTL_MOD, fd, &event) < 0) {
            throw std::runtime_error("Failed to modify fd in epoll: " + std::string(strerror(errno)));
        }
    }
    
    void delete_fd(int fd) {
        if (epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, fd, nullptr) < 0) {
            // 不抛异常，因为fd可能已经关闭
            std::cerr << "Warning: Failed to delete fd from epoll: " << strerror(errno) << std::endl;
        }
    }
    
    int wait(int timeout_ms = -1) {
        return epoll_wait(epoll_fd_, events_.data(), max_events_, timeout_ms);
    }
    
    int get_event_fd(int i) const {
        return events_[i].data.fd;
    }
    
    uint32_t get_events(int i) const {
        return events_[i].events;
    }
    
private:
    int epoll_fd_;
    int max_events_;
    std::vector<struct epoll_event> events_;
};

// 连接管理类
class Connection {
public:
    Connection(std::unique_ptr<Socket> socket, const InetAddress& addr)
        : socket_(std::move(socket)), client_addr_(addr) {}
    
    bool handle_read() {
        char buffer[1024];
        
        while (true) {
            ssize_t bytes_read = socket_->read(buffer, sizeof(buffer) - 1);
            
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                
                // 简单回显
                std::string response = "[OOP Echo from " + client_addr_.to_string() + "] " + std::string(buffer);
                
                ssize_t bytes_written = socket_->write(response.c_str(), response.length());
                if (bytes_written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                    std::cerr << "Write failed: " << strerror(errno) << std::endl;
                    return false;
                }
                
                std::cout << "[" << client_addr_.to_string() << "] Processed " 
                          << bytes_read << " bytes" << std::endl;
                
            } else if (bytes_read == 0) {
                std::cout << "[" << client_addr_.to_string() << "] Client disconnected" << std::endl;
                return false;
                
            } else {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    break;  // 没有更多数据
                } else {
                    std::cerr << "Read failed: " << strerror(errno) << std::endl;
                    return false;
                }
            }
        }
        
        return true;
    }
    
    int get_fd() const {
        return socket_->get_fd();
    }
    
    const InetAddress& get_address() const {
        return client_addr_;
    }
    
private:
    std::unique_ptr<Socket> socket_;
    InetAddress client_addr_;
};

// 主服务器类
class OOPEchoServer {
public:
    OOPEchoServer(const std::string& host, uint16_t port) 
        : listen_addr_(host, port), epoller_(1024) {}
    
    void start() {
        std::cout << "=== 版本4：面向对象Echo服务器 ===" << std::endl;
        
        // 初始化监听socket
        listen_socket_ = std::make_unique<Socket>();
        listen_socket_->set_reuse_addr();
        listen_socket_->set_nonblocking();
        listen_socket_->bind(listen_addr_);
        listen_socket_->listen();
        
        // 添加到epoll监控
        epoller_.add_fd(listen_socket_->get_fd(), EPOLLIN);
        
        std::cout << "🚀 服务器启动成功: " << listen_addr_.to_string() << std::endl;
        std::cout << "使用面向对象设计 + epoll + 非阻塞I/O" << std::endl;
        
        // 主事件循环
        while (true) {
            int ready = epoller_.wait(1000);  // 1秒超时
            
            if (ready < 0) {
                if (errno == EINTR) continue;
                std::cerr << "Epoll wait failed: " << strerror(errno) << std::endl;
                break;
            }
            
            if (ready == 0) {
                print_statistics();
                continue;
            }
            
            // 处理所有就绪事件
            for (int i = 0; i < ready; ++i) {
                handle_event(epoller_.get_event_fd(i), epoller_.get_events(i));
            }
        }
    }
    
private:
    void handle_event(int fd, uint32_t events) {
        if (fd == listen_socket_->get_fd()) {
            // 新连接
            handle_new_connections();
        } else {
            // 客户端事件
            auto it = connections_.find(fd);
            if (it != connections_.end()) {
                if (events & EPOLLIN) {
                    if (!it->second->handle_read()) {
                        close_connection(fd);
                    }
                }
                
                if (events & (EPOLLHUP | EPOLLERR)) {
                    close_connection(fd);
                }
            }
        }
    }
    
    void handle_new_connections() {
        while (true) {
            InetAddress client_addr("", 0);
            auto client_socket = listen_socket_->accept(client_addr);
            
            if (!client_socket) {
                break;  // 没有更多连接
            }
            
            // 设置客户端socket为非阻塞
            client_socket->set_nonblocking();
            
            int client_fd = client_socket->get_fd();
            
            // 添加到epoll监控
            epoller_.add_fd(client_fd, EPOLLIN | EPOLLET);
            
            // 创建连接对象
            auto connection = std::make_unique<Connection>(std::move(client_socket), client_addr);
            connections_[client_fd] = std::move(connection);
            
            std::cout << "✓ 新连接: " << client_addr.to_string() 
                      << " (fd=" << client_fd << "), 总连接数: " << connections_.size() << std::endl;
        }
    }
    
    void close_connection(int fd) {
        auto it = connections_.find(fd);
        if (it != connections_.end()) {
            std::cout << "关闭连接: " << it->second->get_address().to_string() << std::endl;
            epoller_.delete_fd(fd);
            connections_.erase(it);
        }
    }
    
    void print_statistics() {
        static int count = 0;
        if (++count % 10 == 0) {
            std::cout << "📊 当前连接数: " << connections_.size() << std::endl;
        }
    }
    
    InetAddress listen_addr_;
    std::unique_ptr<Socket> listen_socket_;
    Epoller epoller_;
    std::unordered_map<int, std::unique_ptr<Connection>> connections_;
};

int main() {
    try {
        OOPEchoServer server("0.0.0.0", 8080);
        server.start();
    } catch (const std::exception& e) {
        std::cerr << "服务器异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
```

---

## 🛡️ 版本5：异常安全和完整错误处理版本

最后一个版本，我们加入完整的异常处理、资源管理和优雅关闭机制。

```cpp
// 创建文件：version5_robust_server.cpp
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <csignal>
#include <atomic>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/epoll.h>

// 【全局原子变量，用于优雅关闭】
// 主人，atomic<bool>是线程安全的布尔变量，多线程环境下也安全
// 用于在信号处理器和主线程之间安全地传递关闭信号
std::atomic<bool> g_running{true};

// 【信号处理函数】
// 当用户按Ctrl+C或系统发送终止信号时调用
void signal_handler(int sig) {
    if (sig == SIGINT || sig == SIGTERM) {
        // SIGINT: Ctrl+C产生的中断信号
        // SIGTERM: 系统发送的终止信号
        std::cout << "\n接收到信号 " << sig << "，准备优雅关闭..." << std::endl;
        g_running = false;  // 原子操作，安全地设置关闭标志
    }
}

// 【RAII风格的文件描述符管理】
// 主人，这是一个通用的fd管理类，比Socket类更基础
// RAII = Resource Acquisition Is Initialization（资源获取即初始化）
class FileDescriptor {
public:
    // 【默认构造：无效fd】
    FileDescriptor() : fd_(-1) {}
    
    // 【接管已有fd】
    explicit FileDescriptor(int fd) : fd_(fd) {}
    
    // 【析构函数：自动关闭fd】
    // 无论何时对象销毁，都会自动关闭文件描述符，防止资源泄漏
    ~FileDescriptor() {
        close();
    }
    
    // 【禁止拷贝，允许移动】
    // 文件描述符是独占资源，不能被多个对象同时拥有
    FileDescriptor(const FileDescriptor&) = delete;
    FileDescriptor& operator=(const FileDescriptor&) = delete;
    
    FileDescriptor(FileDescriptor&& other) noexcept : fd_(other.fd_) {
        other.fd_ = -1;
    }
    
    FileDescriptor& operator=(FileDescriptor&& other) noexcept {
        if (this != &other) {
            close();
            fd_ = other.fd_;
            other.fd_ = -1;
        }
        return *this;
    }
    
    void reset(int fd = -1) {
        close();
        fd_ = fd;
    }
    
    int get() const { return fd_; }
    int release() {
        int fd = fd_;
        fd_ = -1;
        return fd;
    }
    
    bool valid() const { return fd_ >= 0; }
    
private:
    void close() {
        if (fd_ >= 0) {
            ::close(fd_);
            fd_ = -1;
        }
    }
    
    int fd_;
};

// 异常安全的Socket类
class RobustSocket {
public:
    RobustSocket() {
        int fd = socket(AF_INET, SOCK_STREAM, 0);
        if (fd < 0) {
            throw std::system_error(errno, std::system_category(), "Failed to create socket");
        }
        fd_.reset(fd);
    }
    
    explicit RobustSocket(int fd) : fd_(fd) {
        if (!fd_.valid()) {
            throw std::invalid_argument("Invalid file descriptor");
        }
    }
    
    void set_reuse_addr() {
        int opt = 1;
        if (setsockopt(fd_.get(), SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
            throw std::system_error(errno, std::system_category(), "Failed to set SO_REUSEADDR");
        }
    }
    
    void set_nonblocking() {
        int flags = fcntl(fd_.get(), F_GETFL, 0);
        if (flags == -1) {
            throw std::system_error(errno, std::system_category(), "Failed to get file flags");
        }
        
        if (fcntl(fd_.get(), F_SETFL, flags | O_NONBLOCK) == -1) {
            throw std::system_error(errno, std::system_category(), "Failed to set nonblocking");
        }
    }
    
    void bind(const std::string& ip, uint16_t port) {
        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_port = htons(port);
        
        if (ip.empty() || ip == "0.0.0.0") {
            addr.sin_addr.s_addr = INADDR_ANY;
        } else {
            if (inet_pton(AF_INET, ip.c_str(), &addr.sin_addr) <= 0) {
                throw std::runtime_error("Invalid IP address: " + ip);
            }
        }
        
        if (::bind(fd_.get(), (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            throw std::system_error(errno, std::system_category(), 
                                  "Failed to bind to " + ip + ":" + std::to_string(port));
        }
    }
    
    void listen(int backlog = 128) {
        if (::listen(fd_.get(), backlog) < 0) {
            throw std::system_error(errno, std::system_category(), "Failed to listen");
        }
    }
    
    std::pair<std::unique_ptr<RobustSocket>, std::string> accept() {
        struct sockaddr_in client_addr;
        socklen_t len = sizeof(client_addr);
        
        int client_fd = ::accept(fd_.get(), (struct sockaddr*)&client_addr, &len);
        if (client_fd < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                return {nullptr, ""};  // 非阻塞模式下没有连接
            }
            throw std::system_error(errno, std::system_category(), "Failed to accept connection");
        }
        
        // 构造客户端地址字符串
        char ip[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &client_addr.sin_addr, ip, INET_ADDRSTRLEN);
        std::string client_info = std::string(ip) + ":" + std::to_string(ntohs(client_addr.sin_port));
        
        return {std::make_unique<RobustSocket>(client_fd), client_info};
    }
    
    ssize_t read(void* buffer, size_t size) {
        return ::read(fd_.get(), buffer, size);
    }
    
    ssize_t write(const void* buffer, size_t size) {
        return ::write(fd_.get(), buffer, size);
    }
    
    int get_fd() const { return fd_.get(); }
    
private:
    FileDescriptor fd_;
};

// 异常安全的Epoll类
class RobustEpoller {
public:
    explicit RobustEpoller(int max_events = 1024) : max_events_(max_events) {
        int epoll_fd = epoll_create1(EPOLL_CLOEXEC);
        if (epoll_fd < 0) {
            throw std::system_error(errno, std::system_category(), "Failed to create epoll");
        }
        epoll_fd_.reset(epoll_fd);
        events_.resize(max_events);
    }
    
    void add_fd(int fd, uint32_t events) {
        struct epoll_event event;
        event.events = events;
        event.data.fd = fd;
        
        if (epoll_ctl(epoll_fd_.get(), EPOLL_CTL_ADD, fd, &event) < 0) {
            throw std::system_error(errno, std::system_category(), 
                                  "Failed to add fd " + std::to_string(fd) + " to epoll");
        }
    }
    
    void delete_fd(int fd) noexcept {
        // 不抛异常的版本，用于析构函数等场景
        if (epoll_ctl(epoll_fd_.get(), EPOLL_CTL_DEL, fd, nullptr) < 0) {
            std::cerr << "Warning: Failed to delete fd " << fd << " from epoll: " 
                      << strerror(errno) << std::endl;
        }
    }
    
    int wait(int timeout_ms = -1) {
        int ready = epoll_wait(epoll_fd_.get(), events_.data(), max_events_, timeout_ms);
        if (ready < 0 && errno != EINTR) {
            throw std::system_error(errno, std::system_category(), "epoll_wait failed");
        }
        return ready;
    }
    
    int get_event_fd(int i) const { return events_[i].data.fd; }
    uint32_t get_events(int i) const { return events_[i].events; }
    
private:
    FileDescriptor epoll_fd_;
    int max_events_;
    std::vector<struct epoll_event> events_;
};

// 健壮的连接类
class RobustConnection {
public:
    RobustConnection(std::unique_ptr<RobustSocket> socket, const std::string& addr)
        : socket_(std::move(socket)), client_addr_(addr), bytes_processed_(0) {}
    
    bool handle_read() {
        char buffer[1024];
        bool has_data = false;
        
        try {
            while (true) {
                ssize_t bytes_read = socket_->read(buffer, sizeof(buffer) - 1);
                
                if (bytes_read > 0) {
                    has_data = true;
                    buffer[bytes_read] = '\0';
                    bytes_processed_ += bytes_read;
                    
                    // 构造响应
                    std::string response = "[Robust Echo] " + std::string(buffer);
                    
                    // 发送响应
                    ssize_t bytes_written = socket_->write(response.c_str(), response.length());
                    if (bytes_written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                        std::cerr << "[" << client_addr_ << "] Write failed: " << strerror(errno) << std::endl;
                        return false;
                    }
                    
                } else if (bytes_read == 0) {
                    std::cout << "[" << client_addr_ << "] Client disconnected (processed " 
                              << bytes_processed_ << " bytes total)" << std::endl;
                    return false;
                    
                } else {
                    if (errno == EAGAIN || errno == EWOULDBLOCK) {
                        break;  // 没有更多数据
                    } else {
                        std::cerr << "[" << client_addr_ << "] Read failed: " << strerror(errno) << std::endl;
                        return false;
                    }
                }
            }
            
            if (has_data) {
                std::cout << "[" << client_addr_ << "] Processed data (total: " 
                          << bytes_processed_ << " bytes)" << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cerr << "[" << client_addr_ << "] Exception in handle_read: " << e.what() << std::endl;
            return false;
        }
        
        return true;
    }
    
    int get_fd() const { return socket_->get_fd(); }
    const std::string& get_address() const { return client_addr_; }
    
private:
    std::unique_ptr<RobustSocket> socket_;
    std::string client_addr_;
    size_t bytes_processed_;
};

// 主服务器类
class RobustEchoServer {
public:
    RobustEchoServer(const std::string& host, uint16_t port) 
        : host_(host), port_(port), epoller_(1024) {}
    
    void start() {
        std::cout << "=== 版本5：异常安全Echo服务器 ===" << std::endl;
        
        try {
            // 注册信号处理器
            std::signal(SIGINT, signal_handler);
            std::signal(SIGTERM, signal_handler);
            std::signal(SIGPIPE, SIG_IGN);  // 忽略SIGPIPE
            
            // 初始化监听socket
            initialize_server();
            
            std::cout << "🚀 服务器启动成功: " << host_ << ":" << port_ << std::endl;
            std::cout << "特性: 异常安全 + RAII + 优雅关闭 + 完整错误处理" << std::endl;
            
            // 主事件循环
            run_event_loop();
            
        } catch (const std::exception& e) {
            std::cerr << "服务器启动失败: " << e.what() << std::endl;
            throw;
        }
        
        std::cout << "服务器已优雅关闭" << std::endl;
    }
    
private:
    void initialize_server() {
        listen_socket_ = std::make_unique<RobustSocket>();
        listen_socket_->set_reuse_addr();
        listen_socket_->set_nonblocking();
        listen_socket_->bind(host_, port_);
        listen_socket_->listen();
        
        // 添加到epoll监控
        epoller_.add_fd(listen_socket_->get_fd(), EPOLLIN);
    }
    
    void run_event_loop() {
        while (g_running) {
            try {
                int ready = epoller_.wait(1000);  // 1秒超时
                
                if (ready < 0) {
                    if (errno == EINTR) continue;  // 被信号中断
                    throw std::system_error(errno, std::system_category(), "epoll_wait failed");
                }
                
                if (ready == 0) {
                    print_statistics();
                    continue;
                }
                
                // 处理所有就绪事件
                for (int i = 0; i < ready; ++i) {
                    handle_event(epoller_.get_event_fd(i), epoller_.get_events(i));
                }
                
            } catch (const std::exception& e) {
                std::cerr << "事件循环异常: " << e.what() << std::endl;
                // 继续运行，不因单个异常退出
            }
        }
        
        // 优雅关闭
        shutdown_server();
    }
    
    void handle_event(int fd, uint32_t events) {
        try {
            if (fd == listen_socket_->get_fd()) {
                handle_new_connections();
            } else {
                auto it = connections_.find(fd);
                if (it != connections_.end()) {
                    if (events & EPOLLIN) {
                        if (!it->second->handle_read()) {
                            close_connection(fd);
                        }
                    }
                    
                    if (events & (EPOLLHUP | EPOLLERR)) {
                        std::cout << "连接异常，关闭连接: " << it->second->get_address() << std::endl;
                        close_connection(fd);
                    }
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "处理事件异常 (fd=" << fd << "): " << e.what() << std::endl;
            if (fd != listen_socket_->get_fd()) {
                close_connection(fd);
            }
        }
    }
    
    void handle_new_connections() {
        int accepted = 0;
        
        while (g_running && accepted < 100) {  // 限制单次accept数量，避免饥饿
            try {
                auto [client_socket, client_addr] = listen_socket_->accept();
                
                if (!client_socket) {
                    break;  // 没有更多连接
                }
                
                // 设置客户端socket为非阻塞
                client_socket->set_nonblocking();
                
                int client_fd = client_socket->get_fd();
                
                // 添加到epoll监控
                epoller_.add_fd(client_fd, EPOLLIN | EPOLLET);
                
                // 创建连接对象
                auto connection = std::make_unique<RobustConnection>(std::move(client_socket), client_addr);
                connections_[client_fd] = std::move(connection);
                
                std::cout << "✓ 新连接: " << client_addr 
                          << " (fd=" << client_fd << "), 总连接数: " << connections_.size() << std::endl;
                
                ++accepted;
                
            } catch (const std::exception& e) {
                std::cerr << "接受新连接异常: " << e.what() << std::endl;
                break;
            }
        }
    }
    
    void close_connection(int fd) noexcept {
        try {
            auto it = connections_.find(fd);
            if (it != connections_.end()) {
                std::cout << "关闭连接: " << it->second->get_address() << std::endl;
                epoller_.delete_fd(fd);  // noexcept版本
                connections_.erase(it);
            }
        } catch (...) {
            std::cerr << "关闭连接时发生异常 (fd=" << fd << ")" << std::endl;
        }
    }
    
    void print_statistics() {
        static int count = 0;
        if (++count % 10 == 0) {
            std::cout << "📊 运行状态 - 连接数: " << connections_.size() 
                      << ", 运行时间: " << count * 10 << "秒" << std::endl;
        }
    }
    
    void shutdown_server() {
        std::cout << "开始关闭服务器..." << std::endl;
        
        // 关闭所有客户端连接
        std::cout << "关闭 " << connections_.size() << " 个客户端连接..." << std::endl;
        connections_.clear();  // RAII自动关闭所有连接
        
        // 监听socket会在析构时自动关闭
        std::cout << "服务器关闭完成" << std::endl;
    }
    
    std::string host_;
    uint16_t port_;
    std::unique_ptr<RobustSocket> listen_socket_;
    RobustEpoller epoller_;
    std::unordered_map<int, std::unique_ptr<RobustConnection>> connections_;
};

int main() {
    try {
        RobustEchoServer server("0.0.0.0", 8080);
        server.start();
    } catch (const std::exception& e) {
        std::cerr << "程序异常退出: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
```

---

## 📊 性能对比测试

创建测试脚本来对比三个版本的性能：

```bash
#!/bin/bash
# 创建文件：test_performance.sh

echo "=== Echo服务器性能测试 ==="

# 测试函数
test_server() {
    local version=$1
    local server_cmd=$2
    
    echo "测试 $version..."
    
    # 启动服务器
    $server_cmd &
    SERVER_PID=$!
    
    sleep 2  # 等待服务器启动
    
    # 使用wrk或ab进行压力测试
    if command -v wrk >/dev/null 2>&1; then
        wrk -t4 -c100 -d10s --script=echo_test.lua http://localhost:8080/
    else
        # 使用netcat进行简单测试
        for i in {1..100}; do
            echo "Test message $i" | nc localhost 8080 &
        done
        wait
    fi
    
    # 关闭服务器
    kill $SERVER_PID
    wait $SERVER_PID 2>/dev/null
    
    echo "$version 测试完成"
    echo "---"
}

# 编译所有版本
g++ -o version1_echo version1_simple_echo.cpp
g++ -o version2_echo version2_multi_client.cpp  
g++ -o version3_echo version3_select_based.cpp

# 测试各版本
test_server "版本1" "./version1_echo"
test_server "版本2" "./version2_echo"
test_server "版本3" "./version3_echo"

echo "所有测试完成！"
```

---

## 📝 总结与思考

### 三个版本的特点对比

| 特性 | 版本1 | 版本2 | 版本3 |
|------|-------|-------|-------|
| 并发模型 | 单线程阻塞 | 多进程 | 单线程非阻塞 |
| 同时连接数 | 1 | 受进程数限制 | 受fd数限制 |
| 内存使用 | 最少 | 较多 | 中等 |
| CPU使用 | 低 | 高 | 中等 |
| 复杂度 | 简单 | 中等 | 较复杂 |
| 适用场景 | 学习/测试 | 中等并发 | 高并发 |

### 学到的核心概念

1. **Socket编程基础**：socket() → bind() → listen() → accept()
2. **网络字节序**：htons()、ntohs()、inet_ntop()
3. **错误处理**：errno、strerror()
4. **I/O模型**：阻塞 vs 非阻塞
5. **多路复用**：select()的使用
6. **进程管理**：fork()、信号处理

### 下一步规划

现在您已经掌握了网络编程的基础，接下来我们将：

1. **版本4**：面向对象封装，创建Socket、Address等类
2. **版本5**：加入智能指针和RAII机制
3. **WebServer重写**：将这些知识应用到完整的HTTP服务器

## 🎓 学习总结与进阶指导

### 核心概念掌握检查表

**主人，请确认您已经理解了这些重要概念：**

✅ **Socket编程基础**
- [ ] socket()、bind()、listen()、accept()的作用和参数
- [ ] 文件描述符(fd)的概念和管理
- [ ] 网络字节序转换（htons/ntohs）的必要性
- [ ] sockaddr_in结构体的各个字段含义

✅ **错误处理机制**
- [ ] errno全局变量的作用
- [ ] strerror()函数的使用
- [ ] 系统调用返回值的判断方法
- [ ] 资源清理的重要性（close fd）

✅ **I/O模型演进**
- [ ] 阻塞I/O的问题和限制
- [ ] 非阻塞I/O的优势和使用方法
- [ ] select多路复用的工作原理
- [ ] fd_set位图的操作（FD_ZERO、FD_SET、FD_ISSET）

✅ **面向对象设计**
- [ ] RAII资源管理模式
- [ ] 移动语义vs拷贝语义的区别
- [ ] 异常安全的代码设计
- [ ] 智能指针和自动资源管理

### 实践练习建议

**1. 基础练习（巩固版本1-2）**
```bash
# 编译运行版本1，用telnet测试
g++ -o echo1 version1_simple_echo.cpp
./echo1 &
telnet localhost 8080

# 尝试同时连接多个客户端，观察现象
```

**2. 进阶练习（理解版本3）**
```bash
# 使用工具观察select的行为
strace -e select ./version3_echo  # 跟踪select系统调用
lsof -p $(pgrep version3_echo)    # 查看进程打开的文件描述符
```

**3. 高级练习（掌握版本4-5）**
- 尝试修改代码，添加新功能（如连接数限制）
- 使用valgrind检查内存泄漏
- 用压力测试工具测试并发性能

### 常见问题解答

**Q1: 为什么需要设置SO_REUSEADDR？**
A: 防止"Address already in use"错误。TCP连接关闭后有TIME_WAIT状态，端口暂时不能重用。

**Q2: 什么时候使用阻塞vs非阻塞I/O？**
A: 
- 阻塞I/O：简单场景，一对一通信
- 非阻塞I/O：高并发场景，需要同时处理多个连接

**Q3: select和epoll有什么区别？**
A:
- select：跨平台，但有1024个fd限制，性能O(n)
- epoll：Linux专用，无fd限制，性能O(1)

**Q4: RAII是什么？为什么重要？**
A: Resource Acquisition Is Initialization，确保资源自动释放，防止内存/fd泄漏。

### 下一步学习路径

**🎯 短期目标（1-2周）**
1. 完全理解所有5个版本的代码
2. 能够独立编写简单的TCP服务器
3. 掌握基本的网络调试技巧

**🚀 中期目标（1个月）**
1. 学习HTTP协议基础
2. 实现简单的HTTP服务器
3. 理解WebServer项目的整体架构

**⭐ 长期目标（3个月）**
1. 完全掌握WebServer项目
2. 能够优化和扩展功能
3. 理解高性能服务器的设计原理

**主人，这样详细的注释和解释是否让代码更容易理解了？每个关键概念我都用【】标注并详细解释了原理和用途。如果还有任何不清楚的地方，请随时告诉我！** 