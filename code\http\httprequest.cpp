/*
 * <AUTHOR> mark
 * @Date         : 2020-06-26
 * @copyleft Apache 2.0
 * 
 * HttpRequest HTTP请求解析器实现
 * 
 * 这个文件实现了一个HTTP请求解析器，主要功能是：
 * 1. 解析客户端发送的HTTP请求
 * 2. 提取请求中的方法、路径、头部信息等
 * 3. 处理POST表单数据
 * 4. 实现用户登录注册功能
 * 
 * HTTP协议简介：
 * HTTP是网页浏览器和服务器之间通信的协议
 * 一个HTTP请求包含：
 * - 请求行：GET /index.html HTTP/1.1
 * - 头部：Host: www.example.com
 * - 请求体：表单数据等（POST请求才有）
 */ 

#include "httprequest.h"
using namespace std;

// ==================== 静态配置数据 ====================

// 这些是服务器支持的默认页面
// 比如用户访问 /index 会自动变成 /index.html
// 【DEFAULT_HTML是什么？】
// 这是一个静态的哈希集合，存储了服务器支持的默认页面路径
// 主人，我用生活例子来解释：就像餐厅的菜单一样！

// 【为什么要有这个列表？】
// 1. 简化用户访问：用户输入 /index 就能访问 /index.html
// 2. 统一管理：所有支持的页面都在这里定义
// 3. 快速查找：unordered_set提供O(1)的查找速度

// 【unordered_set是什么？】
// 这是C++ STL的哈希集合，特点是：
// - 不允许重复元素
// - 查找速度非常快（平均O(1)时间复杂度）
// - 无序存储（不按插入顺序）

// 【这些路径的含义】
const unordered_set<string> HttpRequest::DEFAULT_HTML{
    "/index",     // 首页 - 用户访问网站的主页面
    "/register",  // 注册页 - 新用户注册账号的页面  
    "/login",     // 登录页 - 用户登录的页面
    "/welcome",   // 欢迎页 - 登录成功后显示的页面
    "/video",     // 视频页 - 展示视频内容的页面
    "/picture"    // 图片页 - 展示图片内容的页面
};

// 【实际工作原理】
// 当用户访问 /index 时，程序会：
// 1. 在这个集合中查找 "/index"
// 2. 如果找到，自动添加 ".html" 后缀
// 3. 最终访问的是 "/index.html" 文件
// 这样用户就不用输入完整的文件名了！

// 特殊页面的标记，用于区分登录页面(1)和注册页面(0)
// 【DEFAULT_HTML_TAG是什么？】
// 这是一个静态的哈希映射表，用来给特殊页面打标签
// 主人，我用简单例子解释：就像给不同房间贴标签一样！

// 【为什么需要这个标签系统？】
// 1. 区分功能：登录页面和注册页面虽然都是HTML，但处理逻辑不同
// 2. 标识作用：当用户访问这些页面时，服务器知道该做什么
// 3. 简化判断：不用复杂的字符串比较，直接用数字标签

// 【unordered_map<string, int>是什么？】
// 这是C++ STL的哈希映射表，特点是：
// - 键值对存储：string(页面路径) -> int(标签数字)
// - 快速查找：通过页面路径快速找到对应标签
// - 无序存储：不按插入顺序排列

// 【具体的标签含义】
// 0 = 注册页面标签
// 1 = 登录页面标签
// 为什么用数字？因为数字比字符串比较更快！

// 【实际工作流程】
// 当用户访问 "/register.html" 时：
// 1. 程序在这个映射表中查找 "/register.html"
// 2. 找到对应的值是 0（注册标签）
// 3. 程序知道：这是注册页面，需要处理注册逻辑
// 
// 当用户访问 "/login.html" 时：
// 1. 程序查找 "/login.html"
// 2. 找到对应的值是 1（登录标签）  
// 3. 程序知道：这是登录页面，需要处理登录逻辑

const unordered_map<string, int> HttpRequest::DEFAULT_HTML_TAG {
    {"/register.html", 0}, // 注册页面标记为0
    {"/login.html", 1},    // 登录页面标记为1
};

// ==================== 基础接口 ====================

// 初始化解析器，清空所有数据，准备解析新的请求
void HttpRequest::Init() {
    method_ = path_ = version_ = body_ = "";  // 清空请求信息
    state_ = REQUEST_LINE;                   // 设置解析状态为开始
    header_.clear();                         // 清空头部信息
    post_.clear();                           // 清空POST数据
}

// 检查客户端是否要求保持连接（Keep-Alive）
// 
// 主人，让我用最简单的方式为您详细解释这个函数！
//
// 【什么是Keep-Alive？】
// Keep-Alive就像是一条电话线路的"保持通话"功能：
// - 没有Keep-Alive：每次HTTP请求都要重新"拨号"建立连接，用完就"挂断"
// - 有Keep-Alive：一次"拨号"后，可以进行多次对话，节省重复"拨号"的时间
//
// 【为什么要Keep-Alive？】
// 1. 节省时间：建立TCP连接需要"三次握手"，很耗时
// 2. 减少开销：频繁建立/关闭连接消耗系统资源
// 3. 提高性能：同一个连接可以发送多个请求，效率更高
//
// 【Keep-Alive的判断条件】
// 要支持Keep-Alive需要满足两个条件：
// 1. 客户端明确请求：Connection头部必须是"keep-alive"  
// 2. 协议版本支持：必须是HTTP/1.1（HTTP/1.0默认不支持）
//
// 【函数的详细工作流程】
bool HttpRequest::IsKeepAlive() const {
    // 步骤1：检查是否有Connection头部字段
    // header_.count("Connection") == 1 意思是：
    // - count()函数：统计"Connection"这个键在哈希表中出现的次数
    // - == 1：表示恰好出现1次（找到了这个头部字段）
    // - 如果是0：说明客户端没有发送Connection头部
    // - 如果大于1：理论上不可能，但防止异常情况
    if(header_.count("Connection") == 1) {
        
        // 步骤2：获取Connection头部的值并检查
        // header_.find("Connection")：在哈希表中查找"Connection"键
        // ->second：获取找到的键值对中的值部分
        // 
        // 举例说明：
        // 如果HTTP头部有：Connection: keep-alive
        // 那么：header_["Connection"] = "keep-alive"
        // find("Connection")->second 就会得到 "keep-alive"
        //
        // 双重条件检查：
        // 条件1：header_.find("Connection")->second == "keep-alive"
        //       检查客户端是否明确要求keep-alive
        // 条件2：version_ == "1.1"  
        //       检查是否是HTTP/1.1版本（只有1.1版本默认支持keep-alive）
        //
        // && 操作符：两个条件都必须为真，才返回true
        return header_.find("Connection")->second == "keep-alive" && version_ == "1.1";
    }
    
    // 步骤3：没有Connection头部，返回false
    // 如果客户端根本没有发送Connection头部，说明不要求keep-alive
    return false;
}

// 【实际使用场景举例】
// 
// 场景1：支持Keep-Alive的请求
// GET /index.html HTTP/1.1
// Host: localhost:8080
// Connection: keep-alive        ← 客户端明确要求keep-alive
// 
// 此时函数返回：true（因为有Connection头且值为keep-alive，版本是1.1）
//
// 场景2：不支持Keep-Alive的请求 
// GET /index.html HTTP/1.1
// Host: localhost:8080
// Connection: close            ← 客户端要求关闭连接
//
// 此时函数返回：false（虽然有Connection头，但值是close不是keep-alive）
//
// 场景3：HTTP/1.0请求
// GET /index.html HTTP/1.0     ← 版本是1.0
// Host: localhost:8080
// Connection: keep-alive
//
// 此时函数返回：false（虽然有keep-alive头，但版本不是1.1）
//
// 场景4：没有Connection头
// GET /index.html HTTP/1.1
// Host: localhost:8080
//                              ← 没有Connection头部
//
// 此时函数返回：false（因为count("Connection")返回0，进不了if语句）

// ==================== 核心解析函数 ====================

// 这是解析HTTP请求的主要函数
// 输入：包含HTTP请求数据的缓冲区
// 输出：解析是否成功
bool HttpRequest::parse(Buffer& buff) {
    const char CRLF[] = "\r\n";  // HTTP协议中的换行符
    
    // 如果缓冲区没有数据，返回失败
    if(buff.ReadableBytes() <= 0) {
        return false;
    }
    
    // 用状态机一行一行地解析HTTP请求
    // state_表示当前解析到哪个部分了
    while(buff.ReadableBytes() && state_ != FINISH) {
        // 找到当前行的结束位置
        const char* lineEnd = search(buff.Peek(), buff.BeginWriteConst(), CRLF, CRLF + 2);
        std::string line(buff.Peek(), lineEnd);  // 提取当前行的内容
        
        // 根据当前状态决定如何解析这一行
        switch(state_)
        {
        case REQUEST_LINE:
            // 解析第一行：比如 "GET /index.html HTTP/1.1"
            if(!ParseRequestLine_(line)) {
                return false;  // 格式错误
            }
            ParsePath_();      // 处理路径（比如添加.html后缀）
            break;    
        case HEADERS:
            // 解析头部：比如 "Host: www.example.com"
            ParseHeader_(line);
            if(buff.ReadableBytes() <= 2) {
                state_ = FINISH;  // 没有更多数据了
            }
            break;
        case BODY:
            // 解析请求体：POST表单数据
            ParseBody_(line);
            break;
        default:
            break;
        }
        
        // 如果已经到缓冲区末尾，退出循环
        if(lineEnd == buff.BeginWrite()) { break; }
        
        // 移除已经处理的这一行
        buff.RetrieveUntil(lineEnd + 2);
    }
    
    // 记录解析结果到日志
    LOG_DEBUG("[%s], [%s], [%s]", method_.c_str(), path_.c_str(), version_.c_str());
    return true;
}

// ==================== 路径处理 ====================

// 处理请求路径，简化用户访问
void HttpRequest::ParsePath_() {
    // 情况1：如果用户访问的是网站根目录 "/"
    if(path_ == "/") {
        path_ = "/index.html";  // 自动跳转到首页文件 index.html
    }
    else {
        // 情况2：检查用户请求的路径是否是预定义的特殊页面
        // DEFAULT_HTML 是一个容器，里面存放了一些特殊页面的名字
        // 比如可能包含：["/login", "/register", "/welcome"] 等页面
        
        // 用范围for循环遍历所有预定义的页面名
        for(auto &item: DEFAULT_HTML) {
            // 如果当前用户请求的路径 path_ 匹配到了某个预定义页面
            if(item == path_) {
                // 就自动给这个路径加上 ".html" 后缀
                // 举例：用户访问 "/login" → 自动变成 "/login.html"
                //       用户访问 "/register" → 自动变成 "/register.html"
                path_ += ".html";
                break;  // 找到匹配的就退出循环，不用继续找了
            }
        }
        // 注意：如果用户访问的路径不在 DEFAULT_HTML 列表中
        // 那么路径保持原样不变，比如 "/style.css" 还是 "/style.css"
    }
}

// ==================== 解析请求行 ====================

// 这个函数是用来理解HTTP请求的第一行的，就像读懂客户的"点菜单"
// HTTP请求的第一行告诉服务器：客户想要什么、怎么要、用什么协议要
// 格式固定：方法 路径 HTTP版本
// 真实例子：GET /index.html HTTP/1.1
//          POST /login HTTP/1.1
bool HttpRequest::ParseRequestLine_(const string& line) {
    // 【第1步】准备一个"模式识别器"（正则表达式）
    // 这就像是一个模板，用来检查输入的文字是否符合HTTP请求行的格式
    // "^([^ ]*) ([^ ]*) HTTP/([^ ]*)$" 分解说明：
    // ^ = 从行的开头开始匹配
    // ([^ ]*) = 第1组：匹配任意个不是空格的字符（这就是HTTP方法，如GET/POST）
    // 空格 = 必须有一个空格分隔
    // ([^ ]*) = 第2组：匹配任意个不是空格的字符（这就是请求路径，如/index.html）
    // 空格 = 必须有一个空格分隔
    // HTTP/ = 必须有这个固定字符串
    // ([^ ]*) = 第3组：匹配任意个不是空格的字符（这就是HTTP版本号，如1.1）
    // $ = 行必须在这里结束
    regex patten("^([^ ]*) ([^ ]*) HTTP/([^ ]*)$");
    
    // 【第2步】准备一个"收集盒"来存放匹配到的各个部分
    // smatch 就像是一个有编号的小盒子，用来分别装匹配到的各部分
    smatch subMatch;  // subMatch[0]=整行, subMatch[1]=方法, subMatch[2]=路径, subMatch[3]=版本
    
    // 【第3步】尝试用模式去匹配输入的这一行
    if(regex_match(line, subMatch, patten)) {   
        // 匹配成功！说明这一行符合HTTP请求行的标准格式
        // 现在把各部分提取出来保存到类的成员变量中
        
        method_ = subMatch[1];   // 从小盒子1中取出HTTP方法（GET、POST、PUT、DELETE等）
        path_ = subMatch[2];     // 从小盒子2中取出请求路径（/index.html、/login、/api/users等）
        version_ = subMatch[3];  // 从小盒子3中取出HTTP版本（通常是1.1或2.0）
        
        // 【第4步】设置解析器的下一个状态
        // 请求行解析完了，下一步应该解析HTTP头部信息
        state_ = HEADERS;        
        
        return true;  // 返回true表示解析成功
    }
    
    // 【如果走到这里】说明输入的行不符合HTTP请求行的格式
    // 可能是格式错误、缺少空格、协议写错等等
    LOG_ERROR("RequestLine Error");  // 记录错误到日志，方便调试
    return false;  // 返回false表示解析失败
}

// ==================== 解析头部 ====================

// 解析HTTP头部信息
// 格式：键: 值
// 例子：Host: www.example.com
void HttpRequest::ParseHeader_(const string& line) {
    regex patten("^([^:]*): ?(.*)$");  // 匹配 "键: 值" 格式
    smatch subMatch;
    
    if(regex_match(line, subMatch, patten)) {
        // 保存头部信息到字典中
        header_[subMatch[1]] = subMatch[2];
    }
    else {
        // 空行表示头部结束，下一步解析请求体
        state_ = BODY;
    }
}

// ==================== 解析请求体 ====================

// 解析HTTP请求体（主要是POST表单数据）
void HttpRequest::ParseBody_(const string& line) {
    body_ = line;           // 保存请求体内容
    ParsePost_();           // 处理POST数据
    state_ = FINISH;        // 解析完成
    LOG_DEBUG("Body:%s, len:%d", line.c_str(), line.size());
}

// ==================== URL解码工具 ====================

// 将十六进制字符转换为数字
// URL中的特殊字符用%XX表示，比如空格是%20
int HttpRequest::ConverHex(char ch) {
    if(ch >= 'A' && ch <= 'F') return ch -'A' + 10;  // A=10, B=11, ..., F=15
    if(ch >= 'a' && ch <= 'f') return ch -'a' + 10;  // a=10, b=11, ..., f=15
    return ch;  // 0-9直接返回对应数字
}

// ==================== POST数据处理 ====================

// 处理POST请求的表单数据
void HttpRequest::ParsePost_() {
    // 【第一步：检查请求类型】
    // 只有当请求方法是POST，并且内容类型是表单格式时，才进行处理
    // "application/x-www-form-urlencoded" 是网页表单提交的标准格式
    if(method_ == "POST" && header_["Content-Type"] == "application/x-www-form-urlencoded") {
        
        // 【第二步：解析表单数据】
        // 把请求体中的表单数据（如username=admin&password=123）
        // 解析成键值对存储到post_字典中
        ParseFromUrlencoded_();  // 解析表单数据
        
        // 【第三步：特殊页面处理】
        // 检查当前请求的路径是否是需要特殊处理的页面
        // DEFAULT_HTML_TAG是一个预定义的映射表，记录了哪些路径需要特殊处理
        if(DEFAULT_HTML_TAG.count(path_)) {
            
            // 【获取页面标识】
            // 从映射表中获取当前路径对应的标识号
            // 比如：登录页面可能是1，注册页面可能是0
            int tag = DEFAULT_HTML_TAG.find(path_)->second;
            LOG_DEBUG("Tag:%d", tag);
            
            // 【处理登录/注册请求】
            // 如果标识是0或1，说明这是登录或注册页面
            if(tag == 0 || tag == 1) {
                
                // 【判断是登录还是注册】
                // tag=1表示登录，tag=0表示注册
                bool isLogin = (tag == 1);  // tag=1是登录，tag=0是注册
                
                // 【验证用户信息】
                // 从解析出的表单数据中获取用户名和密码
                // post_["username"]获取用户名，post_["password"]获取密码
                // UserVerify函数会检查用户名密码是否正确
                if(UserVerify(post_["username"], post_["password"], isLogin)) {
                    // 【验证成功】
                    // 如果用户名密码正确，跳转到欢迎页面
                    path_ = "/welcome.html";  // 成功，跳转到欢迎页
                } 
                else {
                    // 【验证失败】
                    // 如果用户名密码错误，跳转到错误页面
                    path_ = "/error.html";    // 失败，跳转到错误页
                }
            }
        }
    }   
}

// ==================== 表单数据解析 ====================

// 解析表单数据
// 作用：把网页表单提交的数据（如"username=admin&password=123"）解析成键值对
// 就像把"姓名=张三&年龄=25"这样的字符串，分解成：姓名→张三，年龄→25
void HttpRequest::ParseFromUrlencoded_() {
    if(body_.size() == 0) { return; }  // 如果请求体是空的，就不用解析了

    string key, value;      // key存储键名（如username），value存储值（如admin）
    int num = 0;           // 用于URL解码的临时数字
    int n = body_.size();  // 请求体的总长度
    int i = 0, j = 0;      // i是当前读取位置，j是当前正在解析的片段的起始位置

    // 【核心思路】：逐个字符扫描，遇到特殊字符就做相应处理
    // 就像读一个句子，遇到标点符号就知道该怎么断句
    for(; i < n; i++) {
        char ch = body_[i];  // 获取当前字符
        
        switch (ch) {
        case '=':
            // 【遇到等号】：说明等号前面是键名，等号后面是值
            // 比如"username=admin"，等号前是"username"
            key = body_.substr(j, i - j);  // 从j到i的部分就是键名
            j = i + 1;  // 下次开始读取的位置从等号后面开始
            break;
            
        case '+':
            // 【遇到加号】：在URL编码中，+号代表空格
            // 比如"hello+world" 实际表示 "hello world"
            body_[i] = ' ';  // 直接把+号替换成空格
            break;
            
        case '%':
            // 【遇到%号】：这是URL编码，%后面跟两个十六进制数字
            // 比如%20表示空格，%21表示感叹号
            // 需要把%XX转换回原来的字符
            
            // 把十六进制转换成十进制数字
            // 比如%20 → 2*16 + 0 = 32 → 对应空格字符
            num = ConverHex(body_[i + 1]) * 16 + ConverHex(body_[i + 2]);
            
            // 这里的处理有点特殊，把解码后的数字重新编码成字符串形式
            // 实际上这段代码有问题，应该直接替换成对应的字符
            body_[i + 2] = num % 10 + '0';   // 个位数
            body_[i + 1] = num / 10 + '0';   // 十位数
            i += 2;  // 跳过已经处理的两个字符
            break;
            
        case '&':
            // 【遇到&号】：说明一个键值对结束了，开始下一个
            // 比如"username=admin&password=123"中的&
            value = body_.substr(j, i - j);  // 从j到i的部分就是值
            j = i + 1;  // 下次从&后面开始读取
            
            // 把解析出的键值对保存到post_字典中
            post_[key] = value;  // 相当于：字典[键名] = 值
            LOG_DEBUG("%s = %s", key.c_str(), value.c_str());  // 打印调试信息
            break;
            
        default:
            // 普通字符，什么都不做，继续往下读
            break;
        }
    }
    
    // 【处理最后一个键值对】
    // 因为字符串可能不是以&结尾，所以最后一个键值对需要单独处理
    // 比如"username=admin&password=123" 最后的"password=123"没有&结尾
    assert(j <= i);  // 确保j不会超过i（防止程序出错）
    
    // 如果当前的key还没有被保存，并且j < i说明还有未处理的内容
    if(post_.count(key) == 0 && j < i) {
        value = body_.substr(j, i - j);  // 提取最后的值
        post_[key] = value;              // 保存最后的键值对
    }
}

// ==================== 用户认证系统 ====================

// 验证用户登录或注册新用户
// name: 用户名, pwd: 密码, isLogin: true表示登录，false表示注册
bool HttpRequest::UserVerify(const string &name, const string &pwd, bool isLogin) {
    if(name == "" || pwd == "") { return false; }  // 用户名密码不能为空
    
    LOG_INFO("Verify name:%s pwd:%s", name.c_str(), pwd.c_str());
    
    // 获取数据库连接（用户信息存储在MySQL数据库中）
    MYSQL* sql;
    SqlConnRAII(&sql, SqlConnPool::Instance());
    assert(sql);
    
    bool flag = false;
    unsigned int j = 0;
    char order[256] = { 0 };        // SQL语句
    MYSQL_FIELD *fields = nullptr;  // 数据库字段信息
    MYSQL_RES *res = nullptr;       // 查询结果
    
    if(!isLogin) { flag = true; }   // 注册时先假设用户名可用
    
    // 构造SQL查询语句：在用户表中查找这个用户名
    snprintf(order, 256, "SELECT username, password FROM user WHERE username='%s' LIMIT 1", name.c_str());
    LOG_DEBUG("%s", order);

    // 执行查询
    if(mysql_query(sql, order)) { 
        mysql_free_result(res);
        return false; 
    }
    
    // 获取查询结果
    res = mysql_store_result(sql);
    j = mysql_num_fields(res);
    fields = mysql_fetch_fields(res);

    // 处理查询结果
    while(MYSQL_ROW row = mysql_fetch_row(res)) {
        LOG_DEBUG("MYSQL ROW: %s %s", row[0], row[1]);
        string password(row[1]);  // 数据库中的密码
        
        if(isLogin) {
            // 登录：检查密码是否正确
            if(pwd == password) { 
                flag = true; 
            }
            else {
                flag = false;
                LOG_DEBUG("pwd error!");
            }
        } 
        else { 
            // 注册：如果查到了用户名，说明已被使用
            flag = false; 
            LOG_DEBUG("user used!");
        }
    }
    mysql_free_result(res);
 
    // 如果是注册且用户名可用，添加新用户到数据库
    if(!isLogin && flag == true) {
        LOG_DEBUG("regirster!");
        bzero(order, 256);
        
        // 构造插入SQL：添加新用户
        snprintf(order, 256,"INSERT INTO user(username, password) VALUES('%s','%s')", name.c_str(), pwd.c_str());
        LOG_DEBUG( "%s", order);
        
        if(mysql_query(sql, order)) { 
            LOG_DEBUG( "Insert error!");
            flag = false; 
        }
        flag = true;
    }
    
    SqlConnPool::Instance()->FreeConn(sql);  // 归还数据库连接
    LOG_DEBUG( "UserVerify success!!");
    return flag;
}

// ==================== 数据访问接口 ====================

// 获取请求路径（只读）
std::string HttpRequest::path() const{
    return path_;
}

// 获取请求路径（可修改）
std::string& HttpRequest::path(){
    return path_;
}

// 获取HTTP方法（GET、POST等）
std::string HttpRequest::method() const {
    return method_;
}

// 获取HTTP版本
std::string HttpRequest::version() const {
    return version_;
}

// 从POST数据中获取参数值
std::string HttpRequest::GetPost(const std::string& key) const {
    assert(key != "");
    if(post_.count(key) == 1) {
        return post_.find(key)->second;
    }
    return "";
}

// 从POST数据中获取参数值（C字符串版本）
std::string HttpRequest::GetPost(const char* key) const {
    assert(key != nullptr);
    if(post_.count(key) == 1) {
        return post_.find(key)->second;
    }
    return "";
}