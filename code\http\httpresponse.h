/*
 * <AUTHOR> mark
 * @Date         : 2020-06-25
 * @copyleft Apache 2.0
 * 
 * HttpResponse HTTP响应生成器
 * 
 * What (是什么)：
 * HTTP/1.1响应报文生成器，支持静态文件服务、错误页面、内存映射文件传输
 * 
 * Why (为什么)：
 * 1. 标准化HTTP响应格式生成
 * 2. 高效的静态文件服务能力
 * 3. 智能的MIME类型识别
 * 4. 零拷贝文件传输优化
 * 5. 完善的错误处理机制
 * 
 * How (怎么做)：
 * 1. 使用mmap内存映射技术加速文件读取
 * 2. 自动识别文件类型设置Content-Type
 * 3. 生成标准的HTTP状态行和头部
 * 4. 支持Keep-Alive连接管理
 * 5. 统一的错误页面处理
 */ 

#ifndef HTTP_RESPONSE_H
#define HTTP_RESPONSE_H

// 系统头文件
#include <unordered_map>   // 哈希表：存储MIME类型映射和状态码映射
#include <fcntl.h>         // 文件控制：open()系统调用
#include <unistd.h>        // Unix标准：close()系统调用
#include <sys/stat.h>      // 文件状态：stat()获取文件信息
#include <sys/mman.h>      // 内存映射：mmap(), munmap()

// 项目内部模块
#include "../buffer/buffer.h"  // 缓冲区管理
#include "../log/log.h"        // 日志系统

/**
 * @class HttpResponse
 * @brief HTTP响应生成和文件服务类
 * 
 * 响应生成架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                   HttpResponse                          │
 * │                                                         │
 * │  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐   │
 * │  │    初始化    │──►│   生成响应   │──►│   文件服务   │   │
 * │  │             │   │             │   │             │   │
 * │  │设置基本参数  │   │状态行+头部  │   │mmap映射     │   │
 * │  │路径/代码    │   │Content-Type │   │零拷贝传输   │   │
 * │  │Keep-Alive   │   │Connection   │   │自动解映射   │   │
 * │  └─────────────┘   └─────────────┘   └─────────────┘   │
 * │         │                  │                  │        │
 * │         ▼                  ▼                  ▼        │
 * │  ┌─────────────────────────────────────────────────────┐ │
 * │  │                文件处理流程                          │ │
 * │  │                                                     │ │
 * │  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐│ │
 * │  │  │路径验证  │  │权限检查  │  │MIME识别 │  │内存映射 ││ │
 * │  │  │stat()   │  │S_IROTH  │  │后缀匹配  │  │mmap()  ││ │
 * │  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘│ │
 * │  └─────────────────────────────────────────────────────┘ │
 * └─────────────────────────────────────────────────────────┘
 */
class HttpResponse {
public:
    // ==================== 构造与析构 ====================
    
    /**
     * @brief 默认构造函数
     * What: 初始化HTTP响应对象为无效状态
     * Why:  对象池模式下需要预创建对象，后续通过Init()激活
     * How:  设置默认的无效状态值
     */
    HttpResponse();
    
    /**
     * @brief 析构函数
     * What: 清理HTTP响应相关资源
     * Why:  RAII原则，确保内存映射文件被正确解除
     * How:  调用UnmapFile()释放mmap资源
     */
    ~HttpResponse();

    // ==================== 生命周期管理 ====================

    /**
     * @brief 初始化HTTP响应
     * @param srcDir 静态资源根目录
     * @param path 请求的文件路径
     * @param isKeepAlive 是否保持连接
     * @param code HTTP状态码
     * 
     * What: 设置HTTP响应的基本参数
     * Why:  准备响应生成所需的所有信息
     * How:  保存参数并重置内部状态
     */
    void Init(const std::string& srcDir, std::string& path, bool isKeepAlive = false, int code = -1);
    
    /**
     * @brief 生成完整的HTTP响应
     * @param buff 输出缓冲区，用于存储响应数据
     * 
     * What: 生成标准HTTP响应报文的核心函数
     * Why:  将各种响应信息组合成符合HTTP协议的响应
     * How:  
     * 1. 检查文件存在性和权限
     * 2. 生成状态行、头部、内容
     * 3. 处理错误情况
     */
    void MakeResponse(Buffer& buff);
    
    /**
     * @brief 解除文件内存映射
     * 
     * What: 释放mmap映射的文件内存
     * Why:  避免内存泄漏，系统资源有限
     * How:  调用munmap()系统调用
     */
    void UnmapFile();
    
    /**
     * @brief 生成错误响应内容
     * @param buff 输出缓冲区
     * @param message 错误信息
     * 
     * What: 生成HTML格式的错误页面
     * Why:  向用户展示友好的错误信息
     * How:  构造简单的HTML错误页面
     */
    void ErrorContent(Buffer& buff, std::string message);

    // ==================== 文件访问接口 ====================

    /**
     * @brief 获取映射的文件内容指针
     * @return char* 文件内容的内存地址
     * 
     * What: 返回mmap映射的文件内存地址
     * Why:  支持零拷贝文件传输，提高性能
     * How:  直接返回mmFile_指针
     */
    char* File();
    
    /**
     * @brief 获取文件大小
     * @return size_t 文件字节数
     * 
     * What: 返回当前文件的大小
     * Why:  HTTP响应需要Content-Length头部
     * How:  返回stat结构中的文件大小
     */
    size_t FileLen() const;
    
    /**
     * @brief 获取HTTP状态码
     * @return int HTTP状态码
     * 
     * What: 返回当前响应的状态码
     * Why:  外部需要了解响应的处理结果
     * How:  返回code_成员变量
     */
    int Code() const { return code_; }

private:
    // ==================== 私有响应生成函数 ====================

    /**
     * @brief 添加HTTP状态行
     * @param buff 输出缓冲区
     * 
     * What: 生成"HTTP/1.1 状态码 状态描述"格式的状态行
     * Why:  HTTP响应的第一行，标识处理结果
     * How:  根据状态码查表获取描述文字
     */
    void AddStateLine_(Buffer &buff);
    
    /**
     * @brief 添加HTTP响应头部
     * @param buff 输出缓冲区
     * 
     * What: 生成HTTP响应头部字段
     * Why:  头部包含响应的元数据信息
     * How:  添加Connection、Content-Type等必要头部
     */
    void AddHeader_(Buffer &buff);
    
    /**
     * @brief 添加HTTP响应内容
     * @param buff 输出缓冲区
     * 
     * What: 处理响应体内容（通常是文件）
     * Why:  响应体包含实际的资源数据
     * How:  
     * 1. 打开请求的文件
     * 2. 使用mmap映射到内存
     * 3. 添加Content-Length头部
     */
    void AddContent_(Buffer &buff);

    /**
     * @brief 处理错误页面映射
     * 
     * What: 将错误状态码映射到对应的错误页面
     * Why:  为不同错误提供专门的错误页面
     * How:  查表映射，更新path_和文件状态
     */
    void ErrorHtml_();
    
    /**
     * @brief 获取文件MIME类型
     * @return std::string 文件的MIME类型
     * 
     * What: 根据文件扩展名确定MIME类型
     * Why:  浏览器需要正确的Content-Type来处理文件
     * How:  提取文件后缀，查表映射到MIME类型
     */
    std::string GetFileType_();

    // ==================== 成员变量 ====================

    /**
     * @brief HTTP状态码
     * What: 标识HTTP请求的处理结果
     * Why:  客户端需要了解请求是否成功
     * How:  200表示成功，4xx表示客户端错误，5xx表示服务器错误
     */
    int code_;
    
    /**
     * @brief 连接保持标志
     * What: 标识是否保持HTTP连接
     * Why:  影响Connection头部的生成
     * How:  true生成keep-alive，false生成close
     */
    bool isKeepAlive_;

    /**
     * @brief 请求的文件路径
     * What: 客户端请求的资源路径
     * Why:  用于定位和打开对应的文件
     * How:  与srcDir_拼接成完整的文件系统路径
     */
    std::string path_;
    
    /**
     * @brief 静态资源根目录
     * What: 服务器静态文件的根目录路径
     * Why:  安全限制，防止访问根目录外的文件
     * How:  所有文件路径都相对于此目录
     */
    std::string srcDir_;
    
    /**
     * @brief 内存映射文件指针
     * What: mmap映射的文件内容地址
     * Why:  支持零拷贝文件传输，提高性能
     * How:  通过mmap()将文件映射到虚拟内存
     */
    char* mmFile_; 
    
    /**
     * @brief 文件状态信息
     * What: 存储文件的详细信息
     * Why:  获取文件大小、权限、类型等信息
     * How:  通过stat()系统调用填充
     */
    struct stat mmFileStat_;

    // ==================== 静态配置数据 ====================

    /**
     * @brief 文件扩展名到MIME类型映射表
     * What: 根据文件后缀确定Content-Type的映射关系
     * Why:  浏览器需要正确的MIME类型来处理不同格式文件
     * How:  静态映射表，支持常见的文件类型
     */
    static const std::unordered_map<std::string, std::string> SUFFIX_TYPE;
    
    /**
     * @brief HTTP状态码到状态描述映射表
     * What: 状态码对应的标准描述文字
     * Why:  HTTP协议要求状态行包含状态描述
     * How:  静态映射表，包含常用状态码的描述
     */
    static const std::unordered_map<int, std::string> CODE_STATUS;
    
    /**
     * @brief HTTP错误码到错误页面映射表
     * What: 错误状态码对应的错误页面路径
     * Why:  为不同错误提供专门的用户友好页面
     * How:  静态映射表，将4xx/5xx错误映射到HTML页面
     */
    static const std::unordered_map<int, std::string> CODE_PATH;
};

#endif //HTTP_RESPONSE_H