/*
 * <AUTHOR> mark
 * @Date         : 2020-06-17
 * @copyleft Apache 2.0
 * 
 * WebServer 高性能Web服务器核心类
 * 架构模式：Reactor + 线程池混合模式
 * 核心特性：
 * 1. 基于epoll的事件驱动架构
 * 2. 多线程处理业务逻辑
 * 3. 连接池和线程池资源管理
 * 4. 定时器管理连接超时
 * 5. 异步日志系统
 */ 

#ifndef WEBSERVER_H
#define WEBSERVER_H

// 标准库头文件
#include <unordered_map>  // STL哈希表，用于管理客户端连接

// 系统调用相关头文件
#include <fcntl.h>        // 文件控制：fcntl()函数，设置文件描述符属性
#include <unistd.h>       // Unix标准函数：close()等系统调用
#include <assert.h>       // 断言宏，用于调试和错误检查
#include <errno.h>        // 错误码定义，系统调用错误处理

// 网络编程相关头文件
#include <sys/socket.h>   // Socket编程：socket(), bind(), listen()等
#include <netinet/in.h>   // 网络地址结构：sockaddr_in等
#include <arpa/inet.h>    // 网络地址转换：inet_addr(), inet_ntoa()等

// 项目内部模块头文件
#include "epoller.h"              // epoll事件管理器
#include "../log/log.h"           // 异步日志系统
#include "../timer/heaptimer.h"   // 小根堆定时器
#include "../pool/sqlconnpool.h"  // 数据库连接池
#include "../pool/threadpool.h"   // 线程池
#include "../pool/sqlconnRAII.h"  // 数据库连接RAII管理
#include "../http/httpconn.h"     // HTTP连接处理

/**
 * @class WebServer
 * @brief 高性能Web服务器核心类
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                    WebServer                            │
 * │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
 * │  │   主线程     │  │  工作线程池  │  │   资源池     │     │
 * │  │             │  │             │  │             │     │
 * │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │     │
 * │  │ │ Epoll   │ │  │ │ Thread  │ │  │ │ SQL连接 │ │     │
 * │  │ │ 事件循环 │ │◄─┤ │ Pool    │ │  │ │ Pool    │ │     │
 * │  │ │         │ │  │ │         │ │  │ │         │ │     │
 * │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │     │
 * │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │     │
 * │  │ │ Timer   │ │  │ │ HTTP    │ │  │ │ Log     │ │     │
 * │  │ │ 定时器   │ │  │ │ 处理    │ │  │ │ 系统    │ │     │
 * │  │ │         │ │  │ │         │ │  │ │         │ │     │
 * │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │     │
 * │  └─────────────┘  └─────────────┘  └─────────────┘     │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 工作流程：
 * 1. 主线程运行epoll事件循环，监听网络事件
 * 2. 新连接到达时，accept并添加到epoll监听
 * 3. 数据到达时，将处理任务提交给线程池
 * 4. 工作线程处理HTTP请求，访问数据库
 * 5. 处理完成后，主线程负责发送响应
 * 6. 定时器管理连接超时，自动清理无效连接
 */
class WebServer {
public:
    /**
     * @brief WebServer构造函数
     * @param port 监听端口号
     * @param trigMode 触发模式：0-LT+LT, 1-LT+ET, 2-ET+LT, 3-ET+ET
     * @param timeoutMS 连接超时时间（毫秒）
     * @param OptLinger 是否启用优雅关闭
     * @param sqlPort 数据库端口
     * @param sqlUser 数据库用户名
     * @param sqlPwd 数据库密码
     * @param dbName 数据库名
     * @param connPoolNum 数据库连接池大小
     * @param threadNum 线程池大小
     * @param openLog 是否开启日志
     * @param logLevel 日志级别
     * @param logQueSize 日志队列大小
     * 
     * 参数设计体现了高度的可配置性，适应不同的部署环境
     */
    WebServer(
        int port, int trigMode, int timeoutMS, bool OptLinger, 
        int sqlPort, const char* sqlUser, const  char* sqlPwd, 
        const char* dbName, int connPoolNum, int threadNum,
        bool openLog, int logLevel, int logQueSize);

    /**
     * @brief 析构函数
     * 负责清理资源，关闭连接，停止服务
     */
    ~WebServer();
    
    /**
     * @brief 启动服务器主循环
     * 
     * 核心功能：
     * 1. 初始化Socket监听
     * 2. 进入epoll事件循环
     * 3. 处理各种网络事件
     * 4. 管理客户端连接
     */
    void Start();

private:
    // ==================== 初始化相关方法 ====================
    
    /**
     * @brief 初始化监听Socket
     * @return bool 初始化是否成功
     * 
     * 功能：
     * 1. 创建监听socket
     * 2. 设置socket选项
     * 3. 绑定地址和端口
     * 4. 开始监听连接
     */
    bool InitSocket_(); 
    
    /**
     * @brief 初始化事件模式
     * @param trigMode 触发模式配置
     * 
     * 支持的模式：
     * - LT（水平触发）：事件持续触发直到处理完成
     * - ET（边缘触发）：事件只在状态改变时触发一次
     */
    void InitEventMode_(int trigMode);
    
    /**
     * @brief 添加新客户端连接
     * @param fd 客户端文件描述符
     * @param addr 客户端地址信息
     * 
     * 功能：
     * 1. 创建HttpConn对象管理连接
     * 2. 添加到epoll监听
     * 3. 设置定时器
     */
    void AddClient_(int fd, sockaddr_in addr);

    // ==================== 事件处理方法 ====================
  
    /**
     * @brief 处理监听socket的新连接事件
     * 
     * 流程：
     * 1. accept新连接
     * 2. 设置非阻塞模式
     * 3. 添加到客户端管理
     */
    void DealListen_();
    
    /**
     * @brief 处理客户端写事件
     * @param client HTTP连接对象指针
     * 
     * 负责将响应数据发送给客户端
     */
    void DealWrite_(HttpConn* client);
    
    /**
     * @brief 处理客户端读事件
     * @param client HTTP连接对象指针
     * 
     * 负责读取客户端发送的请求数据
     */
    void DealRead_(HttpConn* client);

    // ==================== 连接管理方法 ====================

    /**
     * @brief 发送错误响应
     * @param fd 客户端文件描述符
     * @param info 错误信息
     * 
     * 用于向客户端发送HTTP错误响应
     */
    void SendError_(int fd, const char*info);
    
    /**
     * @brief 延长连接超时时间
     * @param client HTTP连接对象指针
     * 
     * 在Keep-Alive模式下重置连接的超时时间
     */
    void ExtentTime_(HttpConn* client);
    
    /**
     * @brief 关闭客户端连接
     * @param client HTTP连接对象指针
     * 
     * 功能：
     * 1. 从epoll中移除
     * 2. 关闭文件描述符
     * 3. 清理相关资源
     */
    void CloseConn_(HttpConn* client);

    // ==================== 业务处理方法 ====================

    /**
     * @brief 处理读取完成的连接（提交给线程池）
     * @param client HTTP连接对象指针
     * 
     * 将HTTP请求解析任务提交给线程池处理
     */
    void OnRead_(HttpConn* client);
    
    /**
     * @brief 处理写入完成的连接
     * @param client HTTP连接对象指针
     * 
     * 处理响应发送完成后的连接状态
     */
    void OnWrite_(HttpConn* client);
    
    /**
     * @brief 处理HTTP请求（在工作线程中执行）
     * @param client HTTP连接对象指针
     * 
     * 核心业务逻辑：
     * 1. 解析HTTP请求
     * 2. 处理业务逻辑
     * 3. 生成HTTP响应
     */
    void OnProcess(HttpConn* client);

    // ==================== 静态常量和工具方法 ====================

    /**
     * @brief 最大文件描述符数量
     * Linux系统默认限制，可通过ulimit调整
     */
    static const int MAX_FD = 65536;

    /**
     * @brief 设置文件描述符为非阻塞模式
     * @param fd 文件描述符
     * @return int 设置结果
     * 
     * 非阻塞IO是高性能服务器的基础
     */
    static int SetFdNonblock(int fd);

    // ==================== 成员变量 ====================

    // 网络配置
    int port_;              // 监听端口号
    bool openLinger_;       // 是否启用SO_LINGER选项
    int timeoutMS_;         // 连接超时时间（毫秒）
    bool isClose_;          // 服务器关闭标志
    int listenFd_;          // 监听socket文件描述符
    char* srcDir_;          // 静态资源目录路径
    
    // 事件配置
    uint32_t listenEvent_;  // 监听socket的epoll事件
    uint32_t connEvent_;    // 客户端连接的epoll事件
   
    // 核心组件（使用智能指针管理）
    std::unique_ptr<HeapTimer> timer_;      // 定时器管理器
    std::unique_ptr<ThreadPool> threadpool_; // 线程池
    std::unique_ptr<Epoller> epoller_;      // epoll事件管理器
    
    // 连接管理
    std::unordered_map<int, HttpConn> users_; // 客户端连接映射表
                                              // key: 文件描述符
                                              // value: HTTP连接对象
};

#endif //WEBSERVER_H