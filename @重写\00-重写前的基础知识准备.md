# WebServer重写前的基础知识准备

> **主人，在开始重写之前，我们需要先掌握一些核心概念。这不是枯燥的理论学习，而是实战前的"磨刀不误砍柴工"！**

## 📚 第一部分：必备的C++基础知识

### 1.1 现代C++特性 (C++11/14)

#### 1.1.1 智能指针 - 自动内存管理的神器

**为什么需要智能指针？**
```cpp
// 传统指针的问题
void bad_example() {
    int* ptr = new int(10);  // 分配内存
    // 如果这里发生异常，delete永远不会被调用！
    some_function_that_might_throw();
    delete ptr;  // 释放内存 - 可能永远执行不到
}

// 智能指针的解决方案
void good_example() {
    std::unique_ptr<int> ptr = std::make_unique<int>(10);
    // 无论如何退出函数，内存都会自动释放！
    some_function_that_might_throw();
    // 不需要手动delete，析构函数自动调用
}
```

**unique_ptr - 独占所有权**
```cpp
#include <memory>

// 基本用法
std::unique_ptr<int> ptr1 = std::make_unique<int>(42);
std::cout << *ptr1 << std::endl;  // 输出: 42

// 移动语义 - 所有权转移
std::unique_ptr<int> ptr2 = std::move(ptr1);
// 现在ptr1为nullptr，ptr2拥有对象

// 在WebServer中的应用
class WebServer {
private:
    std::unique_ptr<ThreadPool> threadpool_;  // 自动管理线程池
    std::unique_ptr<Epoller> epoller_;        // 自动管理epoll对象
};
```

**实战练习1：智能指针基础**
```cpp
// 请您亲自编写并运行这个例子
#include <iostream>
#include <memory>

class Resource {
public:
    Resource(int id) : id_(id) {
        std::cout << "Resource " << id_ << " created\n";
    }
    
    ~Resource() {
        std::cout << "Resource " << id_ << " destroyed\n";
    }
    
    void use() {
        std::cout << "Using resource " << id_ << "\n";
    }
    
private:
    int id_;
};

int main() {
    {
        auto res = std::make_unique<Resource>(1);
        res->use();
    }  // 这里Resource自动析构
    
    std::cout << "Program continues...\n";
    return 0;
}
```

#### 1.1.2 RAII机制 - 资源管理的黄金法则

**什么是RAII？**
- **R**esource **A**cquisition **I**s **I**nitialization
- 资源获取即初始化
- 核心思想：在构造函数中获取资源，在析构函数中释放资源

**RAII的威力**
```cpp
// 文件操作的RAII包装
class FileRAII {
public:
    FileRAII(const std::string& filename) {
        file_ = fopen(filename.c_str(), "r");
        if (!file_) {
            throw std::runtime_error("Cannot open file");
        }
        std::cout << "File opened\n";
    }
    
    ~FileRAII() {
        if (file_) {
            fclose(file_);
            std::cout << "File closed\n";
        }
    }
    
    FILE* get() { return file_; }
    
private:
    FILE* file_;
};

// 使用示例
void process_file() {
    FileRAII file("data.txt");  // 自动打开文件
    // 处理文件...
    // 即使发生异常，文件也会自动关闭
}
```

#### 1.1.3 lambda表达式 - 函数式编程的简洁之美

**基本语法**
```cpp
// [捕获列表](参数列表) -> 返回类型 { 函数体 }

// 简单例子
auto add = [](int a, int b) -> int {
    return a + b;
};

int result = add(3, 5);  // result = 8
```

**捕获方式详解**
```cpp
int x = 10, y = 20;

// 按值捕获
auto lambda1 = [x, y](int z) {
    return x + y + z;  // x和y是副本
};

// 按引用捕获
auto lambda2 = [&x, &y](int z) {
    x += z;  // 直接修改原变量
    return x + y;
};

// 捕获所有（按值）
auto lambda3 = [=](int z) {
    return x + y + z;
};

// 捕获所有（按引用）
auto lambda4 = [&](int z) {
    x += z;
    return x + y;
};
```

**在WebServer中的应用**
```cpp
// 定时器回调函数
timer_->add(fd, timeout, [this, fd]() {
    std::cout << "Connection " << fd << " timeout\n";
    close_connection(fd);
});

// 线程池任务
threadpool_->enqueue([this, fd]() {
    handle_request(fd);
});
```

### 1.2 STL容器深度理解

#### 1.2.1 vector - 动态数组的完美实现

**内存布局和扩容机制**
```cpp
#include <vector>
#include <iostream>

void demonstrate_vector_growth() {
    std::vector<int> vec;
    
    std::cout << "Initial capacity: " << vec.capacity() << std::endl;
    
    for (int i = 0; i < 10; ++i) {
        vec.push_back(i);
        std::cout << "Size: " << vec.size() 
                  << ", Capacity: " << vec.capacity() << std::endl;
    }
}

// 输出解释：
// 容量通常按2倍增长：1 -> 2 -> 4 -> 8 -> 16...
// 这样摊还时间复杂度是O(1)
```

**为什么Buffer使用vector<char>？**
```cpp
class Buffer {
private:
    std::vector<char> buffer_;  // 为什么不用char*？
    
    // 优势1：自动内存管理
    // 优势2：异常安全
    // 优势3：连续内存布局
    // 优势4：支持resize()和reserve()
};
```

#### 1.2.2 unordered_map - 哈希表的高效查找

**时间复杂度分析**
```cpp
#include <unordered_map>
#include <chrono>

void compare_map_performance() {
    std::map<int, std::string> ordered_map;        // 红黑树，O(log n)
    std::unordered_map<int, std::string> hash_map; // 哈希表，O(1)
    
    // 插入10万个元素的性能对比
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 100000; ++i) {
        hash_map[i] = "value" + std::to_string(i);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "Hash map insert time: " << duration.count() << " us\n";
}
```

**在WebServer中的应用**
```cpp
class WebServer {
private:
    // 文件描述符到HTTP连接的映射
    std::unordered_map<int, HttpConn> users_;
    
    // 为什么用unordered_map？
    // 1. 需要快速根据fd查找连接：O(1)
    // 2. 不需要排序，只需要快速查找
    // 3. 连接的建立和关闭很频繁
};
```

### 1.3 多线程编程基础

#### 1.3.1 线程基础概念

**线程 vs 进程**
```
进程（Process）：
├── 独立的内存空间
├── 包含多个线程
├── 进程间通信较复杂（IPC）
└── 创建开销大

线程（Thread）：
├── 共享进程内存空间
├── 轻量级的执行单元
├── 线程间通信简单（共享内存）
└── 创建开销小
```

**创建线程的方式**
```cpp
#include <thread>
#include <iostream>

// 方式1：函数指针
void worker_function(int id) {
    std::cout << "Worker " << id << " is running\n";
}

// 方式2：类成员函数
class Worker {
public:
    void run(int id) {
        std::cout << "Class worker " << id << " is running\n";
    }
};

int main() {
    // 创建线程的不同方式
    std::thread t1(worker_function, 1);
    
    Worker w;
    std::thread t2(&Worker::run, &w, 2);
    
    std::thread t3([](int id) {
        std::cout << "Lambda worker " << id << " is running\n";
    }, 3);
    
    // 等待线程完成
    t1.join();
    t2.join();
    t3.join();
    
    return 0;
}
```

#### 1.3.2 线程同步原语

**互斥锁 (mutex)**
```cpp
#include <mutex>
#include <thread>
#include <vector>

class Counter {
public:
    void increment() {
        std::lock_guard<std::mutex> lock(mutex_);  // RAII锁管理
        ++count_;  // 临界区代码
    }  // 锁自动释放
    
    int get() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return count_;
    }
    
private:
    mutable std::mutex mutex_;  // mutable允许在const函数中修改
    int count_ = 0;
};

// 多线程安全的计数器
void test_thread_safety() {
    Counter counter;
    std::vector<std::thread> threads;
    
    // 创建10个线程，每个线程增加1000次
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&counter]() {
            for (int j = 0; j < 1000; ++j) {
                counter.increment();
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& t : threads) {
        t.join();
    }
    
    std::cout << "Final count: " << counter.get() << std::endl;
    // 应该输出10000
}
```

**条件变量 (condition_variable)**
```cpp
#include <condition_variable>
#include <queue>

template<typename T>
class ThreadSafeQueue {
public:
    void push(T item) {
        std::unique_lock<std::mutex> lock(mutex_);
        queue_.push(item);
        condition_.notify_one();  // 通知等待的线程
    }
    
    T pop() {
        std::unique_lock<std::mutex> lock(mutex_);
        // 等待直到队列非空
        condition_.wait(lock, [this] { return !queue_.empty(); });
        
        T result = queue_.front();
        queue_.pop();
        return result;
    }
    
private:
    std::queue<T> queue_;
    std::mutex mutex_;
    std::condition_variable condition_;
};
```

**实战练习2：生产者-消费者模式**
```cpp
// 请您实现这个完整的例子
#include <iostream>
#include <thread>
#include <chrono>

class ProducerConsumer {
private:
    ThreadSafeQueue<int> queue_;
    bool finished_ = false;
    
public:
    void producer() {
        for (int i = 0; i < 10; ++i) {
            queue_.push(i);
            std::cout << "Produced: " << i << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        finished_ = true;
    }
    
    void consumer() {
        while (!finished_ || !queue_.empty()) {
            try {
                int item = queue_.pop();
                std::cout << "Consumed: " << item << std::endl;
            } catch (...) {
                break;
            }
        }
    }
};
```

## 📡 第二部分：网络编程基础

### 2.1 Socket编程基础

#### 2.1.1 Socket是什么？

**Socket的本质**
```
Socket就像电话系统：
├── IP地址 = 电话号码
├── Port端口 = 分机号
├── Socket = 话筒
└── 数据传输 = 对话内容
```

**Socket的类型**
```cpp
// TCP Socket (可靠的、面向连接的)
int tcp_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);

// UDP Socket (不可靠的、无连接的)
int udp_socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);

// AF_INET: IPv4协议族
// SOCK_STREAM: 流式套接字(TCP)
// SOCK_DGRAM: 数据报套接字(UDP)
```

#### 2.1.2 TCP服务器的基本流程

**服务器端代码框架**
```cpp
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>

class SimpleServer {
public:
    void start(int port) {
        // 1. 创建socket
        int listen_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (listen_fd < 0) {
            perror("socket creation failed");
            return;
        }
        
        // 2. 设置地址复用
        int opt = 1;
        setsockopt(listen_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
        
        // 3. 绑定地址
        struct sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;  // 监听所有网卡
        server_addr.sin_port = htons(port);        // 主机字节序转网络字节序
        
        if (bind(listen_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            perror("bind failed");
            close(listen_fd);
            return;
        }
        
        // 4. 开始监听
        if (listen(listen_fd, 128) < 0) {  // 128是等待队列长度
            perror("listen failed");
            close(listen_fd);
            return;
        }
        
        std::cout << "Server listening on port " << port << std::endl;
        
        // 5. 接受连接循环
        while (true) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(listen_fd, (struct sockaddr*)&client_addr, &client_len);
            if (client_fd < 0) {
                perror("accept failed");
                continue;
            }
            
            std::cout << "New client connected: " << inet_ntoa(client_addr.sin_addr) 
                      << ":" << ntohs(client_addr.sin_port) << std::endl;
            
            // 6. 处理客户端请求
            handle_client(client_fd);
            close(client_fd);
        }
        
        close(listen_fd);
    }
    
private:
    void handle_client(int client_fd) {
        char buffer[1024];
        ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer) - 1);
        
        if (bytes_read > 0) {
            buffer[bytes_read] = '\0';
            std::cout << "Received: " << buffer << std::endl;
            
            // 回复客户端
            const char* response = "HTTP/1.1 200 OK\r\n\r\nHello World!";
            write(client_fd, response, strlen(response));
        }
    }
};
```

#### 2.1.3 字节序问题 - 网络编程的重要细节

**什么是字节序？**
```cpp
#include <iostream>
#include <arpa/inet.h>

void demonstrate_byte_order() {
    uint32_t host_num = 0x12345678;
    uint32_t network_num = htonl(host_num);  // 主机序转网络序
    
    std::cout << "Host order: 0x" << std::hex << host_num << std::endl;
    std::cout << "Network order: 0x" << std::hex << network_num << std::endl;
    
    // 在小端机器上输出：
    // Host order: 0x12345678
    // Network order: 0x78563412
}

// 网络编程中的字节序转换函数
// htons(): host to network short (16位)
// htonl(): host to network long (32位)  
// ntohs(): network to host short
// ntohl(): network to host long
```

### 2.2 I/O模型详解

#### 2.2.1 阻塞 vs 非阻塞

**阻塞I/O的问题**
```cpp
// 阻塞I/O示例
void blocking_server() {
    int listen_fd = socket(AF_INET, SOCK_STREAM, 0);
    // ... 绑定和监听代码 ...
    
    while (true) {
        int client_fd = accept(listen_fd, nullptr, nullptr);  // 阻塞等待
        
        char buffer[1024];
        read(client_fd, buffer, sizeof(buffer));  // 阻塞读取
        
        // 问题：一次只能处理一个客户端！
        // 当前客户端不发送数据时，服务器就卡住了
        
        close(client_fd);
    }
}
```

**非阻塞I/O的设置**
```cpp
#include <fcntl.h>

void set_nonblocking(int fd) {
    int flags = fcntl(fd, F_GETFL, 0);
    fcntl(fd, F_SETFL, flags | O_NONBLOCK);
}

void nonblocking_example() {
    int fd = socket(AF_INET, SOCK_STREAM, 0);
    set_nonblocking(fd);
    
    // 现在所有的I/O操作都不会阻塞
    ssize_t result = read(fd, buffer, size);
    if (result < 0) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            // 没有数据可读，但不是错误
            std::cout << "No data available right now" << std::endl;
        } else {
            // 真正的错误
            perror("read error");
        }
    }
}
```

#### 2.2.2 I/O多路复用 - select/poll/epoll

**为什么需要I/O多路复用？**
```
问题：如何同时监视多个socket？

解决方案演进：
1. 多进程：为每个连接创建进程 (开销大)
2. 多线程：为每个连接创建线程 (开销较大)  
3. select：监视多个fd，但有1024限制
4. poll：解决了fd数量限制，但性能仍然O(n)
5. epoll：Linux特有，性能最好O(1)
```

**select的基本用法**
```cpp
#include <sys/select.h>

void select_example() {
    fd_set read_fds;
    int max_fd = 0;
    
    while (true) {
        FD_ZERO(&read_fds);              // 清空集合
        FD_SET(listen_fd, &read_fds);    // 添加监听fd
        max_fd = listen_fd;
        
        // 添加所有客户端fd
        for (int client_fd : client_fds) {
            FD_SET(client_fd, &read_fds);
            max_fd = std::max(max_fd, client_fd);
        }
        
        // 等待事件发生
        int ready = select(max_fd + 1, &read_fds, nullptr, nullptr, nullptr);
        
        if (ready > 0) {
            // 检查监听socket
            if (FD_ISSET(listen_fd, &read_fds)) {
                int client_fd = accept(listen_fd, nullptr, nullptr);
                client_fds.push_back(client_fd);
            }
            
            // 检查客户端socket
            for (int client_fd : client_fds) {
                if (FD_ISSET(client_fd, &read_fds)) {
                    handle_client_data(client_fd);
                }
            }
        }
    }
}
```

**epoll的优势**
```cpp
#include <sys/epoll.h>

class EpollServer {
public:
    void start() {
        // 创建epoll实例
        epoll_fd_ = epoll_create1(EPOLL_CLOEXEC);
        
        // 添加监听socket到epoll
        struct epoll_event event;
        event.events = EPOLLIN;          // 监听读事件
        event.data.fd = listen_fd_;
        epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, listen_fd_, &event);
        
        struct epoll_event events[MAX_EVENTS];
        
        while (true) {
            // 等待事件，比select高效得多
            int ready = epoll_wait(epoll_fd_, events, MAX_EVENTS, -1);
            
            for (int i = 0; i < ready; ++i) {
                int fd = events[i].data.fd;
                
                if (fd == listen_fd_) {
                    accept_new_connection();
                } else {
                    handle_client_event(fd, events[i].events);
                }
            }
        }
    }
    
private:
    int epoll_fd_;
    int listen_fd_;
    static const int MAX_EVENTS = 1024;
};
```

## 🏗️ 第三部分：数据结构基础

### 3.1 堆数据结构

#### 3.1.1 什么是堆？

**堆的性质**
```
小根堆 (Min Heap)：
├── 完全二叉树
├── 父节点 ≤ 子节点
├── 堆顶是最小元素
└── 用数组存储

数组表示：
Index:  0  1  2  3  4  5  6
Value: [1][3][2][7][5][8][4]

树形结构：
       1
      / \
     3   2  
    / \ / \
   7  5 8  4

父子关系：
- 父节点索引：(i-1)/2
- 左子节点：2*i+1  
- 右子节点：2*i+2
```

**堆的基本操作**
```cpp
#include <vector>
#include <algorithm>

class MinHeap {
public:
    void push(int value) {
        heap_.push_back(value);
        sift_up(heap_.size() - 1);  // 上浮维护堆性质
    }
    
    int top() const {
        return heap_.empty() ? -1 : heap_[0];
    }
    
    void pop() {
        if (heap_.empty()) return;
        
        heap_[0] = heap_.back();  // 将最后一个元素移到堆顶
        heap_.pop_back();
        if (!heap_.empty()) {
            sift_down(0);  // 下沉维护堆性质
        }
    }
    
private:
    std::vector<int> heap_;
    
    void sift_up(size_t index) {
        while (index > 0) {
            size_t parent = (index - 1) / 2;
            if (heap_[index] >= heap_[parent]) break;
            
            std::swap(heap_[index], heap_[parent]);
            index = parent;
        }
    }
    
    void sift_down(size_t index) {
        size_t size = heap_.size();
        
        while (true) {
            size_t smallest = index;
            size_t left = 2 * index + 1;
            size_t right = 2 * index + 2;
            
            if (left < size && heap_[left] < heap_[smallest]) {
                smallest = left;
            }
            
            if (right < size && heap_[right] < heap_[smallest]) {
                smallest = right;
            }
            
            if (smallest == index) break;
            
            std::swap(heap_[index], heap_[smallest]);
            index = smallest;
        }
    }
};
```

#### 3.1.2 为什么定时器用堆？

**时间复杂度比较**
```
定时器操作的复杂度对比：

数据结构     插入    删除    获取最小
─────────────────────────────────────
链表        O(n)    O(n)    O(n)
排序数组    O(n)    O(n)    O(1)  
小根堆      O(logn) O(logn) O(1)
```

**定时器堆的实际应用**
```cpp
struct TimerNode {
    int id;                          // 定时器ID(通常是fd)
    std::chrono::time_point expire;  // 过期时间
    std::function<void()> callback;  // 回调函数
    
    bool operator<(const TimerNode& other) const {
        return expire > other.expire;  // 注意：这里用>构造小根堆
    }
};

class TimerHeap {
public:
    void add_timer(int id, int timeout_ms, std::function<void()> cb) {
        auto expire_time = std::chrono::steady_clock::now() + 
                          std::chrono::milliseconds(timeout_ms);
        
        heap_.emplace_back(id, expire_time, cb);
        sift_up(heap_.size() - 1);
        
        // 维护id到索引的映射，便于快速查找
        id_to_index_[id] = heap_.size() - 1;
    }
    
    void handle_expired_timers() {
        auto now = std::chrono::steady_clock::now();
        
        while (!heap_.empty() && heap_[0].expire <= now) {
            // 执行回调函数
            heap_[0].callback();
            
            // 移除过期定时器
            remove_timer(0);
        }
    }
    
private:
    std::vector<TimerNode> heap_;
    std::unordered_map<int, size_t> id_to_index_;
};
```

### 3.2 队列数据结构

#### 3.2.1 普通队列 vs 双端队列

**std::queue的内部实现**
```cpp
// std::queue实际上是适配器，默认基于deque
#include <queue>
#include <deque>

template<typename T>
class MyQueue {
public:
    void push(const T& value) {
        container_.push_back(value);  // 从尾部插入
    }
    
    void pop() {
        container_.pop_front();       // 从头部删除
    }
    
    T& front() {
        return container_.front();
    }
    
    bool empty() const {
        return container_.empty();
    }
    
private:
    std::deque<T> container_;  // 双端队列作为底层容器
};
```

**为什么阻塞队列用deque？**
```cpp
// deque的优势：
// 1. 两端插入删除都是O(1)
// 2. 内存不需要连续（比vector更灵活）
// 3. 支持随机访问
// 4. 不会因为头部删除而移动所有元素

std::deque<int> dq;
dq.push_back(1);   // 尾部插入 O(1)
dq.push_front(0);  // 头部插入 O(1)  
dq.pop_back();     // 尾部删除 O(1)
dq.pop_front();    // 头部删除 O(1)
```

## 🎯 实战练习汇总

### 练习1：智能指针管理资源
```cpp
// 创建一个文件，实现下面的类
class NetworkConnection {
public:
    NetworkConnection(const std::string& host, int port);
    ~NetworkConnection();
    
    bool send(const std::string& data);
    std::string receive();
    
private:
    int socket_fd_;
    bool connected_;
};

// 使用unique_ptr管理连接
std::unique_ptr<NetworkConnection> create_connection(const std::string& host, int port);
```

### 练习2：线程安全的计数器
```cpp
// 实现一个线程安全的统计器
class Statistics {
public:
    void increment_requests();
    void increment_responses();
    void add_response_time(double time_ms);
    
    void print_stats() const;
    
private:
    // 使用适当的同步原语保护数据
    mutable std::mutex mutex_;
    size_t total_requests_ = 0;
    size_t total_responses_ = 0;
    double total_response_time_ = 0.0;
};
```

### 练习3：简单的Echo服务器
```cpp
// 实现一个回显服务器，客户端发送什么就返回什么
class EchoServer {
public:
    void start(int port);
    
private:
    void handle_client(int client_fd);
};
```

---

## ✅ 下一步行动

1. **理论学习**：仔细阅读上述基础知识，运行所有示例代码
2. **动手实践**：完成3个实战练习
3. **准备环境**：安装必要的开发工具（gcc, make, gdb等）
4. **代码调试**：学会使用gdb调试多线程程序

**主人，这些基础知识是重写WebServer的必备武器！请您务必先掌握这些概念，然后我们就可以开始激动人心的重写之旅了！**

每个知识点我都提供了：
- ✅ **为什么需要** - 理解动机
- ✅ **如何工作** - 理解原理  
- ✅ **实际代码** - 理解实现
- ✅ **实战练习** - 理解应用

您觉得哪个部分需要我详细展开讲解呢？ 