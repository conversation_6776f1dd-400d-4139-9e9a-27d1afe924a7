# 第一周详细学习计划：基础知识准备

> **主人，万丈高楼平地起！第一周我们要打好最坚实的基础，每一个概念都要完全理解并实践！**

## 📅 第一周时间安排

### Day 1: C++智能指针深度掌握

#### 上午任务 (3小时)
**1. 理论学习：智能指针原理**
```cpp
// 创建文件：day1_smart_pointer_basics.cpp
#include <iostream>
#include <memory>
#include <string>

// 任务1：理解普通指针的问题
void demonstrate_raw_pointer_problems() {
    std::cout << "=== 普通指针的问题演示 ===" << std::endl;
    
    // 问题1：忘记delete导致内存泄漏
    int* ptr1 = new int(42);
    std::cout << "分配了内存，值为：" << *ptr1 << std::endl;
    // 忘记 delete ptr1; // 内存泄漏！
    
    // 问题2：异常导致delete不被执行
    try {
        int* ptr2 = new int(100);
        throw std::runtime_error("发生异常！");
        delete ptr2; // 这行永远不会执行
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
    }
    
    // 问题3：double delete
    int* ptr3 = new int(200);
    delete ptr3;
    // delete ptr3; // 错误！重复删除
}

// 任务2：智能指针解决方案
void demonstrate_smart_pointer_solutions() {
    std::cout << "\n=== 智能指针解决方案 ===" << std::endl;
    
    // 解决方案1：unique_ptr自动管理内存
    {
        std::unique_ptr<int> ptr1 = std::make_unique<int>(42);
        std::cout << "unique_ptr值：" << *ptr1 << std::endl;
    } // 这里自动调用delete，无内存泄漏
    
    // 解决方案2：异常安全
    try {
        std::unique_ptr<int> ptr2 = std::make_unique<int>(100);
        throw std::runtime_error("发生异常！");
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
        std::cout << "但是内存已经自动释放了！" << std::endl;
    }
    
    // 解决方案3：不可能double delete
    std::unique_ptr<int> ptr3 = std::make_unique<int>(200);
    // 智能指针析构时自动delete，不需要手动操作
}

int main() {
    demonstrate_raw_pointer_problems();
    demonstrate_smart_pointer_solutions();
    return 0;
}
```

**2. 实战练习：资源管理类**
```cpp
// 创建文件：day1_resource_manager.cpp
#include <iostream>
#include <memory>
#include <fstream>
#include <string>

// 练习1：文件资源的RAII管理
class FileManager {
public:
    FileManager(const std::string& filename) : filename_(filename) {
        file_ = std::make_unique<std::ifstream>(filename);
        if (!file_->is_open()) {
            throw std::runtime_error("无法打开文件: " + filename);
        }
        std::cout << "文件 " << filename << " 成功打开" << std::endl;
    }
    
    ~FileManager() {
        std::cout << "文件 " << filename_ << " 自动关闭" << std::endl;
    }
    
    std::string read_line() {
        std::string line;
        if (file_ && std::getline(*file_, line)) {
            return line;
        }
        return "";
    }
    
    bool eof() const {
        return file_->eof();
    }
    
private:
    std::string filename_;
    std::unique_ptr<std::ifstream> file_;
};

// 练习2：网络连接的智能指针管理
class NetworkConnection {
public:
    NetworkConnection(const std::string& host, int port) 
        : host_(host), port_(port), connected_(false) {
        // 模拟网络连接
        std::cout << "连接到 " << host << ":" << port << std::endl;
        connected_ = true;
    }
    
    ~NetworkConnection() {
        if (connected_) {
            std::cout << "断开与 " << host_ << ":" << port_ << " 的连接" << std::endl;
        }
    }
    
    void send(const std::string& data) {
        if (connected_) {
            std::cout << "发送数据: " << data << std::endl;
        }
    }
    
private:
    std::string host_;
    int port_;
    bool connected_;
};

// 使用智能指针管理网络连接
std::unique_ptr<NetworkConnection> create_connection(const std::string& host, int port) {
    return std::make_unique<NetworkConnection>(host, port);
}

int main() {
    // 测试文件管理
    try {
        // 创建一个测试文件
        {
            std::ofstream test_file("test.txt");
            test_file << "Hello World\n";
            test_file << "Smart Pointer Test\n";
        }
        
        FileManager fm("test.txt");
        while (!fm.eof()) {
            std::string line = fm.read_line();
            if (!line.empty()) {
                std::cout << "读取行: " << line << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cout << "错误: " << e.what() << std::endl;
    }
    
    // 测试网络连接管理
    {
        auto conn = create_connection("localhost", 8080);
        conn->send("Hello Server!");
    } // 连接自动断开
    
    return 0;
}
```

#### 下午任务 (3小时)
**3. 深入学习：移动语义与完美转发**
```cpp
// 创建文件：day1_move_semantics.cpp
#include <iostream>
#include <memory>
#include <vector>
#include <string>

// 任务3：理解移动语义
class Resource {
public:
    Resource(const std::string& name) : name_(name), data_(new char[1000]) {
        std::cout << "创建资源: " << name_ << std::endl;
    }
    
    // 拷贝构造函数（昂贵的操作）
    Resource(const Resource& other) : name_(other.name_ + "_copy"), data_(new char[1000]) {
        std::cout << "拷贝资源: " << name_ << std::endl;
        // 拷贝数据...
    }
    
    // 移动构造函数（高效的操作）
    Resource(Resource&& other) noexcept : name_(std::move(other.name_)), data_(other.data_) {
        other.data_ = nullptr;  // 转移所有权
        std::cout << "移动资源: " << name_ << std::endl;
    }
    
    ~Resource() {
        if (data_) {
            delete[] data_;
            std::cout << "销毁资源: " << name_ << std::endl;
        }
    }
    
private:
    std::string name_;
    char* data_;
};

void test_move_semantics() {
    std::cout << "=== 移动语义测试 ===" << std::endl;
    
    std::vector<Resource> resources;
    
    // 直接构造，不会有拷贝
    resources.emplace_back("Resource1");
    
    // 移动语义，高效
    Resource temp("TempResource");
    resources.push_back(std::move(temp));  // 移动而不是拷贝
    
    std::cout << "vector中现在有 " << resources.size() << " 个资源" << std::endl;
}

int main() {
    test_move_semantics();
    return 0;
}
```

**4. 晚上复习与总结**
- 整理今天学到的知识点
- 写一份智能指针使用总结
- 思考在WebServer中哪些地方会用到智能指针

### Day 2: RAII机制与异常安全

#### 上午任务 (3小时)
**1. RAII原理深入理解**
```cpp
// 创建文件：day2_raii_principles.cpp
#include <iostream>
#include <mutex>
#include <fstream>
#include <chrono>

// 任务1：实现一个计时器类
class Timer {
public:
    Timer(const std::string& operation_name) : name_(operation_name) {
        start_time_ = std::chrono::high_resolution_clock::now();
        std::cout << "开始计时: " << name_ << std::endl;
    }
    
    ~Timer() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time_).count();
        std::cout << name_ << " 耗时: " << duration << "ms" << std::endl;
    }
    
private:
    std::string name_;
    std::chrono::time_point<std::chrono::high_resolution_clock> start_time_;
};

// 任务2：实现一个自动锁类
class AutoLock {
public:
    explicit AutoLock(std::mutex& mutex) : mutex_(mutex) {
        mutex_.lock();
        std::cout << "获取锁" << std::endl;
    }
    
    ~AutoLock() {
        mutex_.unlock();
        std::cout << "释放锁" << std::endl;
    }
    
private:
    std::mutex& mutex_;
};

// 任务3：实现一个内存池资源管理器
class MemoryPool {
public:
    MemoryPool(size_t block_size, size_t block_count) 
        : block_size_(block_size), block_count_(block_count) {
        pool_ = new char[block_size * block_count];
        std::cout << "创建内存池: " << block_count << " 块，每块 " << block_size << " 字节" << std::endl;
    }
    
    ~MemoryPool() {
        delete[] pool_;
        std::cout << "销毁内存池" << std::endl;
    }
    
    void* allocate() {
        // 简化的分配逻辑
        static size_t allocated = 0;
        if (allocated < block_count_) {
            return pool_ + (allocated++ * block_size_);
        }
        return nullptr;
    }
    
private:
    char* pool_;
    size_t block_size_;
    size_t block_count_;
};

// 测试RAII
void test_raii_timer() {
    Timer timer("文件处理操作");
    
    // 模拟一些工作
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Timer析构时自动打印耗时
}

std::mutex global_mutex;

void test_raii_lock() {
    std::cout << "\n=== 测试自动锁 ===" << std::endl;
    
    {
        AutoLock lock(global_mutex);
        std::cout << "在临界区中工作..." << std::endl;
        // 即使这里发生异常，锁也会被自动释放
    } // 锁在这里自动释放
    
    std::cout << "锁已释放，可以继续其他工作" << std::endl;
}

int main() {
    test_raii_timer();
    test_raii_lock();
    
    // 测试内存池
    {
        MemoryPool pool(1024, 10);
        void* ptr1 = pool.allocate();
        void* ptr2 = pool.allocate();
        std::cout << "分配了两个内存块" << std::endl;
    } // 内存池自动销毁
    
    return 0;
}
```

#### 下午任务 (3小时)
**2. 异常安全编程**
```cpp
// 创建文件：day2_exception_safety.cpp
#include <iostream>
#include <memory>
#include <vector>
#include <stdexcept>

// 任务1：实现异常安全的类
class SafeContainer {
public:
    SafeContainer() {
        std::cout << "创建SafeContainer" << std::endl;
    }
    
    ~SafeContainer() {
        std::cout << "销毁SafeContainer" << std::endl;
    }
    
    // 强异常安全：要么成功，要么保持原状
    void add_item(const std::string& item) {
        // 使用临时变量，确保异常安全
        auto new_items = items_;  // 拷贝当前状态
        new_items.push_back(item);  // 可能抛出异常
        
        // 只有成功了才更新状态
        items_ = std::move(new_items);
        std::cout << "成功添加项目: " << item << std::endl;
    }
    
    void print_items() const {
        std::cout << "容器中的项目: ";
        for (const auto& item : items_) {
            std::cout << item << " ";
        }
        std::cout << std::endl;
    }
    
private:
    std::vector<std::string> items_;
};

// 任务2：RAII与异常安全的结合
class DatabaseTransaction {
public:
    DatabaseTransaction() : committed_(false) {
        std::cout << "开始数据库事务" << std::endl;
    }
    
    ~DatabaseTransaction() {
        if (!committed_) {
            std::cout << "回滚事务（因为未提交）" << std::endl;
        }
    }
    
    void execute_sql(const std::string& sql) {
        std::cout << "执行SQL: " << sql << std::endl;
        // 模拟可能失败的操作
        if (sql.find("INVALID") != std::string::npos) {
            throw std::runtime_error("SQL执行失败");
        }
    }
    
    void commit() {
        committed_ = true;
        std::cout << "提交事务" << std::endl;
    }
    
private:
    bool committed_;
};

void test_exception_safety() {
    std::cout << "=== 测试异常安全 ===" << std::endl;
    
    SafeContainer container;
    
    try {
        container.add_item("item1");
        container.add_item("item2");
        container.print_items();
        
        // 模拟异常情况
        container.add_item("item3");
        
    } catch (const std::exception& e) {
        std::cout << "捕获异常: " << e.what() << std::endl;
        container.print_items();  // 容器状态应该保持一致
    }
}

void test_database_transaction() {
    std::cout << "\n=== 测试数据库事务 ===" << std::endl;
    
    // 成功的事务
    try {
        DatabaseTransaction trans;
        trans.execute_sql("INSERT INTO users VALUES (1, 'Alice')");
        trans.execute_sql("UPDATE users SET name='Bob' WHERE id=1");
        trans.commit();
    } catch (const std::exception& e) {
        std::cout << "事务失败: " << e.what() << std::endl;
    }
    
    // 失败的事务
    try {
        DatabaseTransaction trans;
        trans.execute_sql("INSERT INTO users VALUES (2, 'Charlie')");
        trans.execute_sql("INVALID SQL");  // 这会导致异常
        trans.commit();  // 不会执行到这里
    } catch (const std::exception& e) {
        std::cout << "事务失败: " << e.what() << std::endl;
        // 析构函数会自动回滚
    }
}

int main() {
    test_exception_safety();
    test_database_transaction();
    return 0;
}
```

### Day 3: Lambda表达式与函数式编程

#### 全天任务 (6小时)
```cpp
// 创建文件：day3_lambda_expressions.cpp
#include <iostream>
#include <vector>
#include <algorithm>
#include <functional>
#include <memory>

// 任务1：Lambda基础语法
void lambda_basics() {
    std::cout << "=== Lambda基础 ===" << std::endl;
    
    // 最简单的lambda
    auto hello = []() {
        std::cout << "Hello from lambda!" << std::endl;
    };
    hello();
    
    // 带参数的lambda
    auto add = [](int a, int b) -> int {
        return a + b;
    };
    std::cout << "3 + 5 = " << add(3, 5) << std::endl;
    
    // 捕获外部变量
    int x = 10, y = 20;
    
    // 按值捕获
    auto capture_by_value = [x, y](int z) {
        return x + y + z;  // x和y是副本
    };
    
    // 按引用捕获
    auto capture_by_ref = [&x, &y](int z) {
        x += z;  // 直接修改原变量
        return x + y;
    };
    
    std::cout << "按值捕获结果: " << capture_by_value(5) << std::endl;
    std::cout << "修改前 x = " << x << std::endl;
    std::cout << "按引用捕获结果: " << capture_by_ref(5) << std::endl;
    std::cout << "修改后 x = " << x << std::endl;
}

// 任务2：Lambda在STL算法中的应用
void lambda_with_stl() {
    std::cout << "\n=== Lambda与STL ===" << std::endl;
    
    std::vector<int> numbers = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    
    // 使用lambda作为谓词
    auto even_count = std::count_if(numbers.begin(), numbers.end(), 
        [](int n) { return n % 2 == 0; });
    std::cout << "偶数个数: " << even_count << std::endl;
    
    // 使用lambda进行变换
    std::vector<int> squares;
    std::transform(numbers.begin(), numbers.end(), std::back_inserter(squares),
        [](int n) { return n * n; });
    
    std::cout << "平方数: ";
    std::for_each(squares.begin(), squares.end(),
        [](int n) { std::cout << n << " "; });
    std::cout << std::endl;
    
    // 使用lambda进行排序
    std::vector<std::string> words = {"apple", "banana", "cherry", "date"};
    std::sort(words.begin(), words.end(),
        [](const std::string& a, const std::string& b) {
            return a.length() < b.length();  // 按长度排序
        });
    
    std::cout << "按长度排序的单词: ";
    for (const auto& word : words) {
        std::cout << word << " ";
    }
    std::cout << std::endl;
}

// 任务3：高级Lambda应用 - 回调函数
class EventHandler {
public:
    using EventCallback = std::function<void(const std::string&)>;
    
    void set_callback(EventCallback callback) {
        callback_ = callback;
    }
    
    void trigger_event(const std::string& event_data) {
        if (callback_) {
            callback_(event_data);
        }
    }
    
private:
    EventCallback callback_;
};

void lambda_callbacks() {
    std::cout << "\n=== Lambda回调函数 ===" << std::endl;
    
    EventHandler handler;
    
    // 设置lambda作为回调函数
    handler.set_callback([](const std::string& data) {
        std::cout << "处理事件: " << data << std::endl;
    });
    
    handler.trigger_event("用户登录");
    handler.trigger_event("文件上传");
    
    // 更复杂的回调，捕获外部状态
    int event_count = 0;
    handler.set_callback([&event_count](const std::string& data) {
        ++event_count;
        std::cout << "第 " << event_count << " 个事件: " << data << std::endl;
    });
    
    handler.trigger_event("数据库连接");
    handler.trigger_event("网络请求");
}

// 任务4：模拟WebServer中的Lambda应用
class SimpleThreadPool {
public:
    using Task = std::function<void()>;
    
    void enqueue(Task task) {
        std::cout << "任务已加入队列" << std::endl;
        // 简化实现，直接执行
        task();
    }
};

class SimpleTimer {
public:
    using TimeoutCallback = std::function<void()>;
    
    void add_timer(int id, int timeout_ms, TimeoutCallback callback) {
        std::cout << "添加定时器 " << id << "，超时时间 " << timeout_ms << "ms" << std::endl;
        // 简化实现，直接调用回调
        callback();
    }
};

void webserver_lambda_usage() {
    std::cout << "\n=== WebServer中的Lambda应用 ===" << std::endl;
    
    SimpleThreadPool thread_pool;
    SimpleTimer timer;
    
    int client_fd = 42;
    std::string client_data = "HTTP GET request";
    
    // 线程池任务使用lambda
    thread_pool.enqueue([client_fd, client_data]() {
        std::cout << "在工作线程中处理客户端 " << client_fd 
                  << " 的请求: " << client_data << std::endl;
    });
    
    // 定时器回调使用lambda
    timer.add_timer(client_fd, 5000, [client_fd]() {
        std::cout << "客户端 " << client_fd << " 连接超时，关闭连接" << std::endl;
    });
    
    // 错误处理回调
    auto error_handler = [](const std::string& error_msg) {
        std::cout << "错误处理: " << error_msg << std::endl;
    };
    
    error_handler("连接重置");
    error_handler("缓冲区溢出");
}

int main() {
    lambda_basics();
    lambda_with_stl();
    lambda_callbacks();
    webserver_lambda_usage();
    return 0;
}
```

### Day 4: STL容器深度掌握

#### 上午：vector和string (3小时)
```cpp
// 创建文件：day4_vector_string.cpp
#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
#include <chrono>

// 任务1：深入理解vector的内存管理
void vector_memory_management() {
    std::cout << "=== Vector内存管理 ===" << std::endl;
    
    std::vector<int> vec;
    std::cout << "初始容量: " << vec.capacity() << "，大小: " << vec.size() << std::endl;
    
    // 观察容量增长
    for (int i = 0; i < 20; ++i) {
        size_t old_capacity = vec.capacity();
        vec.push_back(i);
        
        if (vec.capacity() != old_capacity) {
            std::cout << "容量变化: " << old_capacity << " -> " << vec.capacity() 
                      << "，大小: " << vec.size() << std::endl;
        }
    }
    
    // 预分配内存的重要性
    std::vector<int> vec1, vec2;
    
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < 100000; ++i) {
        vec1.push_back(i);  // 多次重新分配
    }
    auto end = std::chrono::high_resolution_clock::now();
    auto duration1 = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    start = std::chrono::high_resolution_clock::now();
    vec2.reserve(100000);  // 预分配
    for (int i = 0; i < 100000; ++i) {
        vec2.push_back(i);
    }
    end = std::chrono::high_resolution_clock::now();
    auto duration2 = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "未预分配耗时: " << duration1.count() << " 微秒" << std::endl;
    std::cout << "预分配耗时: " << duration2.count() << " 微秒" << std::endl;
    std::cout << "性能提升: " << (double)duration1.count() / duration2.count() << " 倍" << std::endl;
}

// 任务2：模拟Buffer类的设计
class SimpleBuffer {
public:
    SimpleBuffer(size_t initial_size = 1024) {
        buffer_.reserve(initial_size);
        read_index_ = 0;
        write_index_ = 0;
    }
    
    void append(const std::string& data) {
        // 确保有足够空间
        ensure_writable_bytes(data.size());
        
        // 复制数据
        buffer_.insert(buffer_.begin() + write_index_, data.begin(), data.end());
        write_index_ += data.size();
    }
    
    std::string retrieve(size_t len) {
        len = std::min(len, readable_bytes());
        std::string result(buffer_.begin() + read_index_, 
                          buffer_.begin() + read_index_ + len);
        read_index_ += len;
        return result;
    }
    
    size_t readable_bytes() const {
        return write_index_ - read_index_;
    }
    
    size_t writable_bytes() const {
        return buffer_.capacity() - write_index_;
    }
    
    void print_status() const {
        std::cout << "Buffer状态 - 容量: " << buffer_.capacity()
                  << "，可读: " << readable_bytes()
                  << "，可写: " << writable_bytes() << std::endl;
    }
    
private:
    void ensure_writable_bytes(size_t len) {
        if (writable_bytes() < len) {
            // 扩容策略
            size_t new_capacity = buffer_.capacity();
            while (new_capacity - write_index_ < len) {
                new_capacity *= 2;
            }
            buffer_.reserve(new_capacity);
            buffer_.resize(write_index_);  // 调整大小到当前写位置
        }
    }
    
    std::vector<char> buffer_;
    size_t read_index_;
    size_t write_index_;
};

void test_simple_buffer() {
    std::cout << "\n=== 测试SimpleBuffer ===" << std::endl;
    
    SimpleBuffer buffer(16);
    buffer.print_status();
    
    buffer.append("Hello");
    buffer.print_status();
    
    buffer.append(" World");
    buffer.print_status();
    
    std::string data = buffer.retrieve(5);
    std::cout << "读取数据: " << data << std::endl;
    buffer.print_status();
    
    buffer.append("! This is a longer string that will cause expansion.");
    buffer.print_status();
}

int main() {
    vector_memory_management();
    test_simple_buffer();
    return 0;
}
```

#### 下午：unordered_map和容器选择 (3小时)
```cpp
// 创建文件：day4_containers_comparison.cpp
#include <iostream>
#include <unordered_map>
#include <map>
#include <vector>
#include <chrono>
#include <random>

// 任务1：对比不同容器的性能
class PerformanceTest {
public:
    static void compare_map_performance() {
        std::cout << "=== Map性能对比 ===" << std::endl;
        
        const int N = 100000;
        std::vector<int> keys;
        
        // 生成随机键
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1, N * 10);
        
        for (int i = 0; i < N; ++i) {
            keys.push_back(dis(gen));
        }
        
        // 测试unordered_map插入
        auto start = std::chrono::high_resolution_clock::now();
        std::unordered_map<int, std::string> hash_map;
        for (int key : keys) {
            hash_map[key] = "value_" + std::to_string(key);
        }
        auto end = std::chrono::high_resolution_clock::now();
        auto hash_insert_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        // 测试map插入
        start = std::chrono::high_resolution_clock::now();
        std::map<int, std::string> tree_map;
        for (int key : keys) {
            tree_map[key] = "value_" + std::to_string(key);
        }
        end = std::chrono::high_resolution_clock::now();
        auto tree_insert_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        // 测试查找性能
        start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 10000; ++i) {
            hash_map.find(keys[i % keys.size()]);
        }
        end = std::chrono::high_resolution_clock::now();
        auto hash_find_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 10000; ++i) {
            tree_map.find(keys[i % keys.size()]);
        }
        end = std::chrono::high_resolution_clock::now();
        auto tree_find_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "插入 " << N << " 个元素:" << std::endl;
        std::cout << "unordered_map: " << hash_insert_time.count() << " 微秒" << std::endl;
        std::cout << "map: " << tree_insert_time.count() << " 微秒" << std::endl;
        
        std::cout << "查找 10000 次:" << std::endl;
        std::cout << "unordered_map: " << hash_find_time.count() << " 微秒" << std::endl;
        std::cout << "map: " << tree_find_time.count() << " 微秒" << std::endl;
    }
};

// 任务2：模拟WebServer中的连接管理
class ConnectionManager {
public:
    struct Connection {
        int fd;
        std::string client_ip;
        uint16_t client_port;
        std::chrono::time_point<std::chrono::steady_clock> last_active;
        
        Connection(int f, const std::string& ip, uint16_t port) 
            : fd(f), client_ip(ip), client_port(port), 
              last_active(std::chrono::steady_clock::now()) {}
    };
    
    void add_connection(int fd, const std::string& ip, uint16_t port) {
        connections_[fd] = std::make_unique<Connection>(fd, ip, port);
        std::cout << "添加连接 " << fd << " (" << ip << ":" << port << ")" << std::endl;
    }
    
    void remove_connection(int fd) {
        auto it = connections_.find(fd);
        if (it != connections_.end()) {
            std::cout << "移除连接 " << fd << std::endl;
            connections_.erase(it);
        }
    }
    
    Connection* get_connection(int fd) {
        auto it = connections_.find(fd);
        return (it != connections_.end()) ? it->second.get() : nullptr;
    }
    
    void update_last_active(int fd) {
        auto conn = get_connection(fd);
        if (conn) {
            conn->last_active = std::chrono::steady_clock::now();
        }
    }
    
    std::vector<int> get_timeout_connections(int timeout_seconds) {
        std::vector<int> timeout_fds;
        auto now = std::chrono::steady_clock::now();
        
        for (const auto& pair : connections_) {
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                now - pair.second->last_active);
            
            if (duration.count() > timeout_seconds) {
                timeout_fds.push_back(pair.first);
            }
        }
        
        return timeout_fds;
    }
    
    void print_statistics() const {
        std::cout << "当前连接数: " << connections_.size() << std::endl;
        for (const auto& pair : connections_) {
            const auto& conn = pair.second;
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                now - conn->last_active);
            
            std::cout << "  FD " << conn->fd << ": " << conn->client_ip 
                      << ":" << conn->client_port 
                      << " (空闲 " << duration.count() << "s)" << std::endl;
        }
    }
    
private:
    // 为什么用unordered_map？
    // 1. 需要O(1)的查找性能
    // 2. 连接的fd是唯一的，适合做key
    // 3. 不需要排序，只需要快速访问
    std::unordered_map<int, std::unique_ptr<Connection>> connections_;
};

void test_connection_manager() {
    std::cout << "\n=== 测试连接管理器 ===" << std::endl;
    
    ConnectionManager manager;
    
    // 添加一些连接
    manager.add_connection(3, "*************", 12345);
    manager.add_connection(4, "*************", 12346);
    manager.add_connection(5, "*************", 12347);
    
    manager.print_statistics();
    
    // 模拟活动
    std::this_thread::sleep_for(std::chrono::seconds(1));
    manager.update_last_active(4);  // 只有fd=4保持活跃
    
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // 检查超时连接
    auto timeout_fds = manager.get_timeout_connections(1);  // 1秒超时
    std::cout << "超时连接: ";
    for (int fd : timeout_fds) {
        std::cout << fd << " ";
    }
    std::cout << std::endl;
    
    // 移除超时连接
    for (int fd : timeout_fds) {
        manager.remove_connection(fd);
    }
    
    manager.print_statistics();
}

int main() {
    PerformanceTest::compare_map_performance();
    test_connection_manager();
    return 0;
}
```

### Day 5-7: 多线程编程实战

由于内容太多，我先给出第一周的前4天详细计划。主人，这样的详细程度如何？每天都有：

1. **明确的学习目标**
2. **详细的代码示例**
3. **实战练习项目**
4. **与WebServer项目的关联**

您希望我继续完善剩下几天的计划，还是先开始第一天的学习呢？我可以根据您的进度随时调整教学内容的深度和广度！ 