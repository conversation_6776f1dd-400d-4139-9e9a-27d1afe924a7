/*
 * <AUTHOR> mark
 * @Date         : 2020-06-15
 * @copyleft Apache 2.0
 * 
 * HttpConn HTTP连接管理类
 * 功能：封装单个HTTP连接的完整生命周期管理
 * 核心特性：
 * 1. 集成HTTP请求解析和响应生成
 * 2. 高效的缓冲区管理
 * 3. 支持Keep-Alive长连接
 * 4. 零拷贝数据传输优化
 * 5. 线程安全的连接计数
 */ 

#ifndef HTTP_CONN_H
#define HTTP_CONN_H

// 系统调用相关头文件
#include <sys/types.h>   // 系统数据类型定义
#include <sys/uio.h>     // 向量IO操作：readv/writev
#include <arpa/inet.h>   // 网络地址转换：sockaddr_in等
#include <stdlib.h>      // 标准库函数：atoi()等
#include <errno.h>       // 错误码定义

// 项目内部模块
#include "../log/log.h"              // 日志系统
#include "../pool/sqlconnRAII.h"    // 数据库连接RAII管理
#include "../buffer/buffer.h"       // 高效缓冲区
#include "httprequest.h"            // HTTP请求解析
#include "httpresponse.h"           // HTTP响应生成

/**
 * @class HttpConn
 * @brief HTTP连接管理类
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                    HttpConn                             │
 * │                                                         │
 * │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │
 * │  │   网络层     │    │   协议层     │    │   应用层     │ │
 * │  │             │    │             │    │             │ │
 * │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │ │
 * │  │ │Socket   │ │◄──►│ │HTTP     │ │◄──►│ │Business │ │ │
 * │  │ │Read/    │ │    │ │Request/ │ │    │ │Logic    │ │ │
 * │  │ │Write    │ │    │ │Response │ │    │ │         │ │ │
 * │  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │ │
 * │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │ │
 * │  │ │Buffer   │ │    │ │Parser   │ │    │ │Database │ │ │
 * │  │ │Manager  │ │    │ │State    │ │    │ │Access   │ │ │
 * │  │ │         │ │    │ │Machine  │ │    │ │         │ │ │
 * │  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │ │
 * │  └─────────────┘    └─────────────┘    └─────────────┘ │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 生命周期管理：
 * 1. init()     - 初始化连接，设置socket和地址信息
 * 2. read()     - 读取客户端数据到缓冲区
 * 3. process()  - 解析HTTP请求，生成响应
 * 4. write()    - 发送响应数据给客户端
 * 5. Close()    - 关闭连接，清理资源
 */
class HttpConn {
public:
    /**
     * @brief 默认构造函数
     * 初始化HTTP连接对象的基本状态
     */
    HttpConn();

    /**
     * @brief 析构函数
     * 清理连接资源，更新连接计数
     */
    ~HttpConn();

    // ==================== 连接管理接口 ====================

    /**
     * @brief 初始化HTTP连接
     * @param sockFd 客户端socket文件描述符
     * @param addr 客户端地址信息
     * 
     * 功能：
     * 1. 保存连接的基本信息
     * 2. 重置缓冲区状态
     * 3. 更新全局连接计数
     */
    void init(int sockFd, const sockaddr_in& addr);

    /**
     * @brief 从socket读取数据到缓冲区
     * @param saveErrno 错误码保存指针
     * @return ssize_t 实际读取的字节数，-1表示错误
     * 
     * 技术要点：
     * 1. 使用Buffer的ReadFd()方法高效读取
     * 2. 支持ET模式的一次性读完
     * 3. 错误处理和状态维护
     */
    ssize_t read(int* saveErrno);

    /**
     * @brief 将缓冲区数据写入socket
     * @param saveErrno 错误码保存指针
     * @return ssize_t 实际写入的字节数，-1表示错误
     * 
     * 技术要点：
     * 1. 使用writev()向量写入，支持零拷贝
     * 2. 处理部分写入的情况
     * 3. 动态调整iovec结构
     */
    ssize_t write(int* saveErrno);

    /**
     * @brief 关闭HTTP连接
     * 
     * 功能：
     * 1. 关闭socket文件描述符
     * 2. 重置连接状态
     * 3. 更新连接计数
     */
    void Close();

    // ==================== 状态查询接口 ====================

    /**
     * @brief 获取socket文件描述符
     * @return int 文件描述符
     */
    int GetFd() const;

    /**
     * @brief 获取客户端端口号
     * @return int 端口号
     */
    int GetPort() const;

    /**
     * @brief 获取客户端IP地址
     * @return const char* IP地址字符串
     */
    const char* GetIP() const;
    
    /**
     * @brief 获取客户端地址结构
     * @return sockaddr_in 地址结构
     */
    sockaddr_in GetAddr() const;

    // ==================== 业务处理接口 ====================
    
    /**
     * @brief 处理HTTP请求（核心业务逻辑）
     * @return bool 处理是否成功
     * 
     * 处理流程：
     * 1. 解析HTTP请求（请求行、请求头、请求体）
     * 2. 根据请求类型执行相应的业务逻辑
     * 3. 生成HTTP响应（状态行、响应头、响应体）
     * 4. 准备发送数据
     */
    bool process();

    /**
     * @brief 获取待写入的总字节数
     * @return int 待写入字节数
     * 
     * 计算iovec中所有数据块的总大小
     * 用于判断是否还有数据需要发送
     */
    int ToWriteBytes() { 
        return iov_[0].iov_len + iov_[1].iov_len; 
    }

    /**
     * @brief 检查是否为Keep-Alive连接
     * @return bool true表示保持连接，false表示关闭连接
     * 
     * Keep-Alive机制：
     * 1. HTTP/1.1默认启用Keep-Alive
     * 2. 允许在同一连接上发送多个请求
     * 3. 减少连接建立和关闭的开销
     */
    bool IsKeepAlive() const {
        return request_.IsKeepAlive();
    }

    // ==================== 静态配置和统计 ====================

    /**
     * @brief 是否使用ET（边缘触发）模式
     * 静态变量，全局配置epoll的触发模式
     */
    static bool isET;
    
    /**
     * @brief 静态资源目录路径
     * 指向Web服务器的静态文件根目录
     */
    static const char* srcDir;
    
    /**
     * @brief 当前活跃连接数
     * 原子变量，线程安全的连接计数器
     * 用于监控服务器负载和连接管理
     */
    static std::atomic<int> userCount;
    
private:
    // ==================== 连接基本信息 ====================
   
    int fd_;                    // socket文件描述符
    struct sockaddr_in addr_;   // 客户端地址信息
    bool isClose_;              // 连接关闭标志

    // ==================== 数据传输相关 ====================
    
    int iovCnt_;                // iovec数组中有效元素个数
    struct iovec iov_[2];       // 向量IO结构数组
                                // iov_[0]: 响应头缓冲区
                                // iov_[1]: 响应体（通常是文件内容）
    
    // ==================== 缓冲区管理 ====================
    
    Buffer readBuff_;           // 读缓冲区：存储从客户端接收的数据
    Buffer writeBuff_;          // 写缓冲区：存储要发送给客户端的数据

    // ==================== HTTP协议处理 ====================
    
    HttpRequest request_;       // HTTP请求解析器
    HttpResponse response_;     // HTTP响应生成器
};

#endif //HTTP_CONN_H