/*
 * <AUTHOR> mark
 * @Date         : 2020-06-17
 * @copyleft Apache 2.0
 * 
 * HeapTimer 小根堆定时器
 * 数据结构：基于小根堆的高效定时器管理
 * 核心特性：
 * 1. O(log n)时间复杂度的插入和删除操作
 * 2. O(1)时间复杂度获取最近超时时间
 * 3. 支持定时器的动态调整
 * 4. 高精度时间处理（毫秒级）
 * 5. 回调函数机制处理超时事件
 */ 

#ifndef HEAP_TIMER_H
#define HEAP_TIMER_H

// 标准库头文件
#include <queue>            // STL队列（这里主要用于类型定义）
#include <unordered_map>    // 哈希表，用于快速查找定时器
#include <time.h>           // 时间处理函数
#include <algorithm>        // 算法库，用于堆操作
#include <arpa/inet.h>      // 网络地址转换（这里可能用于调试）
#include <functional>       // 函数对象包装器
#include <assert.h>         // 断言宏
#include <chrono>           // C++11高精度时间库

// 项目内部模块
#include "../log/log.h"     // 日志系统

// ==================== 类型别名定义 ====================

/**
 * @typedef TimeoutCallBack
 * @brief 超时回调函数类型
 * 
 * 使用std::function包装回调函数，支持：
 * 1. 普通函数指针
 * 2. 成员函数（通过bind）
 * 3. lambda表达式
 * 4. 函数对象
 */
typedef std::function<void()> TimeoutCallBack;

/**
 * @typedef Clock
 * @brief 高精度时钟类型
 * 
 * 使用high_resolution_clock获得最高精度的时间测量
 * 通常基于系统的最高精度时钟实现
 */
typedef std::chrono::high_resolution_clock Clock;

/**
 * @typedef MS
 * @brief 毫秒时间单位
 * 
 * 定时器的时间精度为毫秒级
 */
typedef std::chrono::milliseconds MS;

/**
 * @typedef TimeStamp
 * @brief 时间戳类型
 * 
 * 表示一个具体的时间点
 */
typedef Clock::time_point TimeStamp;

// ==================== 定时器节点结构 ====================

/**
 * @struct TimerNode
 * @brief 定时器节点
 * 
 * 小根堆中的基本元素，包含定时器的所有信息
 */
struct TimerNode {
    int id;                     // 定时器唯一标识符（通常是文件描述符）
    TimeStamp expires;          // 超时时间点
    TimeoutCallBack cb;         // 超时回调函数
    
    /**
     * @brief 比较操作符（小根堆排序依据）
     * @param t 另一个定时器节点
     * @return bool true表示当前节点超时时间更早
     * 
     * 注意：这里的比较逻辑是为了构建小根堆
     * expires越小（时间越早）的节点优先级越高
     */
    bool operator<(const TimerNode& t) {
        return expires < t.expires;
    }
};

// ==================== 小根堆定时器类 ====================

/**
 * @class HeapTimer
 * @brief 基于小根堆的高效定时器管理器
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                   HeapTimer                             │
 * │                                                         │
 * │  ┌─────────────┐              ┌─────────────┐          │
 * │  │   小根堆     │              │   哈希表     │          │
 * │  │             │              │             │          │
 * │  │ ┌─────────┐ │              │ ┌─────────┐ │          │
 * │  │ │ Timer1  │ │◄────────────►│ │ id->idx │ │          │
 * │  │ │(最早)   │ │              │ │ 映射表   │ │          │
 * │  │ └─────────┘ │              │ └─────────┘ │          │
 * │  │ ┌─────────┐ │              └─────────────┘          │
 * │  │ │ Timer2  │ │                                       │
 * │  │ │         │ │              ┌─────────────┐          │
 * │  │ └─────────┘ │              │  回调函数    │          │
 * │  │     ...     │              │             │          │
 * │  │ ┌─────────┐ │              │ ┌─────────┐ │          │
 * │  │ │ TimerN  │ │              │ │ 超时    │ │          │
 * │  │ │(最晚)   │ │              │ │ 处理    │ │          │
 * │  │ └─────────┘ │              │ └─────────┘ │          │
 * │  └─────────────┘              └─────────────┘          │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 核心算法：
 * 1. 插入：O(log n) - 在堆尾添加后上浮调整
 * 2. 删除：O(log n) - 删除后下沉调整
 * 3. 查找最小：O(1) - 堆顶元素
 * 4. 调整：O(log n) - 修改后重新调整堆
 */
class HeapTimer {
public:
    /**
     * @brief 构造函数
     * 
     * 预分配64个元素的空间，避免频繁的内存分配
     * 这是一个经验值，可根据实际需求调整
     */
    HeapTimer() { heap_.reserve(64); }

    /**
     * @brief 析构函数
     * 
     * 清理所有定时器，释放资源
     */
    ~HeapTimer() { clear(); }

    // ==================== 定时器管理接口 ====================
    
    /**
     * @brief 调整定时器的超时时间
     * @param id 定时器ID
     * @param newExpires 新的超时时间（毫秒）
     * 
     * 功能：
     * 1. 查找指定ID的定时器
     * 2. 更新超时时间
     * 3. 重新调整堆结构维持堆性质
     */
    void adjust(int id, int newExpires);

    /**
     * @brief 添加新的定时器
     * @param id 定时器ID（通常是文件描述符）
     * @param timeOut 超时时间（毫秒）
     * @param cb 超时回调函数
     * 
     * 功能：
     * 1. 创建新的定时器节点
     * 2. 插入到堆中并维持堆性质
     * 3. 更新ID到索引的映射关系
     */
    void add(int id, int timeOut, const TimeoutCallBack& cb);

    /**
     * @brief 执行指定定时器的回调函数
     * @param id 定时器ID
     * 
     * 立即执行定时器的回调函数，通常用于手动触发超时处理
     */
    void doWork(int id);

    /**
     * @brief 清空所有定时器
     * 
     * 清理堆和映射表，释放所有资源
     */
    void clear();

    /**
     * @brief 处理所有已超时的定时器
     * 
     * 核心功能：
     * 1. 检查堆顶定时器是否超时
     * 2. 执行超时定时器的回调函数
     * 3. 从堆中移除已处理的定时器
     * 4. 重复直到没有超时的定时器
     */
    void tick();

    /**
     * @brief 移除堆顶定时器
     * 
     * 删除最早超时的定时器，通常在tick()中调用
     */
    void pop();

    /**
     * @brief 获取下一个定时器的超时时间
     * @return int 距离下次超时的毫秒数，-1表示没有定时器
     * 
     * 用于设置epoll_wait的超时时间，实现精确的事件循环
     */
    int GetNextTick();

private:
    // ==================== 私有辅助方法 ====================
    
    /**
     * @brief 删除指定位置的定时器
     * @param i 要删除的定时器在堆中的索引
     * 
     * 算法：
     * 1. 将最后一个元素移到删除位置
     * 2. 根据情况进行上浮或下沉调整
     * 3. 更新映射关系
     */
    void del_(size_t i);
    
    /**
     * @brief 上浮调整（用于插入操作）
     * @param i 要调整的节点索引
     * 
     * 算法：将节点与父节点比较，如果更小则交换，直到满足堆性质
     */
    void siftup_(size_t i);

    /**
     * @brief 下沉调整（用于删除操作）
     * @param index 要调整的节点索引
     * @param n 堆的大小
     * @return bool 是否进行了调整
     * 
     * 算法：将节点与子节点比较，选择最小的子节点交换，直到满足堆性质
     */
    bool siftdown_(size_t index, size_t n);

    /**
     * @brief 交换堆中两个节点
     * @param i 第一个节点索引
     * @param j 第二个节点索引
     * 
     * 同时更新节点内容和映射关系
     */
    void SwapNode_(size_t i, size_t j);

    // ==================== 成员变量 ====================

    /**
     * @brief 小根堆存储
     * 
     * 使用vector实现堆，支持动态扩容
     * 堆顶（索引0）始终是最早超时的定时器
     */
    std::vector<TimerNode> heap_;

    /**
     * @brief ID到堆索引的映射表
     * 
     * key: 定时器ID（通常是文件描述符）
     * value: 该定时器在堆中的索引位置
     * 
     * 作用：
     * 1. 快速查找指定ID的定时器
     * 2. 支持O(1)时间复杂度的定时器查找
     * 3. 在堆调整时同步更新索引关系
     */
    std::unordered_map<int, size_t> ref_;
};

#endif //HEAP_TIMER_H