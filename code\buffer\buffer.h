/*
 * <AUTHOR> mark
 * @Date         : 2020-06-26
 * @copyleft Apache 2.0
 * 
 * Buffer 高效缓冲区类
 * 功能：提供高性能的网络数据缓冲区管理
 * 特点：
 * 1. 自动扩容的动态缓冲区
 * 2. 支持零拷贝的数据读写
 * 3. 线程安全的原子操作
 * 4. 高效的内存管理策略
 */ 

#ifndef BUFFER_H
#define BUFFER_H

// 系统头文件
#include <cstring>   // 字符串操作函数：memcpy, memmove等
#include <iostream>  // 标准输入输出流
#include <unistd.h>  // Unix标准函数：write, read等系统调用
#include <sys/uio.h> // 向量IO操作：readv, writev等
#include <vector>    // STL动态数组容器
#include <atomic>    // C++11原子操作，保证线程安全
#include <assert.h>  // 断言宏，用于调试

/**
 * @class Buffer
 * @brief 高性能网络缓冲区类
 * 
 * 设计思想：
 * 1. 使用vector<char>作为底层存储，自动管理内存
 * 2. 维护读写位置指针，支持高效的数据读写
 * 3. 采用原子操作保证多线程安全
 * 4. 支持零拷贝技术，减少数据拷贝开销
 * 
 * 内存布局：
 * +-------------------+------------------+------------------+
 * | prependable bytes |  readable bytes  |  writable bytes  |
 * |                   |     (CONTENT)    |                  |
 * +-------------------+------------------+------------------+
 * |                   |                  |                  |
 * 0      <=      readerIndex   <=   writerIndex    <=     size
 */
class Buffer {
public:
    /**
     * @brief 构造函数
     * @param initBuffSize 初始缓冲区大小，默认1024字节
     * 
     * 初始化缓冲区，设置合理的初始大小避免频繁扩容
     */
    Buffer(int initBuffSize = 1024);
    
    /**
     * @brief 析构函数
     * 使用默认析构函数，vector会自动释放内存
     */
    ~Buffer() = default;

    // ==================== 状态查询接口 ====================
    
    /**
     * @brief 获取可写字节数
     * @return size_t 缓冲区中可写入的字节数
     */
    size_t WritableBytes() const;
    
    /**
     * @brief 获取可读字节数  
     * @return size_t 缓冲区中可读取的字节数
     */
    size_t ReadableBytes() const;
    
    /**
     * @brief 获取预留字节数
     * @return size_t 读指针前的预留空间大小
     * 
     * 预留空间可用于在数据前添加协议头等信息
     */
    size_t PrependableBytes() const;

    // ==================== 数据访问接口 ====================
    
    /**
     * @brief 获取可读数据的起始指针（只读）
     * @return const char* 指向可读数据开始位置的指针
     * 
     * 用于直接访问缓冲区数据，不移动读指针
     */
    const char* Peek() const;
    
    /**
     * @brief 确保有足够的可写空间
     * @param len 需要的可写字节数
     * 
     * 如果空间不足会自动扩容或整理内存空间
     */
    void EnsureWriteable(size_t len);
    
    /**
     * @brief 标记已写入指定长度的数据
     * @param len 已写入的字节数
     * 
     * 移动写指针，更新缓冲区状态
     */
    void HasWritten(size_t len);

    // ==================== 数据读取接口 ====================
    
    /**
     * @brief 从缓冲区中取出指定长度的数据
     * @param len 要取出的字节数
     * 
     * 移动读指针，释放已读数据的空间
     */
    void Retrieve(size_t len);
    
    /**
     * @brief 取出数据直到指定位置
     * @param end 结束位置指针
     */
    void RetrieveUntil(const char* end);

    /**
     * @brief 清空所有数据
     * 重置读写指针到初始位置
     */
    void RetrieveAll();
    
    /**
     * @brief 取出所有数据并转为字符串
     * @return std::string 包含所有数据的字符串
     */
    std::string RetrieveAllToStr();

    // ==================== 写入位置访问 ====================
    
    /**
     * @brief 获取写入位置指针（只读）
     * @return const char* 指向可写位置的指针
     */
    const char* BeginWriteConst() const;
    
    /**
     * @brief 获取写入位置指针（可写）
     * @return char* 指向可写位置的指针
     */
    char* BeginWrite();

    // ==================== 数据写入接口 ====================
    
    /**
     * @brief 追加字符串数据
     * @param str 要追加的字符串
     */
    void Append(const std::string& str);
    
    /**
     * @brief 追加字符数组数据
     * @param str 字符数组指针
     * @param len 数据长度
     */
    void Append(const char* str, size_t len);
    
    /**
     * @brief 追加任意类型数据
     * @param data 数据指针
     * @param len 数据长度
     */
    void Append(const void* data, size_t len);
    
    /**
     * @brief 追加另一个缓冲区的数据
     * @param buff 源缓冲区
     */
    void Append(const Buffer& buff);

    // ==================== 文件IO接口 ====================
    
    /**
     * @brief 从文件描述符读取数据到缓冲区
     * @param fd 文件描述符
     * @param Errno 错误码指针
     * @return ssize_t 实际读取的字节数，-1表示错误
     * 
     * 使用readv系统调用实现高效读取，支持零拷贝
     */
    ssize_t ReadFd(int fd, int* Errno);
    
    /**
     * @brief 将缓冲区数据写入文件描述符
     * @param fd 文件描述符  
     * @param Errno 错误码指针
     * @return ssize_t 实际写入的字节数，-1表示错误
     */
    ssize_t WriteFd(int fd, int* Errno);

private:
    // ==================== 私有辅助方法 ====================
    
    /**
     * @brief 获取缓冲区起始指针（可写）
     * @return char* 缓冲区起始地址
     */
    char* BeginPtr_();
    
    /**
     * @brief 获取缓冲区起始指针（只读）
     * @return const char* 缓冲区起始地址
     */
    const char* BeginPtr_() const;
    
    /**
     * @brief 确保有足够空间，必要时扩容或整理内存
     * @param len 需要的空间大小
     * 
     * 智能内存管理：
     * 1. 优先通过移动数据来释放空间
     * 2. 空间不足时才进行扩容
     * 3. 避免频繁的内存分配
     */
    void MakeSpace_(size_t len);

    // ==================== 成员变量 ====================
    
    std::vector<char> buffer_;              // 底层存储容器，自动管理内存
    /*
     * 【详细解释：原子变量在缓冲区中的应用】
     * 
     * 1. 基础语法解释：
     *    std::atomic<T> - C++11引入的原子类型模板
     *    - T：被包装的数据类型，这里是std::size_t（无符号整数类型）
     *    - 原子性：保证对该变量的读写操作是不可分割的，要么完全执行，要么完全不执行
     *    - 线程安全：多个线程同时访问时不会出现数据竞争
     * 
     * 2. 为什么要用原子变量？
     *    想象一个例子：两个线程同时操作一个普通的int变量
     *    线程A读取值100，准备加1变成101
     *    线程B也读取值100，准备加2变成102  
     *    最终结果可能是101或102，而不是期望的103
     *    这就是数据竞争！原子变量能避免这个问题
     * 
     * 3. 在本缓冲区中的具体应用：
     *    - readPos_：标记当前读取到的位置（已读数据的末尾）
     *    - writePos_：标记当前写入到的位置（已写数据的末尾）
     *    
     *    缓冲区示意图：
     *    [已读数据][可读数据][可写空间]
     *          ↑         ↑        ↑
     *        readPos_  writePos_  buffer_.size()
     * 
     * 4. 为什么这两个位置需要原子操作？
     *    - 多线程环境下，一个线程可能在读数据（修改readPos_）
     *    - 另一个线程可能在写数据（修改writePos_）
     *    - 还有线程可能在查询缓冲区状态（读取这两个位置）
     *    - 原子操作确保这些操作不会相互干扰，数据始终一致
     * 
     * 5. 性能优势：
     *    - 相比传统的mutex锁，原子操作开销更小
     *    - 避免了线程阻塞，提高并发性能
     *    - 在高频率的读写操作中特别重要
     */
    std::atomic<std::size_t> readPos_;      // 读位置指针，原子操作保证线程安全
    std::atomic<std::size_t> writePos_;     // 写位置指针，原子操作保证线程安全
};

#endif //BUFFER_H