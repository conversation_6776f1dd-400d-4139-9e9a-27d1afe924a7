/*
 * <AUTHOR> mark
 * @Date         : 2020-06-16
 * @copyleft Apache 2.0
 * 
 * SqlConnPool MySQL数据库连接池
 * What (是什么)：
 * 高性能MySQL数据库连接池，采用单例模式确保全局唯一实例，使用对象池模式管理连接资源
 * 
 * Why (为什么需要这两种设计模式)：
 * 【单例模式】- 确保整个应用程序只有一个连接池实例
 *   - 避免多个连接池竞争数据库资源
 *   - 统一管理所有数据库连接
 *   - 节省内存，避免重复创建连接池对象
 * 
 * 【对象池模式】- 预先创建并复用数据库连接对象
 *   - 避免频繁创建/销毁MySQL连接的巨大开销（每次连接需要TCP握手、身份验证等）
 *   - 控制并发连接数量，保护数据库服务器不被过多连接压垮
 *   - 提高响应速度，用户请求可以立即获得可用连接
 * 
 * How (怎么实现)：
 * 【单例模式实现】
 *   - 私有构造函数，防止外部直接创建对象
 *   - 静态Instance()方法返回唯一实例
 *   - 线程安全的懒汉式初始化
 * 
 * 【对象池模式实现】
 *   - 使用std::queue存储空闲的MYSQL*连接对象
 *   - 信号量(semaphore)控制可用连接数量
 *   - 互斥锁(mutex)保护连接队列的线程安全
 *   - GetConn()从池中取出连接，FreeConn()归还连接到池中
 * 核心特性：
 * 1. 预创建数据库连接，避免频繁建立/关闭连接
 * 2. 线程安全的连接管理
 * 3. 信号量控制并发访问
 * 4. 自动连接回收和复用
 * 5. 单例模式确保全局唯一
 */ 

#ifndef SQLCONNPOOL_H
#define SQLCONNPOOL_H

// 系统和第三方库头文件
#include <mysql/mysql.h>  // MySQL C API
#include <string>         // 字符串处理
#include <queue>          // STL队列，存储空闲连接
#include <mutex>          // 互斥锁，保护共享资源
#include <semaphore.h>    // POSIX信号量，控制并发访问
#include <thread>         // C++11线程库

// 项目内部模块
#include "../log/log.h"   // 日志系统

/**
 * @class SqlConnPool
 * @brief MySQL数据库连接池
 * 
 * 设计架构：
 * ┌─────────────────────────────────────────────────────────┐
 * │                  SqlConnPool                            │
 * │                                                         │
 * │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │
 * │  │  业务线程    │    │   连接池     │    │  MySQL服务   │ │
 * │  │             │    │             │    │             │ │
 * │  │ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │ │
 * │  │ │GetConn()│ │───►│ │ Queue   │ │◄──►│ │Database │ │ │
 * │  │ │         │ │    │ │ MYSQL*  │ │    │ │ Server  │ │ │
 * │  │ └─────────┘ │    │ │ Conn1   │ │    │ │         │ │ │
 * │  │ ┌─────────┐ │    │ │ Conn2   │ │    │ └─────────┘ │ │
 * │  │ │FreeConn │ │───►│ │ ...     │ │    └─────────────┘ │
 * │  │ │()       │ │    │ │ ConnN   │ │                    │
 * │  │ └─────────┘ │    │ └─────────┘ │    ┌─────────────┐ │
 * │  └─────────────┘    │ ┌─────────┐ │    │   同步机制   │ │
 * │                     │ │ Mutex   │ │    │             │ │
 * │  ┌─────────────┐    │ │ Lock    │ │    │ ┌─────────┐ │ │
 * │  │   统计信息   │    │ └─────────┘ │    │ │Semaphore│ │ │
 * │  │             │    │ ┌─────────┐ │    │ │ 信号量   │ │ │
 * │  │ ┌─────────┐ │    │ │ Counter │ │    │ │         │ │ │
 * │  │ │useCount │ │    │ │ useCount│ │    │ └─────────┘ │ │
 * │  │ │freeCount│ │    │ │freeCount│ │    └─────────────┘ │
 * │  │ └─────────┘ │    │ └─────────┘ │                    │
 * │  └─────────────┘    └─────────────┘                    │
 * └─────────────────────────────────────────────────────────┘
 * 
 * 工作原理：
 * 1. 初始化时预创建指定数量的MySQL连接
 * 2. 业务线程通过GetConn()获取连接（可能阻塞）
 * 3. 使用完毕后通过FreeConn()归还连接
 * 4. 信号量控制并发访问数量
 * 5. 互斥锁保护连接队列的线程安全
 */
class SqlConnPool {
public:
    // ==================== 单例模式接口 ====================
    
    /**
     * @brief 获取连接池单例实例
     * @return SqlConnPool* 连接池实例指针
     * 
     * 单例模式实现：
     * 1. 线程安全的懒汉式单例
     * 2. 使用局部静态变量保证唯一性
     * 3. C++11保证静态变量初始化的线程安全
     */
    static SqlConnPool *Instance();

    // ==================== 连接管理接口 ====================

    /**
     * @brief 从连接池获取一个数据库连接
     * @return MYSQL* 数据库连接指针，nullptr表示获取失败
     * 
     * 获取流程：
     * 1. 信号量P操作（可能阻塞等待可用连接）
     * 2. 加锁保护临界区
     * 3. 从队列中取出一个连接
     * 4. 更新统计计数
     * 5. 解锁并返回连接
     * 
     * 注意：获取的连接使用完毕后必须调用FreeConn()归还
     */
    MYSQL *GetConn();
    
    /**
     * @brief 归还数据库连接到连接池
     * @param conn 要归还的数据库连接
     * 
     * 归还流程：
     * 1. 检查连接有效性
     * 2. 加锁保护临界区
     * 3. 将连接放回队列
     * 4. 更新统计计数
     * 5. 信号量V操作，通知等待的线程
     * 6. 解锁
     */
    void FreeConn(MYSQL * conn);
    
    /**
     * @brief 获取当前空闲连接数量
     * @return int 空闲连接数
     * 
     * 用于监控连接池状态和负载情况
     */
    int GetFreeConnCount();

    // ==================== 生命周期管理接口 ====================

    /**
     * @brief 初始化连接池
     * @param host 数据库服务器地址
     * @param port 数据库端口号
     * @param user 数据库用户名
     * @param pwd 数据库密码
     * @param dbName 数据库名称
     * @param connSize 连接池大小
     * 
     * 初始化流程：
     * 1. 保存连接参数
     * 2. 创建指定数量的MySQL连接
     * 3. 初始化信号量（初值为连接数）
     * 4. 初始化统计计数器
     * 5. 记录初始化日志
     */
    void Init(const char* host, int port,
              const char* user,const char* pwd, 
              const char* dbName, int connSize);
    
    /**
     * @brief 关闭连接池
     * 
     * 清理流程：
     * 1. 关闭所有数据库连接
     * 2. 清空连接队列
     * 3. 销毁信号量
     * 4. 重置统计计数
     */
    void ClosePool();

private:
    // ==================== 单例模式实现 ====================
    
    /**
     * @brief 私有构造函数（单例模式）
     * 初始化连接池的基本状态
     */
    SqlConnPool();
    
    /**
     * @brief 私有析构函数（单例模式）
     * 确保资源正确释放
     */
    ~SqlConnPool();

    // ==================== 成员变量 ====================

    /**
     * @brief 最大连接数
     * 连接池的容量上限，在Init()时设置
     */
    int MAX_CONN_;
    
    /**
     * @brief 当前使用中的连接数
     * 已被业务线程获取但尚未归还的连接数量
     */
    int useCount_;
    
    /**
     * @brief 当前空闲连接数
     * 在连接池中等待被获取的连接数量
     * 满足：useCount_ + freeCount_ = MAX_CONN_
     */
    int freeCount_;

    /**
     * @brief 连接队列
     * 存储所有空闲的MySQL连接指针
     * 使用队列实现FIFO策略，避免连接长时间不使用
     */
    std::queue<MYSQL *> connQue_;
    
    /**
     * @brief 互斥锁
     * 保护连接队列和统计变量的线程安全
     * 确保多线程环境下的数据一致性
     */
    std::mutex mtx_;
    
    /**
     * @brief 信号量
     * 控制并发访问连接池的线程数量
     * 初值等于连接池大小，实现资源计数
     * 
     * 工作机制：
     * - GetConn()时P操作（减1），无可用连接时阻塞
     * - FreeConn()时V操作（加1），唤醒等待的线程
     */
    sem_t semId_;
};

#endif // SQLCONNPOOL_H